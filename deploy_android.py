import os
from deploy_base import execute, flutter_clean

CMD_BUILD_SANDBOX_APK = "flutter build apk --flavor sandbox"
CMD_UPLOAD_SANDBOX_TO_DROPBOX = "bundle exec fastlane upload_sandbox_to_dropbox"
CMD_BUILD_PRODUCTION_APK = "flutter build apk --flavor production"
CMD_UPLOAD_PRODUCTION_TO_DROPBOX = "bundle exec fastlane upload_production_to_dropbox"


def deploy_production():
    execute(CMD_BUILD_PRODUCTION_APK)
    os.chdir("./android")
    execute(CMD_UPLOAD_PRODUCTION_TO_DROPBOX)
    os.chdir("..")


def deploy_sandbox():
    execute(CMD_BUILD_SANDBOX_APK)
    os.chdir("./android")
    execute(CMD_UPLOAD_SANDBOX_TO_DROPBOX)
    os.chdir("..")


def deploy(with_clean=True):
    if with_clean:
        flutter_clean()
    deploy_sandbox()
    deploy_production()


if __name__ == "__main__":
    deploy()
