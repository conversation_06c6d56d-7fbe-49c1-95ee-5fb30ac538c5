import Appier
import Flutter
import UIKit
import AppTrackingTransparency

// #docregion swift-class
private class PigeonApiImplementation: ExampleHostApi {
  // initialLink
  var initialLink: String? // 加上問號表示 optional，可以是 nil
  func getInitialLink(completion: @escaping (Result<String, Error>) -> Void) {
    // test
    // completion(.success("efshop://m.efshop.com.tw/product/108866"))
    // 如果 initialLink 是空值或空字串就回傳失敗
    // if initialLink == nil || initialLink == "" {
    //   completion(.failure(PigeonError(code: "code", message: "message", details: "details")))
    //   return
    // }
    // 如果有值就回傳，否則失敗
    // if let initialLink = initialLink {
    //   completion(.success(initialLink))
    // } else {
    //   completion(.failure(PigeonError(code: "code", message: "message", details: "details")))
    // }
    // // 使用 guard let 來處理
    guard let initialLink = initialLink else {
      completion(.failure(PigeonError(code: "code", message: "message", details: "details")))
      return
    }
    completion(.success(initialLink))
  }
    
  func getHostLanguage() throws -> String {
    return "Swift"
  }

  func add(_ a: Int64, to b: Int64) throws -> Int64 {
    if a < 0 || b < 0 {
      throw PigeonError(code: "code", message: "message", details: "details")
    }
    return a + b
  }

  func sendMessage(message: MessageData, completion: @escaping (Result<Bool, Error>) -> Void) {
    if message.code == Code.one {
      completion(.failure(PigeonError(code: "code", message: "message", details: "details")))
      return
    }
    completion(.success(true))
  }
}
// #enddocregion swift-class

// #docregion swift-class-flutter
private class PigeonFlutterApi {
  var flutterAPI: MessageFlutterApi

  init(binaryMessenger: FlutterBinaryMessenger) {
    flutterAPI = MessageFlutterApi(binaryMessenger: binaryMessenger)
  }

  func callFlutterMethod(
    aString aStringArg: String?, completion: @escaping (Result<String, Error>) -> Void
  ) {
    flutterAPI.flutterMethod(aString: aStringArg) { result in
      switch result {
      case .success(let value):
        completion(.success(value))
      case .failure(let error):
        completion(.failure(error))
      }
    }
  }
}
// #enddocregion swift-class-flutter


@main
@objc class AppDelegate: FlutterAppDelegate {
  fileprivate let api = PigeonApiImplementation()

  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // Override point for customization after application launch
    GeneratedPluginRegistrant.register(with: self)
    let controller = window?.rootViewController as! FlutterViewController
    // 設定 Flutter 與 Swift 之間的通訊
    ExampleHostApiSetup.setUp(binaryMessenger: controller.binaryMessenger, api: api)
    // Appier
    let QG = QGSdk.getSharedInstance()
    QG.onStart("d7c31d6283d3ccc28bc8", withAppGroup: "group.com.shangching.com.shangching.efshop.notification", setDevProfile: false)
    // #if DEBUG
    //   QG.onStart("d021cfac392ead2d2f1c", withAppGroup: "group.com.shangching.com.shangching.efshop.notification", setDevProfile: true)
    // #else
    //   QG.onStart("d7c31d6283d3ccc28bc8", withAppGroup: "group.com.shangching.com.shangching.efshop.notification", setDevProfile: false)
    // #endif
    // 設定 domain: www.efshop.com.tw, m.efshop.com.tw
    QG.setUniversalLinkDomains(["www.efshop.com.tw", "m.efshop.com.tw"])
    // Registering Push Notification
    if #available(iOS 10.0, *) {
      let center = UNUserNotificationCenter.current()
      center.delegate = self
      center.requestAuthorization(options: [.badge, .carPlay, .alert, .sound]) { granted, error in
        print("Granted: \(granted), Error: \(String(describing: error))")
      }
    } else {
      // Fallback on earlier versions - iOS 8 & 9
      let settings = UIUserNotificationSettings(types: [.alert, .badge, .sound], categories: nil)
      UIApplication.shared.registerUserNotificationSettings(settings)
    }
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // iOS9以上 Scheme
  // override func application(_ application: UIApplication, handleOpen url: URL) -> Bool {
  //     return true
  // }

  // iOS9以下 Scheme
  // override func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bool {
  //     return true
  // }

  // Universal Link
  override func application(_: UIApplication, continue userActivity: NSUserActivity, restorationHandler _: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
    // log
    print("continue userActivity")
    print(userActivity.webpageURL?.absoluteString ?? "nil")
    // リンク先のURLを取得
    let url = userActivity.webpageURL
    // リンク先のURLをFlutter側に渡す
    let controller: FlutterViewController = window?.rootViewController as! FlutterViewController
    // let channel = FlutterMethodChannel(name: "com.example.flutter_app/link", binaryMessenger: controller.binaryMessenger)
    // channel.invokeMethod("onLink", arguments: url?.absoluteString)
    api.initialLink = url?.absoluteString
    return super.application(UIApplication.shared, continue: userActivity, restorationHandler: { _ in })
  }

  // Handle click and deep link events for push notification
  // @available(iOS 10.0, *)
  // func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
  //   QGSdk.getSharedInstance().userNotificationCenter(center, didReceive: response)
  //   completionHandler()
  // }

  // Handle silent push notifications
  // pass completion handler UIBackgroundFetchResult accordingly
  override func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable: Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
    // log notification
    print("didReceiveRemoteNotification: \(userInfo)")
    let QG = QGSdk.getSharedInstance()
    QG.application(application, didReceiveRemoteNotification: userInfo)
    completionHandler(UIBackgroundFetchResult.noData)
    super.application(application, didReceiveRemoteNotification: userInfo, fetchCompletionHandler: completionHandler)
  }

  // 應用在前台或後台時接收到通知
  @available(iOS 10.0, *)
  override func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
    // log notification
    print("willPresent notification: \(notification.request.content.userInfo)")
    // log center
    print("center: \(center)")
    QGSdk.getSharedInstance().userNotificationCenter(center, willPresent: notification)
    completionHandler([.alert, .badge, .sound])
    super.userNotificationCenter(center, willPresent: notification, withCompletionHandler: completionHandler)
  }

  // 這個方法會呼叫到
  // 用戶點擊通知後應用被啟動或喚醒
  @available(iOS 10.0, *)
  override func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
    // log response
    print("didReceive response: \(response.notification.request.content.userInfo)")
    // log center
    print("center: \(center)")
    let isAppierPush = QGSdk.getSharedInstance().isAppierPush(response.notification.request.content.userInfo)
    print("isAppierPush: \(isAppierPush)")
    if isAppierPush {
      // Handle push notifications from Appier
      QGSdk.getSharedInstance().userNotificationCenter(center, didReceive: response)
      completionHandler()
    } else {
      // Handle other push notifications
    }
    super.userNotificationCenter(center, didReceive: response, withCompletionHandler: completionHandler)
  }

  // add these delegate methods to your AppDelegate class
  // 這個方法會呼叫到
  override func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    print("My token description is: \(deviceToken.description)")
    // 將 deviceToken 轉換成字串
    let tokenParts = deviceToken.map { data -> String in
        return String(format: "%02.2hhx", data)
    }
    let token = tokenParts.joined()
    print("My token is: \(token)")
    let QG = QGSdk.getSharedInstance()
    QG.setToken(deviceToken as Data)
    super.application(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken)
  }

  override func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
    print("Failed to get token, error: \(error.localizedDescription)")
    super.application(application, didFailToRegisterForRemoteNotificationsWithError: error)
  }

  override func applicationDidBecomeActive(_ application: UIApplication) {
    super.applicationDidBecomeActive(application)
    if #available(iOS 14, *) {
      ATTrackingManager.requestTrackingAuthorization { status in
        switch status {
          case .authorized:
            print("enable tracking")
          case .denied:
            print("disable tracking")
          default:
            print("disable tracking")
        }
      }
    }
  }
}
