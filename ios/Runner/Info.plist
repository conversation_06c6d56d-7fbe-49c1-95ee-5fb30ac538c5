<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>衣芙</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(APP_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>line3rdp.$(PRODUCT_BUNDLE_IDENTIFIER)</string>
				<string>fb2243741228996320</string>
				<string>fb522314041213398</string>
				<string>efshop</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FacebookAppID</key>
	<string>2243741228996320</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>衣芙女裝 官方購物網</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tg</string>
		<string>fbauth2</string>
		<string>fbapi</string>
		<string>fbapi20130214</string>
		<string>fbapi20130410</string>
		<string>fbapi20130702</string>
		<string>fbapi20131010</string>
		<string>fbapi20131219</string>
		<string>fbapi20140410</string>
		<string>fbapi20140116</string>
		<string>fbapi20150313</string>
		<string>fbapi20150629</string>
		<string>fbapi20160328</string>
		<string>fbauth</string>
		<string>fb-messenger-share-api</string>
		<string>fbshareextension</string>
		<string>line</string>
		<string>lineauth</string>
		<string>line3rdp.$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LineSDKConfig</key>
	<dict>
		<key>ChannelID</key>
		<string>1493494037</string>
	</dict>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>授權相機權限以供拍攝個人大頭照相片</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>授權相簿權限以供下載相片</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>授權相簿權限以供選擇個人大頭照相片</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>這允許我們根據您的網站與APP瀏覽紀錄，提供個人化內容與廣告</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>授權位置權限以供查詢附近門市</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>授權麥克風權限以供錄製影片</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>CFBundleAllowMixedLocalizations</key>
	<true/>
</dict>
</plist>
