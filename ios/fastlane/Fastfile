# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

DROPBOX_APP_KEY = "t1nlh4jyskomqd9"
DROPBOX_APP_SECRET = "mu7v54d0vbc3ok6"
DROPBOX_REFRESH_TOKEN = "Mk_qtpryG38AAAAAAAAAASXVoUeRScIFE3narNzPbjzek8cl7pRaLYKFVaBJN1-R"
DROPBOX_PATH = "/ipa"
OUTPUT_DIRECTORY = "../build/ios/ipa"
SCHEME = "Runner"
WORKSPACE = "Runner.xcworkspace"
WRITE_MODE_OVERWRITE = "overwrite"
IOS_ACCOUNT = "<EMAIL>"
IOS_PASSWORD = "dejd-yyue-sidu-artm"
IPA_FILE = "../../build/ios/ipa/mangolu.ipa"
KEY_DROPBOX_ACCESS_TOKEN = "dropbox_access_token"

default_platform(:ios)

platform :ios do
  desc "Description of what the lane does"
  lane :custom_lane do
    # add actions here: https://docs.fastlane.tools/actions
  end

  desc "使用 flutter build 產生 ipa"
  lane :flutter_build do
    # sh("cd .. && flutter build ios --release")
    Dir.chdir ".." do
      sh("pod", "repo", "update")
      sh("flutter", "clean")
      sh("flutter", "build", "ipa", "--export-method=ad-hoc")
    end
  end

  desc "從 refresh token 取得 dropbox access token"
  lane :dropbox_fetch_access_token do
    uri = URI.parse("https://api.dropbox.com/oauth2/token")
    request = Net::HTTP::Post.new(uri)
    request.basic_auth(DROPBOX_APP_KEY, DROPBOX_APP_SECRET)
    request.set_form_data(
      "grant_type" => "refresh_token",
      "refresh_token" => DROPBOX_REFRESH_TOKEN, 
    )
    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == "https") do |http|
      http.request(request)
    end
    # res = Net::HTTP.post_form(
    #   URI('https://api.dropbox.com/oauth2/token'),
    #   'grant_type' => 'refresh_token',
    #   'refresh_token' => DROPBOX_REFRESH_TOKEN,
    # )
    puts response.body if response.is_a?(Net::HTTPSuccess)
    parsed_json = JSON.parse(response.body)
    puts parsed_json
    dropbox_access_token = parsed_json["access_token"]
    puts "#{KEY_DROPBOX_ACCESS_TOKEN}: #{dropbox_access_token}"
    ENV[KEY_DROPBOX_ACCESS_TOKEN] = dropbox_access_token
  end

  # lane :upload_to_dropbox do
  #   dropbox_fetch_access_token
  #   dropbox(
  #     file_path: '../docs/index.html',
  #     dropbox_path: DROPBOX_PATH,
  #     write_mode: WRITE_MODE_OVERWRITE,
  #     app_key: DROPBOX_APP_KEY,
  #     app_secret: DROPBOX_APP_SECRET,
  #     access_token: ENV[KEY_DROPBOX_ACCESS_TOKEN],
  #     keychain_password: ''
  #   )
  # end

  def install_pods
    cocoapods(
      clean: true,
      podfile: "Podfile",
      try_repo_update_on_error: true
    )
  end

  desc "Push a new dev build to TestFlight"
  lane :dev do
    # sigh(force: true)
    # install_pods()
    # build_number = number_of_commits()
    # increment_build_number(
    #   build_number: build_number # set a specific number
    # )
    # build_app(workspace: "product-app-env-demo.xcworkspace", 
    #   scheme: "Staging",
    #   configuration: "Staging",
    # )
    # upload_to_testflight
    # pilot(skip_waiting_for_build_processing: true)
    # upload_to_testflight(
    #   # skip_submission: true,
    #   username: "<EMAIL>",
    #   app_identifier: "tw.omos.muyipork.dev",
    #   ipa: "../build/ios/Payload.ipa"
    # )
  end

  desc "Publish a new prod build to prod"
  lane :prod do
    # sigh(force: true)
    # install_pods()
    # build_number = number_of_commits()
    # increment_build_number(
    #   build_number: build_number # set a specific number
    # )
    # build_app(
    #   workspace: "product-app-env-demo.xcworkspace", 
    #   scheme: "Release", 
    #   configuration: "Release"
    # )
    # upload_to_testflight
    # pilot(skip_waiting_for_build_processing: true)
  end

  # desc "Runs all the tests"
  # lane :tests do
  #   install_pods()
  #   scan(workspace: "product-app-env-demo.xcworkspace",
  #       scheme: "product-app-env-demo",
  #       devices: ["iPhone Xs"],
  #       code_coverage: true,
  #       clean: true)
  # end

  # lane :firebase_development do
  #   replace_file(
  #     path_to_old_file: "./Runner/GoogleService-Info.plist",
  #     path_to_new_file: "./firebase/GoogleService-Info.development.plist"
  #   )
  # end

  # lane :firebase_production do
  #   replace_file(
  #     path_to_old_file: "./Runner/GoogleService-Info.plist",
  #     path_to_new_file: "./firebase/GoogleService-Info.production.plist"
  #   )
  # end

  # lane :deploy_development_to_testflight do
  #   api_key = app_store_connect_api_key(
  #     key_id: "D383SF739",
  #     issuer_id: "6053b7fe-68a8-4acb-89be-165aa6465141",
  #     key_filepath: "./AuthKey_D383SF739.p8",
  #     duration: 1200, # optional (maximum 1200)
  #     in_house: false # optional but may be required if using match/sigh
  #   )
  #   upload_to_testflight(
  #     api_key_path: "",
  #     api_key: api_key,
  #     skip_waiting_for_build_processing: true,
  #     username: "<EMAIL>",
  #     app_identifier: "tw.omos.muyipork.dev",
  #     ipa: "../build/ios/Payload.ipa",
  #     skip_submission:true,
  #    )
  # end

  lane :upload_sandbox_to_dropbox do
    dropbox_fetch_access_token
    dropbox(
      file_path: "#{OUTPUT_DIRECTORY}/sandbox.ipa",
      dropbox_path: DROPBOX_PATH,
      write_mode: WRITE_MODE_OVERWRITE,
      app_key: DROPBOX_APP_KEY,
      app_secret: DROPBOX_APP_SECRET,
      access_token: ENV[KEY_DROPBOX_ACCESS_TOKEN],
      keychain_password: ''
    )
  end

  lane :upload_production_to_dropbox do
    dropbox_fetch_access_token
    dropbox(
      file_path: "#{OUTPUT_DIRECTORY}/production.ipa",
      dropbox_path: DROPBOX_PATH,
      write_mode: WRITE_MODE_OVERWRITE,
      app_key: DROPBOX_APP_KEY,
      app_secret: DROPBOX_APP_SECRET,
      access_token: ENV[KEY_DROPBOX_ACCESS_TOKEN],
      keychain_password: ''
    )
  end

  lane :upload_appstore_to_dropbox do
    dropbox_fetch_access_token
    dropbox(
      file_path: "#{OUTPUT_DIRECTORY}/appstore.ipa",
      dropbox_path: DROPBOX_PATH,
      write_mode: WRITE_MODE_OVERWRITE,
      app_key: DROPBOX_APP_KEY,
      app_secret: DROPBOX_APP_SECRET,
      access_token: ENV[KEY_DROPBOX_ACCESS_TOKEN],
      keychain_password: ''
    )
  end

  lane :upload_ipa_to_testflight do
    sh("xcrun altool --upload-app -t ios -f #{IPA_FILE} -u #{IOS_ACCOUNT} -p #{IOS_PASSWORD}")
  end

  lane :build_clean do
    sh("cd .. && flutter clean")
  end

  lane :build_production_ipa do
    sh("cd .. && flutter build ipa --flavor=production --export-options-plist=production.plist")
  end

  lane :build_and_export_ipa do
    CONFIG_NAME = ENV["CONFIG_NAME"]
    puts "CONFIG_NAME: #{CONFIG_NAME}"
    EXPORT_METHOD = ENV["EXPORT_METHOD"]
    puts "EXPORT_METHOD: #{EXPORT_METHOD}"
    APP_ID = ENV["APP_ID"]
    puts "APP_ID: #{APP_ID}"
    PROFILE = ENV["PROFILE"]
    puts "PROFILE: #{PROFILE}"
    build_app(
      clean: true,
      # silent: true,
      include_symbols: true,
      include_bitcode: false,
      # scheme: SCHEME,
      # workspace: WORKSPACE,
      export_xcargs: "-allowProvisioningUpdates",
      # xcargs: "-allowProvisioningUpdates",
      # xcargs: {
      #   :BUNDLE_IDENTIFIER => APP_ID,
      #   :PROVISIONING_PROFILE_SPECIFIER => PROFILE
      # },
      # skip_build_archive: true,
      # skip_archive: true,
      # skip_package_ipa: false,
      # archive_path: "../build/ios/archive/Runner.xcarchive",
      export_method: EXPORT_METHOD,
      configuration: CONFIG_NAME,
      output_directory: OUTPUT_DIRECTORY,
      output_name: "#{APP_ID}.#{EXPORT_METHOD}.ipa",
      # export_options: {
      #   method: EXPORT_METHOD,
      #   signingStyle: "automatically",
      #   provisioningProfiles: { 
      #     "tw.omos.muyipork" => "OKSHOP_AD_HOC",
      #     "tw.omos.muyipork" => "OKSHOP_APP_STORE",
      #     "tw.omos.muyipork.dev" => "OKSHOP_AD_HOC_DEV",
      #     "tw.omos.muyipork.dev" => "OKSHOP_APP_STORE_DEV",
      #     # APP_ID => PROFILE
      #   }
      # },
      # export_team_id: "6W9Y38RSYR",
      # codesigning_identity: "iPhone Distribution: POS TECH Co., Ltd. (6W9Y38RSYR)"
    )
  end

  desc "Upload symbols to Crashlytics"
  lane :upload_debug_symbol do
    dsym_zip(
      archive_path: "../build/ios/archive/Runner.xcarchive",
      dsym_path: "../build/ios/Runner.app.dSYM.zip",
    )
    upload_symbols_to_crashlytics(
      dsym_path: "../build/ios/Runner.app.dSYM.zip",
      gsp_path: "Runner/GoogleService-Info.plist",
    )
  end
end
