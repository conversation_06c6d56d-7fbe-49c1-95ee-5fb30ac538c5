//
//  NotificationService.swift
//  AppierNotificationServiceExtension
//
//  Created by pht on 2024/5/14.
//

import AppierExtension


class NotificationService: QGNotificationService {
    var contentHandler: ((UNNotificationContent) -> Void)!
    var bestAttemptContent: UNMutableNotificationContent!
    let APPIER_APP_GROUP_ID = "group.com.shangching.com.shangching.efshop.notification"
    
    override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
        super.didReceive(request, withContentHandler: contentHandler)
        self.bestAttemptContent = request.content.mutableCopy() as! UNMutableNotificationContent
        self.contentHandler = contentHandler
        
        let isAppierPush = QGNotificationSdk.sharedInstance(withAppGroup:APPIER_APP_GROUP_ID).isAppierPush(request.content.userInfo)
        if isAppierPush {
            QGNotificationSdk.sharedInstance(withAppGroup:APPIER_APP_GROUP_ID).didReceive(request) { content in
                contentHandler(content);
            }
        }
    }
    
    override func serviceExtensionTimeWillExpire() {
        super.serviceExtensionTimeWillExpire()
        let isAppierPush = QGNotificationSdk.sharedInstance(withAppGroup:APPIER_APP_GROUP_ID).isAppierPush(self.bestAttemptContent.userInfo)
        if isAppierPush {
            self.contentHandler(self.bestAttemptContent)
        }
    }
}
