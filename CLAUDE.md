# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Essential Flutter Commands

- `flutter pub get` - Install dependencies
- `flutter pub run build_runner build` - Generate mock files for testing
- `flutter test` - Run all tests
- `flutter pub run pigeon --input pigeons/message.dart` - Generate platform-specific code from Pigeon definitions

### Build Commands

- `flutter build apk` - Build Android APK
- `flutter build ios` - Build iOS app
- `flutter run` - Run app in debug mode
- `flutter run --release` - Run app in release mode

### Platform-Specific Deployment

- **Android**: Uses Fastlane for deployment (android/fastlane/Fastfile)
- **iOS**: Uses Fastlane for deployment (ios/fastlane/Fastfile)
- **Deployment Scripts**: Use Python scripts in project root
  - `deploy_android.py` - Android deployment
  - `deploy_ios.py` - iOS deployment

## Project Architecture

### Core Architecture Pattern

- **Framework**: Flutter with GetX state management
- **API Layer**: Dio HTTP client with custom ApiProvider
- **State Management**: GetX Controllers with dependency injection
- **Local Storage**: Hive for caching, GetStorage for preferences
- **Authentication**: JWT tokens with social login (Apple, Facebook, LINE)

### Key Directory Structure

```
lib/
├── app/
│   ├── components/          # Reusable UI components
│   ├── models/             # Data models and API response objects
│   ├── modules/            # Feature modules (MVC pattern)
│   │   ├── [feature]/
│   │   │   ├── bindings/   # Dependency injection
│   │   │   ├── controllers/ # Business logic
│   │   │   └── views/      # UI components
│   ├── providers/          # Service layer (API, storage, etc.)
│   └── routes/             # App navigation and routing
├── firebase_options.dart   # Firebase configuration
├── main.dart              # App entry point
└── constants.dart         # App-wide constants
```

### Provider Pattern

The app uses a provider pattern for services:
- `ApiProvider` - HTTP client wrapper with authentication
- `PrefProvider` - User preferences and settings
- `AppierProvider` - Marketing analytics integration
- `WabowProvider` - Custom analytics provider
- `NotificationProvider` - Push notification handling

### Navigation Architecture

- **Router**: GetX routing with named routes
- **Deep Linking**: App Links package for URL handling
- **Route Generation**: Auto-generated routes in `app_routes.dart`

### Firebase Integration

- **Analytics**: Firebase Analytics for user behavior tracking
- **Crashlytics**: Error reporting and crash analytics
- **Messaging**: Push notifications via Firebase Cloud Messaging
- **Configuration**: Environment-specific Firebase configs

### Third-Party Integrations

- **Appier**: Marketing automation and analytics
- **Social Login**: Apple Sign-In, Facebook Login, LINE Login
- **Payment**: Integrated payment processing
- **Image Processing**: Extended Image package for advanced image handling

### Testing Strategy

- **Unit Tests**: Located in `test/` directory
- **Mocking**: Uses Mockito with code generation
- **Test Models**: Extensive JSON test data in `docs/models/`

### Platform-Specific Features

- **iOS**: 
  - Notification extensions for rich notifications
  - App Store deployment via Fastlane
  - Entitlements for different build configurations
- **Android**:
  - Google Services integration
  - Play Store deployment via Fastlane
  - ProGuard configuration for release builds

### Development Environment Setup

- **Flutter SDK**: 3.24.5 (stable channel)
- **Dart**: 3.5.4
- **IDE Support**: Configured for both Android Studio and VS Code
- **VPN Requirement**: Office VPN needed for API access (api.wabow.com:81)

### Key Dependencies

- **State Management**: GetX 4.6.6
- **HTTP Client**: Dio 5.4.3+1 with logging
- **Local Storage**: Hive 2.2.3, GetStorage 2.1.1
- **UI Components**: Sizer 3.0.0 for responsive design
- **Image Handling**: Extended Image 8.2.1, Cached Network Image 3.4.0
- **WebView**: Custom WebView implementation for embedded content