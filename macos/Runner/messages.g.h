// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from <PERSON><PERSON> (v20.0.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#import <Foundation/Foundation.h>

@protocol FlutterBinaryMessenger;
@protocol FlutterMessageCodec;
@class FlutterError;
@class FlutterStandardTypedData;

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, PGNCode) {
  PGNCodeOne = 0,
  PGNCodeTwo = 1,
};

/// Wrapper for PGNCode to allow for nullability.
@interface PGNCodeBox : NSObject
@property(nonatomic, assign) PGNCode value;
- (instancetype)initWithValue:(PGNCode)value;
@end

@class PGNMessageData;

@interface PGNMessageData : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithName:(nullable NSString *)name
    description:(nullable NSString *)description
    code:(PGNCode)code
    data:(NSDictionary<NSString *, NSString *> *)data;
@property(nonatomic, copy, nullable) NSString * name;
@property(nonatomic, copy, nullable) NSString * description;
@property(nonatomic, assign) PGNCode code;
@property(nonatomic, copy) NSDictionary<NSString *, NSString *> * data;
@end

/// The codec used by all APIs.
NSObject<FlutterMessageCodec> *PGNGetMessagesCodec(void);

@protocol PGNExampleHostApi
/// @return `nil` only when `error != nil`.
- (nullable NSString *)getHostLanguageWithError:(FlutterError *_Nullable *_Nonnull)error;
/// @return `nil` only when `error != nil`.
- (nullable NSNumber *)addNumber:(NSInteger)a toNumber:(NSInteger)b error:(FlutterError *_Nullable *_Nonnull)error;
- (void)sendMessageMessage:(PGNMessageData *)message completion:(void (^)(NSNumber *_Nullable, FlutterError *_Nullable))completion;
- (void)getInitialLinkWithCompletion:(void (^)(NSString *_Nullable, FlutterError *_Nullable))completion;
@end

extern void SetUpPGNExampleHostApi(id<FlutterBinaryMessenger> binaryMessenger, NSObject<PGNExampleHostApi> *_Nullable api);

extern void SetUpPGNExampleHostApiWithSuffix(id<FlutterBinaryMessenger> binaryMessenger, NSObject<PGNExampleHostApi> *_Nullable api, NSString *messageChannelSuffix);


@interface PGNMessageFlutterApi : NSObject
- (instancetype)initWithBinaryMessenger:(id<FlutterBinaryMessenger>)binaryMessenger;
- (instancetype)initWithBinaryMessenger:(id<FlutterBinaryMessenger>)binaryMessenger messageChannelSuffix:(nullable NSString *)messageChannelSuffix;
- (void)flutterMethodAString:(nullable NSString *)aString completion:(void (^)(NSString *_Nullable, FlutterError *_Nullable))completion;
@end

NS_ASSUME_NONNULL_END
