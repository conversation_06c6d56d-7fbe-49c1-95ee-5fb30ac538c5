# var

@host = https://api.dropbox.com
@app_key = t1nlh4jyskomqd9
@app_secret = mu7v54d0vbc3ok6
@refresh_token = Mk_qtpryG38AAAAAAAAAASXVoUeRScIFE3narNzPbjzek8cl7pRaLYKFVaBJN1-R

### get code (使用網頁開啟，用來取得 code)

GET https://www.dropbox.com/oauth2/authorize
?client_id=t1nlh4jyskomqd9
&token_access_type=offline
&response_type=code

### get refresh_token & access_token from code
# @name get_token
# @prompt code 8FkkevIDaa8AAAAAAAAAuFlNol9QL5RjISoKCDOK0Fg

curl {{host}}/oauth2/token \
-d code={{code}} \
-d grant_type=authorization_code \
-u {{app_key}}:{{app_secret}}

### get access_toke from refresh_token
# @name get_token

curl {{host}}/oauth2/token \
-d grant_type=refresh_token \
-d refresh_token={{refresh_token}} \
-u {{app_key}}:{{app_secret}}
