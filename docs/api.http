# 宣告

# @host = {{dev}}
@host = https://{{domain}}
@token_0 = {{login.response.body.token}}
@token_1 = {{renew.response.body.token}}
@token_ef = eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-hpJ2D6qPXXwbrKdq0wZ6KLKXnA7W4nWBovdEffk44U
@token_yumingdev = eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.XbcVIDPH6MksNx7LRzGUWHWbW8bHEPd3meryctAjRdc
@token_sandbox = eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************.QhO6A1zSdkATNUO9PKi_q_CgH2pvPds7lVldfYk0RP4
@token_2 = eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ER0zqXv3WPdd4GPRrByq-X-W_YxO2ETfeUKA_MIbdJ0
###############################################################################
### 本站使用者登入
###############################################################################
# @name login

POST {{host}}/login HTTP/1.1
Content-Type: application/x-www-form-urlencoded

email={{email}}
&password={{password}}

###############################################################################
### Facebook 使用者登入
###############################################################################
# @prompt access_token 通過授權後，取得的 facebook access token

POST {{host}}/login/facebook HTTP/1.1
Content-Type: application/x-www-form-urlencoded

access_token={{access_token}}

###############################################################################
### Line 使用者登入
###############################################################################
# @prompt access_token 通過授權後，取得的 line access token

POST {{host}}/login/line HTTP/1.1

access_token={{access_token}}

###############################################################################
### Apple 使用者登入
###############################################################################
# @prompt access_token 通過授權後，取得的 apple access token
# @prompt uid 通過授權後，取得的 apple uid
# @prompt code 通過授權後，取得的 apple code

POST {{host}}/login/apple HTTP/1.1

access_token={{access_token}}
&uid={{uid}}
&code={{code}}

###############################################################################
### 忘記密碼處理
###############################################################################
# @prompt email 欲註冊的 email

GET {{host}}/login/forget/password
?email={{email}}

###############################################################################
### 忘記密碼處理
###############################################################################
# @prompt user_id 使用者id
# @prompt forget_key 忘記密碼專用key

POST {{host}}/login/forget/password/{{user_id}}/{{forget_key}} HTTP/1.1

###############################################################################
### 忘記密碼處理 (reset)
###############################################################################
# @prompt email 欲註冊的 email

GET {{host}}/login/reset/password
?email={{email}}

###############################################################################
### 忘記密碼處理 (reset)
###############################################################################
# @prompt user_id 使用者id
# @prompt forget_key 忘記密碼專用key

POST {{host}}/login/reset/password/{{user_id}}/{{forget_key}} HTTP/1.1

###############################################################################
### 取得 session token (X)
###############################################################################

GET {{host}}/session HTTP/1.1

###############################################################################
### 重新簽發及交換一組 token (舊的 token 會失效)
###############################################################################
# @name renew

POST {{host}}/login/renew HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

###############################################################################
### 使用者登出
###############################################################################

POST {{host}}/logout HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

###############################################################################
### 本站註冊使用者
###############################################################################
# @prompt email 欲註冊的 email <EMAIL>
# @prompt password 欲註冊的密碼 vufWCR4V
# @prompt fullname 欲註冊的全名

POST {{host}}/register HTTP/1.1
Content-Type: application/x-www-form-urlencoded

email={{email}}
&password={{password}}
&fullname={{fullname}}

###############################################################################
### 檢查 Email 註冊狀況
###############################################################################
# @prompt email 欲檢查的 email

POST {{host}}/register/check HTTP/1.1
Content-Type: application/x-www-form-urlencoded

email={{email}}

###############################################################################
### 取得有 7-11 的所有縣市資料
###############################################################################

GET {{host}}/store/seven_eleven/cities HTTP/1.1

###############################################################################
### 取得有 7-11 的所有鄉鎮資料
###############################################################################

GET {{host}}/store/seven_eleven/towns HTTP/1.1

###############################################################################
### 取得某區域的所有 7-11 店家資料
###############################################################################
# @prompt zip_code 三碼郵遞區號 100

GET {{host}}/store/seven_eleven/zip_code/{{zip_code}} HTTP/1.1

###############################################################################
### 取得有全家的所有縣市資料
###############################################################################

GET {{host}}/store/family_mart/cities HTTP/1.1

###############################################################################
### 取得有全家的所有鄉鎮資料
###############################################################################

GET {{host}}/store/family_mart/towns HTTP/1.1

###############################################################################
### 取得某區域的所有全家店家資料
###############################################################################
# @prompt zip_code 三碼郵遞區號 100

GET {{host}}/store/family_mart/zip_code/{{zip_code}} HTTP/1.1

###############################################################################
### 產品搜尋
###############################################################################
# @prompt keyword 關鍵字: 衣服

GET {{host}}/search/products/{{keyword}} HTTP/1.1

###############################################################################
### 產品標籤搜尋
###############################################################################
# @prompt keyword 關鍵字: 涼感

GET {{host}}/tags/products/{{keyword}} HTTP/1.1

###############################################################################
### 熱門關鍵字-筆數由後台設定
###############################################################################

GET {{host}}/hot_terms HTTP/1.1

###############################################################################
### 首頁廣告 - index
###############################################################################

GET {{host}}/index HTTP/1.1

###############################################################################
### 首頁廣告 - index without web-app
###############################################################################

GET {{host}}/index/mobile HTTP/1.1

###############################################################################
### 首頁廣告 - index with web-app
###############################################################################

GET {{host}}/index/mobile HTTP/1.1
X-Client-Agent: web-app

###############################################################################
### 首頁廣告中 - 期間限定資料
###############################################################################

GET {{host}}/promotions/limited HTTP/1.1

###############################################################################
### 首頁廣告中 - 期間限定資料
###############################################################################

GET {{host}}/promotions/limited-nmgiftmix HTTP/1.1

###############################################################################
### 首頁模組 APP專用
###############################################################################

GET {{host}}/index/module/app HTTP/1.1

###############################################################################
### 首頁模組 手機專用
###############################################################################

GET {{host}}/index/module/mobile HTTP/1.1

###############################################################################
### 分類廣告
###############################################################################

GET {{host}}/advs/categories HTTP/1.1

###############################################################################
### 靜態頁面 - index
###############################################################################
# @prompt id 靜態頁面 id

GET {{host}}/content/{{id}} HTTP/1.1

###############################################################################
### 搜尋欄關聯字詞
###############################################################################

GET {{host}}/suggestions HTTP/1.1

###############################################################################
### 取得巢狀的兩層類別列表
###############################################################################

GET {{host}}/categories HTTP/1.1

###############################################################################
### 取得新版巢狀類別列表
###############################################################################

GET {{host}}/categories/index HTTP/1.1

###############################################################################
### 取得單一類別的所有產品
###############################################################################
# @prompt id 類別 id

GET {{host}}/categories/{{id}} HTTP/1.1

###############################################################################
### 取得單一類別的所有產品
###############################################################################
# @prompt id 類別 id
# @prompt quantity 每頁數量

GET {{host}}/categories/{{id}}/{{quantity}} HTTP/1.1

###############################################################################
### 取得單一類別的 seo
###############################################################################
# @prompt id 類別 id 21

GET {{host}}/categories/{{id}}/seo HTTP/1.1

###############################################################################
### 取得類別商品
###############################################################################
# @prompt id 類別 id
# @prompt quantity 每頁數量

GET {{host}}/categories/app/{{id}}/{{quantity}} HTTP/1.1

###############################################################################
### 取得單一產品資料
###############################################################################
# @prompt id 產品 id: 76793, 85372

GET {{host}}/products/{{id}} HTTP/1.1

###############################################################################
### 取得某產品的所有說明圖片或影片
###############################################################################
# @prompt id 產品 id: 57788

GET {{host}}/products/{{id}}/group_images HTTP/1.1

###############################################################################
### 取得某產品的同系列產品資料
###############################################################################
# @prompt id 產品 id: 76793

GET {{host}}/products/{{id}}/group HTTP/1.1

###############################################################################
### 取得某產品的評價資料
###############################################################################
# @prompt id 產品 id: 76793

GET {{host}}/products/{{id}}/comments HTTP/1.1

###############################################################################
### 取得某產品的搭配購產品資料
###############################################################################
# @prompt id 產品 id: 76793

GET {{host}}/products/{{id}}/collocation HTTP/1.1

###############################################################################
### 取得同質產品的搭配購產品資料
###############################################################################
# @prompt id 產品 id: 76793

GET {{host}}/products/{{id}}/collocation/all HTTP/1.1

###############################################################################
### 新增商品到購物車
###############################################################################
# @prompt productId 產品 id: 76793
# @prompt quantity 數量: 1

POST {{host}}/cart/{{productId}}/{{quantity}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 更新商品到購物車 (更新數量)
###############################################################################
# @prompt productId 產品 id: 76793
# @prompt quantity 數量: 1

PATCH {{host}}/cart/{{productId}}/{{quantity}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 點重新購買時，購物車內若已有相同項目則直接取代，確保與訂單項目相同，不累計；也不刪除其他品項(?)
###############################################################################
# @prompt orderId 產品 id: 76793

POST {{host}}/cart/buy_again/{{orderId}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 刪除購物車商品 (商品數量設定為0)
###############################################################################
# @prompt productId 產品 id: 76793

DELETE {{host}}/cart/{{productId}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 新增多筆加價購商品到購物車
###############################################################################

POST {{host}}/cart/add_on HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

items=[{"product_id":"76793","quantity":"1"}]

###############################################################################
### 查看購物車商品數量
###############################################################################

GET {{host}}/cart/quantity HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### cart 與 checkout 頁面取得購物車包含商品與總價等計算的詳細資訊(?)
###############################################################################

GET {{host}}/cart/items HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 設定本次結帳使用紅利點數(?)，填入 0 server 會 crash
###############################################################################
# @prompt points 使用紅利點數: 100

POST {{host}}/cart/set/bonus/{{points}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

###############################################################################
### 設定本次結帳使用付款方式(?)
###############################################################################
# @prompt paymentId 付款方式: 7eshop, fmeshop, cod, hitrust

POST {{host}}/cart/set/payment/{{paymentId}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

###############################################################################
### 設定本次結帳使用的折扣(?)
###############################################################################
# @prompt rebateNumber 折扣代碼

POST {{host}}/cart/set/rebate/{{rebateNumber}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

###############################################################################
### 刪除使用者所有優惠(?)
###############################################################################
POST {{host}}/cart/forget/userOffer
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

###############################################################################
### 購物車優惠卷列出
###############################################################################

GET {{host}}/cart/vouchers
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

###############################################################################
### 設定本次結帳使用的優惠卷
###############################################################################
# @prompt voucher_id 優惠卷id

POST {{host}}/cart/set/voucher/{{voucher_id}}
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

###############################################################################
### 新增一筆裝置ID
###############################################################################
# @prompt deviceType 裝置種類: android, ios

POST {{host}}/devices/{{deviceType}}/create HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

device_id=**********

###############################################################################
### 取得常見問題
###############################################################################

GET {{host}}/faq HTTP/1.1

###############################################################################
### 所有任選
###############################################################################

GET {{host}}/promotions/buy_n_get_m_discount HTTP/1.1

###############################################################################
### 單一任選
###############################################################################
# @prompt id 任選 id 136

GET {{host}}/promotions/buy_n_get_m_discount/{{id}} HTTP/1.1

###############################################################################
### 買N送M
###############################################################################
# @prompt id 任選 id 134, 204

GET {{host}}/promotions/buy_n_gift_m/{{id}} HTTP/1.1

###############################################################################
### 加價購所有商品
###############################################################################

GET {{host}}/promotions/add_on_discount HTTP/1.1

###############################################################################
### 所有加價購活動資訊
###############################################################################

GET {{host}}/promotions/add_on_ids HTTP/1.1

###############################################################################
### 取得指定會員的發票證明聯 (?)
###############################################################################
# @prompt id 訂單 id: 6901532

GET {{host}}/members/orders/{{id}}/invoices HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得所有訂單
###############################################################################

GET {{host}}/members/orders HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得所有訂單狀態數量
###############################################################################

GET {{host}}/members/orders/count HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得一筆訂單
###############################################################################
# @prompt id 訂單 id: 3702969

GET {{host}}/members/orders/{{id}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 訂單取消
###############################################################################
# @prompt id 訂單 id: 3702969

PATCH {{host}}/members/orders/{{id}}/cancel HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得特定訂單的詢問記錄
###############################################################################
# @prompt id 訂單 id: 3702969

GET {{host}}/members/orders/{{id}}/questions HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 新增一筆特定訂單的詢問記錄(?)
###############################################################################
# @prompt id 訂單 id: 6901532
# @prompt type_id 1, 2, 3, 4, 5, 6

POST {{host}}/members/orders/{{id}}/questions HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

question=問題
&type_id=1

###############################################################################
### 取得物流記錄
###############################################################################
# @prompt id 訂單 id: 3702969

GET {{host}}/members/orders/{{id}}/ships HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得退貨物流
###############################################################################
# @prompt id 訂單 id: 6901532

GET {{host}}/members/orders/{{id}}/ships/refunds HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得退貨訊息
###############################################################################
# @prompt id 訂單 id: 6901746

GET {{host}}/members/orders/{{id}}/refund HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 新增退貨 (使用者退貨資料 id 或 銀行代碼+名稱+戶名+帳號 擇一必填)(?)
###############################################################################
# @prompt id 訂單 id: 6901746

POST {{host}}/members/orders/{{id}}/refund HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

user_refund_id=1
&bank_branch=高雄分行
&bank_code=004
&account_name=台灣銀行
&account_number=**********
&comment=備註
&products=jsonstring

###############################################################################
### 取得訂單商品評價
###############################################################################
# @prompt id 訂單 id: 3702969

GET {{host}}/members/orders/{{id}}/comments HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 新增多筆訂單商品評價 (?)
###############################################################################
# @prompt id 訂單 id: 6901532

POST {{host}}/members/orders/{{id}}/comments HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

comments=[{"product_id":"深藍","comment":"水藍"}]

###############################################################################
### 新增意見回饋
###############################################################################

POST {{host}}/members/feedbacks HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

feedback=意見回饋
&telephone=0912345678

###############################################################################
### 取得個人資料
###############################################################################

GET {{host}}/members/profile HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 更新個人資料
###############################################################################

PATCH {{host}}/members/profile HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

fullname=yuming
&gender=1
&birthday=1980-01-01
&subscribe_epaper=0

###############################################################################
### 變更密碼
###############################################################################
# @prompt password 原密碼
# @prompt new_password 新密碼

PATCH {{host}}/members/password HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

password={{password}}
&new_password={{new_password}}

###############################################################################
### 取得個人的所有地址
###############################################################################

GET {{host}}/members/addresses HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 刪除一筆個人地址
###############################################################################
# @prompt id 地址 id: 149

DELETE {{host}}/members/addresses/{{id}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 新增一組個人地址
###############################################################################
# @prompt type 地址類型: home, seven_eleven, family_mart

POST {{host}}/members/addresses/{{type}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

addressType={{type}}
&is_default=true
&receiver_name=yuming
&store_id=1
&store_name=seven
&zipcode=813
&address=高雄市左營區新莊一路123號

###############################################################################
### 更新一筆個人的地址，不傳入的欄位表示不更新
###############################################################################
# @prompt type 地址類型: home, seven_eleven, family_mart
# @prompt id 地址 id: 150

PATCH {{host}}/members/addresses/{{type}}/{{id}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

is_default=true
&receiver_name=yuming
&store_id=1
&store_name=seven
&zipcode=813
&address=高雄市左營區新莊一路123號

###############################################################################
### 取得個人的所有退貨資料
###############################################################################

GET {{host}}/members/refund HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 新增一組個人退貨資料
###############################################################################

POST {{host}}/members/refund HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

is_default=true
&bank_branch=高雄分行
&bank_code=004
&account_name=台灣銀行
&account_number=**********

###############################################################################
### 更新一筆個人的退貨資料，不傳入的欄位表示不更新
###############################################################################
# @prompt id 退貨資料 id: 1

PATCH {{host}}/members/refund/{{id}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

is_default=true
&bank_branch=高雄分行
&bank_code=004
&account_name=yuming
&account_number=**********

###############################################################################
### 刪除一筆個人退貨資料
###############################################################################
# @prompt id 退貨資料 id: 7

DELETE {{host}}/members/refund/{{id}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 新增 Preorder 貨到通知
###############################################################################
# @prompt product_id 商品 id: 76793
# @prompt email 通知 email: <EMAIL>

POST {{host}}/products/{{product_id}}/preorder HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

email={{email}}

###############################################################################
### 取得 Preorder 貨到通知
###############################################################################

GET {{host}}/members/preorders HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 刪除該使用者的所有 Preorder 貨到通知
###############################################################################

DELETE {{host}}/members/preorders HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 刪除一筆貨到通知
###############################################################################
# @prompt id 貨到通知 id: 1

DELETE {{host}}/members/preorders/{{id}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 修改此使用者的推播偏好設定
###############################################################################
# @prompt action 開關: on, off

POST {{host}}/members/messages/preference/{{action}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 客服紀錄
###############################################################################

GET {{host}}/members/messages/questions HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 優惠活動 (推播)
###############################################################################

GET {{host}}/members/messages/activities HTTP/1.1

###############################################################################
### 未登入優惠活動 (推播)
###############################################################################

GET {{host}}/members/messages/activities/nologin HTTP/1.1

###############################################################################
### 通知消息 (訂單狀態改變)
###############################################################################

GET {{host}}/members/messages/orders HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 物流通知 (推播)
###############################################################################

GET {{host}}/members/messages/ships HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得訊息中心摘要
###############################################################################

GET {{host}}/members/messages/summarys HTTP/1.1

###############################################################################
### 優惠活動清空 (DB 標記為前端不可讀取)
###############################################################################

PATCH {{host}}/members/messages/activities/clear HTTP/1.1

###############################################################################
### 推播已讀取
###############################################################################
# @prompt type activities, orders, ships, questions

PUT {{host}}/members/messages/read/{{type}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 推播未讀取數量
###############################################################################

GET {{host}}/members/messages/unread HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 優惠卷
###############################################################################
# @prompt stage app, mobile

GET {{host}}/members/messages/vouchers/{{stage}}
Authorization: Bearer {{token_sandbox}}

###############################################################################
### 取得搜尋紀錄
###############################################################################

GET {{host}}/members/search_histories HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 新增搜尋紀錄
###############################################################################
# @prompt keyword 搜尋關鍵字: 上衣

POST {{host}}/members/search_histories HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

keywords={{keyword}}

###############################################################################
### 刪除該使用者的所有搜尋紀錄
###############################################################################

DELETE {{host}}/members/search_histories HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得我的收藏
###############################################################################

GET {{host}}/members/my_favorite HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 刪除所有收藏
###############################################################################

DELETE {{host}}/members/my_favorite HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 同步
###############################################################################

PUT {{host}}/members/my_favorite HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

client_items={"Y18S376-1013":"深藍","Y18S255-1013":"水藍"}

###############################################################################
### 新增
###############################################################################

POST {{host}}/members/my_favorite HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

number=Y18S376-1013
&color=深藍

###############################################################################
### 取得收藏數量
###############################################################################

GET {{host}}/members/my_favorite/count HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得是否為收藏商品
###############################################################################
# @prompt number 商品id

GET {{host}}/members/my_favorite/{{number}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 移除
###############################################################################
# @prompt number 商品id: Y18S376-1013

DELETE {{host}}/members/my_favorite/{{number}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得會員紅利點數資料
###############################################################################

GET {{host}}/members/my_bonus HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得會員彈窗資料
###############################################################################
# @prompt stage app, mobile

GET {{host}}/members/popup/{{stage}} HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 彈窗完成
###############################################################################
# @prompt voucher_id 彈窗 id: 1

PUT {{host}}/members/popup/{{voucher_id}} HTTP/1.1

###############################################################################
### 取得所有設定值
###############################################################################

GET {{host}}/configs HTTP/1.1

###############################################################################
### 取結帳前預覽資訊
###############################################################################

GET {{host}}/checkout HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### post 結帳表單 (須先設定 payment)
###############################################################################

POST {{host}}/checkout HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{token_0}}

buyer_name=umum
&buyer_telephone=0912345678
&buyer_city=台北市
&buyer_postcode=100
&buyer_address=台北市中正區忠孝西路一段1號
&receiver_name=umum
&receiver_telephone=0912345678
&receiver_city=台北市
&store_id=227728
&store_name=seven
&store_address=台北市中正區忠孝西路一段1號
&vat_number=83193989
&invoice_vehicle=/KK2X9NQ
&comment=備註
&parent_id=6901532

###############################################################################
### 付款方式選擇頁
###############################################################################

GET {{host}}/payment_options HTTP/1.1
Authorization: Bearer {{token_0}}

###############################################################################
### 取得漢堡選單
###############################################################################

GET {{host}}/menus HTTP/1.1

###############################################################################
### 頁尾
###############################################################################

GET {{host}}/footer HTTP/1.1

###############################################################################
### android
###############################################################################

GET https://m.efshop.com.tw/.well-known/assetlinks.json

###############################################################################
### ios
###############################################################################

GET https://m.efshop.com.tw/.well-known/apple-app-site-association
