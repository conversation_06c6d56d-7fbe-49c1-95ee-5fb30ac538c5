{"resize_image": false, "rno": "1", "expiration_time": "1697466031", "headsUp": false, "notificationId": "17253100000", "source": "QG", "message": "This is the message", "title": "This is the title", "type": "basic", "channelId": "po", "poll": true, "actions": [{"deepLink": "https://www.google.com/", "id": 1, "text": "Click me"}, {"deepLink": "https://www.google.com/", "id": 1, "text": "Option 1"}], "bigImageUrl": "https://www.dogster.com/wp-content/uploads/2019/12/1912_<PERSON><PERSON>_<PERSON><PERSON>i_GettyImages-1061822700.png", "subText": "A cattle herding dog breed", "textColor": "#f8e71c", "bgColor": "#8b572a", "deepLink": "https://www.appier.aiqua/?_lnuid=42314233&_lnchid=1232567483524", "pileUp": true, "imageUrl": "https://s3.amazonaws.com/cdn-origin-etr.akc.org/wp-content/uploads/2017/11/14112506/Pembroke-Welsh-Corgi-standing-outdoors-in-the-fall.jpg", "qgPayload": {"qgToast": "Copied to Clipboard", "qgTextToCopy": "copy123", "myKey": "myValue", "Key of customize key-value pairs if any": "Value of customize key-value pairs if any", "key": "value", "myKey1": "myValue1", "myKey2": "myValue2", "k1": "v1", "k2": "v2", "k3": "v3"}, "q10CF": true, "soundUrl": "https://aiqua-mobile-campaign-tester-cxvcdcmecq-de.a.run.app/pianoE.mp3", "qgTimestamp": "2023-09-21T23:48:11", "aps": {"mutable-content": 1, "alert": {"title": "This is the title", "subtitle": "This is the subtitle if any", "body": "This is the message body"}, "category": "Action category identifier", "sound": "Customize notification sound file"}, "qgPush": {"type": "basic", "url": "This is the media attachment URL if any", "custom": {"aspect": "fill", "carouselType": "slider", "data": [{"title": "This is the headline", "imageUrl": "This is the image URL", "body": "This is the message body if any", "deepLink": "This is the deepLink URL if any"}]}}, "qgts": "1694769363", "nid": "15624100000", "qg": "1562410", "contentImageUrl": "https://s3.amazonaws.com/cdn-origin-etr.akc.org/wp-content/uploads/2017/11/14112506/Pembroke-Welsh-Corgi-standing-outdoors-in-the-fall.jpg", "animation": {"images": ["https://s3.amazonaws.com/cdn-origin-etr.akc.org/wp-content/uploads/2017/11/14112506/Pembroke-Welsh-Corgi-standing-outdoors-in-the-fall.jpg", "https://www.dogster.com/wp-content/uploads/2019/12/1912_<PERSON><PERSON>_<PERSON><PERSON>i_GettyImages-1061822700.png", "https://www.thesprucepets.com/thmb/N-RyBF_Ehn3zyixrXe30-jV3xoI=/960x0/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/breed_profile_corgi_1117986_hero_917-6ed2ed41b6e641bb98221b13a1d83a86.jpg"], "millisecondsToRefresh": 500}, "slider": [{"image": "https://s3.amazonaws.com/cdn-origin-etr.akc.org/wp-content/uploads/2017/11/14112506/Pembroke-Welsh-Corgi-standing-outdoors-in-the-fall.jpg", "deepLink": "https://www.youtube.com/", "qgPayload": {"qgToast": "Copied to Clipboard", "qgTextToCopy": "copy123", "myKey": "myValue", "Key of customize key-value pairs if any": "Value of customize key-value pairs if any", "key": "value", "myKey1": "myValue1", "myKey2": "myValue2", "k1": "v1", "k2": "v2", "k3": "v3"}, "message": "description 2", "title": "headline 2"}, {"image": "https://www.dogster.com/wp-content/uploads/2019/12/1912_<PERSON><PERSON>_<PERSON><PERSON>i_GettyImages-1061822700.png", "deepLink": "https://www.google.com/maps/", "qgPayload": {"qgToast": "Copied to Clipboard", "qgTextToCopy": "copy123", "myKey": "myValue", "Key of customize key-value pairs if any": "Value of customize key-value pairs if any", "key": "value", "myKey1": "myValue1", "myKey2": "myValue2", "k1": "v1", "k2": "v2", "k3": "v3"}, "message": "description 2", "title": "headline 2"}], "qg_prev_button": "https://cdn.qgraph.io/img/left.png", "qg_next_button": "https://cdn.qgraph.io/img/right.png", "closeNotificationOnItemClick": true, "carousel": [{"image": "https://s3.amazonaws.com/cdn-origin-etr.akc.org/wp-content/uploads/2017/11/14112506/Pembroke-Welsh-Corgi-standing-outdoors-in-the-fall.jpg", "deepLink": "https://www.google.com/", "qgPayload": {"qgToast": "Copied to Clipboard", "qgTextToCopy": "copy123", "myKey": "myValue", "Key of customize key-value pairs if any": "Value of customize key-value pairs if any", "key": "value", "myKey1": "myValue1", "myKey2": "myValue2", "k1": "v1", "k2": "v2", "k3": "v3"}, "message": "description 1", "title": "headline 1"}, {"image": "https://www.dogster.com/wp-content/uploads/2019/12/1912_<PERSON><PERSON>_<PERSON><PERSON>i_GettyImages-1061822700.png", "deepLink": "https://www.youtube.com/", "qgPayload": {"qgToast": "Copied to Clipboard", "qgTextToCopy": "copy123", "myKey": "myValue", "Key of customize key-value pairs if any": "Value of customize key-value pairs if any", "key": "value", "myKey1": "myValue1", "myKey2": "myValue2", "k1": "v1", "k2": "v2", "k3": "v3"}, "message": "description 2", "title": "headline 2"}, {"image": "https://www.thesprucepets.com/thmb/N-RyBF_Ehn3zyixrXe30-jV3xoI=/960x0/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/breed_profile_corgi_1117986_hero_917-6ed2ed41b6e641bb98221b13a1d83a86.jpg", "deepLink": "https://www.google.com/maps", "qgPayload": {"qgToast": "Copied to Clipboard", "qgTextToCopy": "copy123", "myKey": "myValue", "Key of customize key-value pairs if any": "Value of customize key-value pairs if any", "key": "value", "myKey1": "myValue1", "myKey2": "myValue2", "k1": "v1", "k2": "v2", "k3": "v3"}, "message": "description 3", "title": "headline 3"}], "iconImage": "https://s3.amazonaws.com/cdn-origin-etr.akc.org/wp-content/uploads/2017/11/14112506/Pembroke-Welsh-Corgi-standing-outdoors-in-the-fall.jpg"}