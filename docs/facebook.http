# variables
@host = https://{{domain}}
@client_id = ***************
@secret = secret_id=0c234af6e9daa02cdbfa892e000f99b2

###############################################################################
### 取得 facebook access_token
### 使用瀏覽器開啟以下網址，並登入 facebook 帳號
###############################################################################

GET https://graph.facebook.com/oauth/authorize
?response_type=token
&client_id=***************
&redirect_uri=fb***************://authorize
&scope=email,public_profile

# 在瀏覽器 console 會看到以下資訊，複製 access_token
fb***************://authorize/#access_token=EAAHbCrbjQdYBAK7R2D8KFvwCdXGLbnV5mAwp9sFiZCYA3uiXnEWCWO6JMNbp4oTDqUfrzvcrq74bcr6uXpumZARaOQaQFS0qvoB4pSmXtpFjlH2RWpBTctOwICx53ZBRdQlHXAvRdJ2iUjHNELL3tmtyi2Eo86LF239R8zZA1WyjQ6ojbjmtAW2Nt7ZCXdzk7TuO4qiCDpgZDZD&data_access_expiration_time=1696610347&expires_in=4853&long_lived_token=EAAHbCrbjQdYBAIsAPZBJwhyIJwWpYCtTmZCpK5ko2ZBS22QVHOrq9NhhHgJMSdAXamBeHfC8uojGtGACa3u5V08C6JRkkNItR3jC7SywC0USQaYGlhxZBW7ZCg4e6fVEnlIhA1ZBbZC0DSJEOR0LyH9c4BVEI9JIDQrzhJuDaWPdwZDZD

###############################################################################
### 使用 access_token 取得 facebook user_id
###############################################################################
# @prompt access_token

GET https://graph.facebook.com/me
?access_token={{access_token}}


# 回傳結果
{
  "name": "Chen, Chien-Yu",
  "id": "10155612345678901"
}

###############################################################################
### 使用 access_token 取得 facebook friends
###############################################################################
# @prompt access_token

GET https://graph.facebook.com/me/friends
?access_token={{access_token}}

###############################################################################
### 使用 access_token 取得 facebook user_id
###############################################################################
# @prompt access_token

GET https://graph.facebook.com/me/accounts
?access_token={{access_token}}

###############################################################################
### 沒有加上 type, 則是取得 code
###############################################################################

https://www.facebook.com/dialog/oauth
?client_id=***************
&redirect_uri=fb***************://authorize
&scope=email,public_profile

# 回傳結果
fb***************://authorize/?code=AQCVE98LjjW8sZixR8CVI65cLY_kixnACafMbMYwPdoqsBw8wTOEBiqqi0NXJk2vK8_OrMpDsIYMp3dsChTWk9gV-oDRR9QDVUU4w6rvR23lPeqkfIEIx0HUDsiBACMFydA-xopE-A9wjODe2OkIzuETlrKgm_3F8gGpbllPXw2Zf0VDUazx1IZR-39Yb2iijyRGRGS0jxHtAjH10Osd-PuUaGfKElygjBtAfI6mXVQ8Dc28JEefLAXIik50wSJt9FYtGkUdHripRLl0iBn0Q3fhh1173OTzHt9ntRTLpcOMEuavdSyrQn_5UtEyJozzfzHXD5MG8Oa29XAa4l7buUGjhyORb5PSjfqv_msvBCimQnc-PKBn9dVzj1NslI3_03UwyVwyK_HLShe_sPiOOGKNAYjrdacgYhCZ2-Oe_NiPgw#_=_
