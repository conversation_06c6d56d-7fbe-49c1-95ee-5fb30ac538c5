# 宣告
@host = https://webchat-backend.botbonnie.com
@pageId = page-3aa88108fbb24ad2a0b259b4
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzY29wZSI6ImFsbCIsImJvdElkIjoiYm90LW0xSVpGSGoxdCIsImlhdCI6MTY1Nzc4NjA1MiwiaXNzIjoiYm90Ym9ubmllX2NvbnNvbGUifQ.qHGXzebARcBCxDcQdlrwXUKPfgpaVZQkQUHQlpHsKC0
@crmId = 2079880
@crmName = 何諭明

###############################################################################
# 換 code
###############################################################################
POST {{host}}/api/v2/server-api/auth-code HTTP/1.1
Content-Type: application/json
x-api-token: {{token}}

{
    "crmId":"2079880",
    "pageId":"page-3aa88108fbb24ad2a0b259b4",
    "crmName":"何諭明"
}

###############################################################################
# 換 code
###############################################################################
POST {{host}}/api/v2/server-api/auth-code HTTP/1.1
Content-Type: application/x-www-form-urlencoded
x-api-token: {{token}}

crmId={{crmId}}
&pageId={{pageId}}
&crmName={{crmName}}

###############################################################################
# 換 token
###############################################################################

curl -X POST 'https://webchat-backend.botbonnie.com/api/v2/server-api/auth-code' \
-H 'content-type: application/json' \
-H 'x-api-token: {{token}}' \
--data '{
"crmId":"2079880",
"pageId":"page-3aa88108fbb24ad2a0b259b4",
"crmName":"何諭明"
}'