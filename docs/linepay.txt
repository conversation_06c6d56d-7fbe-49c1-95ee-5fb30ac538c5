3
E/FrameEvents(17439): updateAcquireFence: Did not find frame.
I/flutter (17439): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (17439): │ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:136:16)
I/flutter (17439): │ #1   AndroidNavigationDelegate._handleNavigation (package:webview_flutter_android/src/android_webview_controller.dart:1366:73)
I/flutter (17439): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (17439): │ 💡 onNavigationRequest: https://web-pay.line.me/web/payment/wait?transactionReserveId=K3EyVks5QnZyVnd3alNsdUlDS1NkMk54dkxtaHg2Qm02dlVCTVl3RStnWExvTHZabGZYUVJHcXdSY3dzRnZHbg
I/flutter (17439): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (17439): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (17439): │ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:231:16)
I/flutter (17439): │ #1   new AndroidNavigationDelegate.<anonymous closure> (package:webview_flutter_android/src/android_webview_controller.dart:1268:19)
I/flutter (17439): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (17439): │ 💡 onUrlChange: https://web-pay.line.me/web/payment/wait?transactionReserveId=K3EyVks5QnZyVnd3alNsdUlDS1NkMk54dkxtaHg2Qm02dlVCTVl3RStnWExvTHZabGZYUVJHcXdSY3dzRnZHbg
I/flutter (17439): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (17439): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (17439): │ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:136:16)
I/flutter (17439): │ #1   AndroidNavigationDelegate._handleNavigation (package:webview_flutter_android/src/android_webview_controller.dart:1366:73)
I/flutter (17439): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (17439): │ 💡 onNavigationRequest: https://web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=K3EyVks5QnZyVnd3alNsdUlDS1NkMk54dkxtaHg2Qm02dlVCTVl3RStnWExvTHZabGZYUVJHcXdSY3dzRnZHbg
I/flutter (17439): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
E/FrameEvents(17439): updateAcquireFence: Did not find frame.
I/flutter (17439): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (17439): │ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:231:16)
I/flutter (17439): │ #1   new AndroidNavigationDelegate.<anonymous closure> (package:webview_flutter_android/src/android_webview_controller.dart:1268:19)
I/flutter (17439): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (17439): │ 💡 onUrlChange: https://web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=K3EyVks5QnZyVnd3alNsdUlDS1NkMk54dkxtaHg2Qm02dlVCTVl3RStnWExvTHZabGZYUVJHcXdSY3dzRnZHbg
I/flutter (17439): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2
I/chromium(17439): [INFO:CONSOLE(1)] "A parser-blocking, cross site (i.e. different eTLD+1) script, https://scdn.line-apps.com/channel/sdk/js/android/cordova_20150326.js, is invoked via document.write. The network request for this script MAY be blocked by the browser in this or a future page load due to poor network connectivity. If blocked in this page load, it will be confirmed in a subsequent console message. See https://www.chromestatus.com/feature/5718547946799104 for more details.", source: https://scdn.line-apps.com/channel/sdk/js/LCSLoader_20150909.js?@buildVersion@ (1)
I/chromium(17439): [INFO:CONSOLE(1)] "A parser-blocking, cross site (i.e. different eTLD+1) script, https://scdn.line-apps.com/channel/sdk/js/LCS_20150326.js, is invoked via document.write. The network request for this script MAY be blocked by the browser in this or a future page load due to poor network connectivity. If blocked in this page load, it will be confirmed in a subsequent console message. See https://www.chromestatus.com/feature/5718547946799104 for more details.", source: https://scdn.line-apps.com/channel/sdk/js/LCSLoader_20150909.js?@buildVersion@ (1)
26
E/FrameEvents(17439): updateAcquireFence: Did not find frame.
--- 按下付款按鈕後
I/flutter (17439): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (17439): │ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:136:16)
I/flutter (17439): │ #1   AndroidNavigationDelegate._handleNavigation (package:webview_flutter_android/src/android_webview_controller.dart:1366:73)
I/flutter (17439): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (17439): │ 💡 onNavigationRequest: intent://pay/payment/K3EyVks5QnZyVnd3alNsdUlDS1NkMk54dkxtaHg2Qm02dlVCTVl3RStnWExvTHZabGZYUVJHcXdSY3dzRnZHbg#Intent;scheme=line;action=android.intent.action.VIEW;category=android.intent.category.BROWSABLE;package=jp.naver.line.android;end
I/flutter (17439): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (17439): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (17439): │ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:231:16)
I/flutter (17439): │ #1   new AndroidNavigationDelegate.<anonymous closure> (package:webview_flutter_android/src/android_webview_controller.dart:1268:19)
I/flutter (17439): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (17439): │ 💡 onUrlChange: intent://pay/payment/K3EyVks5QnZyVnd3alNsdUlDS1NkMk54dkxtaHg2Qm02dlVCTVl3RStnWExvTHZabGZYUVJHcXdSY3dzRnZHbg#Intent;scheme=line;action=android.intent.action.VIEW;category=android.intent.category.BROWSABLE;package=jp.naver.line.android;end
I/flutter (17439): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2
E/FrameEvents(17439): updateAcquireFence: Did not find frame.

--- ios

┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:150:16)<…>
│ #1   new WebKitNavigationDelegate.<anonymous closure> (package:webview_flutter_wkwebview/src/webkit_webview_controller.dart:995:59)<…>
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
│ 💡 onNavigationRequest: https://web-pay.line.me/web/payment/wait?transactionReserveId=MkRLNzM4Q1cyZjFBMWIvZmdGTjhPb3d5b0I4eEg1RFRvVFhQbFFDTjJvVGVuUFF4QmdOYmpqQjE4SXN0Q2ZMVw<…>
└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:260:16)<…>
│ #1   WebKitWebViewController._webView.<anonymous closure>.<anonymous closure> (package:webview_flutter_wkwebview/src/webkit_webview_controller.dart:299:32)<…>
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
│ 💡 onUrlChange: https://web-pay.line.me/web/payment/wait?transactionReserveId=MkRLNzM4Q1cyZjFBMWIvZmdGTjhPb3d5b0I4eEg1RFRvVFhQbFFDTjJvVGVuUFF4QmdOYmpqQjE4SXN0Q2ZMVw<…>
└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:150:16)<…>
│ #1   new WebKitNavigationDelegate.<anonymous closure> (package:webview_flutter_wkwebview/src/webkit_webview_controller.dart:995:59)<…>
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
│ 💡 onNavigationRequest: https://web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=MkRLNzM4Q1cyZjFBMWIvZmdGTjhPb3d5b0I4eEg1RFRvVFhQbFFDTjJvVGVuUFF4QmdOYmpqQjE4SXN0Q2ZMVw<…>
└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:260:16)<…>
│ #1   WebKitWebViewController._webView.<anonymous closure>.<anonymous closure> (package:webview_flutter_wkwebview/src/webkit_webview_controller.dart:299:32)<…>
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
│ 💡 onUrlChange: https://web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=MkRLNzM4Q1cyZjFBMWIvZmdGTjhPb3d5b0I4eEg1RFRvVFhQbFFDTjJvVGVuUFF4QmdOYmpqQjE4SXN0Q2ZMVw<…>
└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:150:16)<…>
│ #1   new WebKitNavigationDelegate.<anonymous closure> (package:webview_flutter_wkwebview/src/webkit_webview_controller.dart:995:59)<…>
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
│ 💡 onNavigationRequest: line://pay/payment/MkRLNzM4Q1cyZjFBMWIvZmdGTjhPb3d5b0I4eEg1RFRvVFhQbFFDTjJvVGVuUFF4QmdOYmpqQjE4SXN0Q2ZMVw<…>
└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
---
┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ #0   CartWebController._initWebViewController.<anonymous closure> (package:efshop/app/modules/cart_web/controllers/cart_web_controller.dart:150:16)<…>
│ #1   new WebKitNavigationDelegate.<anonymous closure> (package:webview_flutter_wkwebview/src/webkit_webview_controller.dart:995:59)<…>
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
│ 💡 onNavigationRequest: line://pay/payment/MkRLNzM4Q1cyZjFBMWIvZmdGTjhPb3d5b0I4eEg1RFRvVFhQbFFDTjJvVGVuUFF4QmdOYmpqQjE4SXN0Q2ZMVw<…>
└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>

--- ios 2

┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onNavigationRequest: https://web-pay.line.me/web/payment/wait?transactionReserveId=RXVLNTZKQVF6MFZ3K0NFWG9GbnIvSDJYRGRGdzkrczhjZjdsV05RQ3pkcGE1MEh3V3M3aVBoSEYvOXVHZ0dyeA<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onUrlChange: https://web-pay.line.me/web/payment/wait?transactionReserveId=RXVLNTZKQVF6MFZ3K0NFWG9GbnIvSDJYRGRGdzkrczhjZjdsV05RQ3pkcGE1MEh3V3M3aVBoSEYvOXVHZ0dyeA<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onNavigationRequest: https://web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=RXVLNTZKQVF6MFZ3K0NFWG9GbnIvSDJYRGRGdzkrczhjZjdsV05RQ3pkcGE1MEh3V3M3aVBoSEYvOXVHZ0dyeA<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onUrlChange: https://web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=RXVLNTZKQVF6MFZ3K0NFWG9GbnIvSDJYRGRGdzkrczhjZjdsV05RQ3pkcGE1MEh3V3M3aVBoSEYvOXVHZ0dyeA<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onNavigationRequest: line://pay/payment/RXVLNTZKQVF6MFZ3K0NFWG9GbnIvSDJYRGRGdzkrczhjZjdsV05RQ3pkcGE1MEh3V3M3aVBoSEYvOXVHZ0dyeA<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onUrlChange: line://pay/payment/RXVLNTZKQVF6MFZ3K0NFWG9GbnIvSDJYRGRGdzkrczhjZjdsV05RQ3pkcGE1MEh3V3M3aVBoSEYvOXVHZ0dyeA<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onUrlChange: https://web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=RXVLNTZKQVF6MFZ3K0NFWG9GbnIvSDJYRGRGdzkrczhjZjdsV05RQ3pkcGE1MEh3V3M3aVBoSEYvOXVHZ0dyeA<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ uriLinkStream: https://m.efshop.com.tw/CartFinish/fail/7420311?payment_name=LINE+Pay&source=ios<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ allUriLinkStream: https://m.efshop.com.tw/CartFinish/fail/7420311?payment_name=LINE+Pay&source=ios<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ allUriLinkStream: /CartFinish/fail/7420311<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ allUriLinkStream: {payment_name: LINE Pay, source: ios}<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>

--- ios 3

┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onNavigationRequest: https://web-pay.line.me/web/payment/wait?transactionReserveId=aUF2VnAwQmNxRDdBWjkycmdPb0d4UUlTbmNVQ2ZVcFIwT2pPQ3JaUEZ3Zm40RTZyS1JjWlE3STJWMEp3SjN0aA<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onUrlChange: https://web-pay.line.me/web/payment/wait?transactionReserveId=aUF2VnAwQmNxRDdBWjkycmdPb0d4UUlTbmNVQ2ZVcFIwT2pPQ3JaUEZ3Zm40RTZyS1JjWlE3STJWMEp3SjN0aA<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onNavigationRequest: https://web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=aUF2VnAwQmNxRDdBWjkycmdPb0d4UUlTbmNVQ2ZVcFIwT2pPQ3JaUEZ3Zm40RTZyS1JjWlE3STJWMEp3SjN0aA<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ launchUrl: true<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ uriLinkStream: https://m.efshop.com.tw/app/CartFinish/fail/7422788?openExternalBrowser=1<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ allUriLinkStream: https://m.efshop.com.tw/app/CartFinish/fail/7422788?openExternalBrowser=1<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ allUriLinkStream: /app/CartFinish/fail/7422788<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ allUriLinkStream: {openExternalBrowser: 1}<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ [CartView] empty<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
[GETX] Instance "CartController" has been created with tag "797337898"
[GETX] Instance "CartController" with tag "797337898" has been initialized
[GETX] Instance "CartWebController" has been created with tag "797341720"
[GETX] Instance "CartWebController" with tag "797341720" has been initialized
[GETX] Instance "OrderFailController" has been created
[GETX] Instance "OrderFailController" has been initialized
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onUrlChange: https://m.efshop.com.tw/app_access?redirect=cart&client_agent_source=ios&client_agent_source_os_version=Version+17.1.2+%28Build+21B101%29&client_agent_source_app_version=3.0.0<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────
[Talker] ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────
         │ [http-request] [GET] https://api.efshop.com.tw/v1/cart/quantity?
         │ Headers: {
         │   "content-type": "application/x-www-form-urlencoded",
         │   "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************.oLOTkovzY4zmiYsVxFVkZB59WtDuOn7hRVhAif8T-oA",
         │   "accept": "application/json"
         │ }
         └──────────────────────────────────────────────────────────────────────────────────────────────────────────────
[Talker] ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────
         │ [http-request] [GET] https://api.efshop.com.tw/v1/cart/items?
         │ Headers: {
         │   "content-type": "application/x-www-form-urlencoded",
         │   "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************.oLOTkovzY4zmiYsVxFVkZB59WtDuOn7hRVhAif8T-oA",
         │   "accept": "application/json"
         │ }
         └──────────────────────────────────────────────────────────────────────────────────────────────────────────────
[Talker] ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────
         │ [http-request] [GET] https://api.efshop.com.tw/v1/members/orders/7422788?
         │ Headers: {
         │   "content-type": "application/x-www-form-urlencoded",
         │   "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************.oLOTkovzY4zmiYsVxFVkZB59WtDuOn7hRVhAif8T-oA",
         │   "accept": "application/json"
         │ }
         └──────────────────────────────────────────────────────────────────────────────────────────────────────────────
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onNavigationRequest: https://m.efshop.com.tw/app_access?redirect=cart&client_agent_source=ios&client_agent_source_os_version=Version+17.1.2+%28Build+21B101%29&client_agent_source_app_version=3.0.0<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
[Talker] ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────
         │ [http-response] [GET] https://api.efshop.com.tw/v1/cart/quantity?
         │ Status: 200
         │ Message: OK
         └──────────────────────────────────────────────────────────────────────────────────────────────────────────────
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ 頁面不存在<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
[Talker] ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────
         │ [http-request] [GET] https://api.efshop.com.tw/v1/cart/quantity?
         │ Headers: {
         │   "content-type": "application/x-www-form-urlencoded",
         │   "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************.oLOTkovzY4zmiYsVxFVkZB59WtDuOn7hRVhAif8T-oA",
         │   "accept": "application/json"
         │ }
         └──────────────────────────────────────────────────────────────────────────────────────────────────────────────
[Talker] ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────
         │ [http-response] [GET] https://api.efshop.com.tw/v1/members/orders/7422788?
         │ Status: 200
         │ Message: OK
         └──────────────────────────────────────────────────────────────────────────────────────────────────────────────
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ [CartController] _empty<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
[Talker] ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────
         │ [http-response] [GET] https://api.efshop.com.tw/v1/cart/quantity?
         │ Status: 200
         │ Message: OK
         └──────────────────────────────────────────────────────────────────────────────────────────────────────────────
[Talker] ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────
         │ [http-request] [GET] https://api.efshop.com.tw/v1/categories/app/21/20?target_page=1
         │ Headers: {
         │   "content-type": "application/x-www-form-urlencoded",
         │   "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************.oLOTkovzY4zmiYsVxFVkZB59WtDuOn7hRVhAif8T-oA",
         │   "accept": "application/json"
         │ }
         └──────────────────────────────────────────────────────────────────────────────────────────────────────────────
[Talker] ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────
         │ [http-response] [GET] https://api.efshop.com.tw/v1/categories/app/21/20?target_page=1
         │ Status: 200
         │ Message: OK
         └──────────────────────────────────────────────────────────────────────────────────────────────────────────────
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ [CartView] empty<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onUrlChange: https://m.efshop.com.tw/app_access?redirect=cart&client_agent_source=ios&client_agent_source_os_version=Version%2017.1.2%20%28Build%2021B101%29&client_agent_source_app_version=3.0.0<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onNavigationRequest: https://m.efshop.com.tw/showEmptyCart<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onNavigationRequest: https://chat.botbonnie.com/?appId=page-3aa88108fbb24ad2a0b259b4&userId=bb-X-3Mpi5pT5wfun-Kq8DF1&isNewUser=true&redirect=cart&client_agent_source=ios&client_agent_source_os_version=Version%2017.1.2%20%28Build%2021B101%29&client_agent_source_app_version=3.0.0<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
│ onNavigationRequest: https://gum.criteo.com/syncframe?topUrl=m.efshop.com.tw&origin=onetag#%7B%22bundle%22:%7B%22origin%22:3,%22value%22:%22r-0plF81Y01rZUlneVZQWERsaWNrRmlkbldqaDU3b2Vlc0tQaGhkVXV3MXdub3Z3dmR5Zmw5TEtXV3BoMTZrUGhLTG5TMnhxWFI4Y1VWM2E2SUVESHpCUTFZQk5BWkd4aEFvQWlucEZoSXZtVUU1WENBOEk1QkNsc0RrS3Z3QkxEOVQwU05GSWVuV3lzSlRxV3dzRFhUMVp5MTJSVzM0VmM2QmtkMGtldVJnSDF6dVklM0Q%22%7D,%22cw%22:true,%22optout%22:%7B%22origin%22:0,%22value%22:null%7D,%22origin%22:%22onetag%22,%22sid%22:%7B%22origin%22:0,%22value%22:null%7D,%22tld%22:%22efshop.com.tw%22,%22topUrl%22:%22m.efshop.com.tw%22,%22version%22:%225_23_0%22,%22ifa%22:%7B%22origin%22:0,%22value%22:null%7D,%22lsw%22:true,%22pm%22:0%7D<…>
└──────────────────────────────────────────────────────────────────────────────────────────────────────────────
[GETX] Instance "CategoriesController" has been created
[GETX] Instance "CategoriesController" has been initialized
