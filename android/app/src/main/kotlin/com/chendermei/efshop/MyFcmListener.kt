package com.chendermei.efshop

import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.quantumgraph.sdk.QG

class MyFcmListener : FirebaseMessagingService() {
   override fun onMessageReceived(message: RemoteMessage) {
       // If the message is from AIQUA, handleRemoteMessage() returns true
       // and processes it. Otherwise, the method returns false.
       if (QG.getInstance(applicationContext).isAppierPush(message.data)) {
           if (!QG.getInstance(this).handleRemoteMessage(message)) {
               // handle FCM message from other services
           }
       }
   }

   override fun onNewToken(token: String) {
       super.onNewToken(token)
       QG.logFcmId(applicationContext)
       // handle FCM token for other services
   }
}