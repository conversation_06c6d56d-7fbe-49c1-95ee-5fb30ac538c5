package com.chendermei.efshop

import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
//import androidx.activity.result.contract.ActivityResultContracts
//import androidx.core.content.ContextCompat
//import com.google.android.gms.tasks.OnCompleteListener
//import com.google.firebase.messaging.FirebaseMessaging
//import com.quantumgraph.sdk.QG
import io.flutter.embedding.android.FlutterActivity

class MainActivity : FlutterActivity() {
    // override onCreate method in kotlin
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // QG.initializeSdk(application, "d7c31d6283d3ccc28bc8", "498175649962")

        // FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener { task ->
        //     if (!task.isSuccessful) {
        //         return@OnCompleteListener
        //     }

        //     // Log and toast
        //     QG.logFcmId(applicationContext)
        // })
    }

    // Declare the launcher at the top of your Activity/Fragment:
    //  private val requestPermissionLauncher = registerForActivityResult(
    //      ActivityResultContracts.RequestPermission(),
    //  ) { isGranted: Boolean ->
    //      if (isGranted) {
    //          // FCM SDK (and your app) can post notifications.
    //      } else {
    //          // TODO: Inform user that that your app will not show notifications.
    //      }
    //  }

    // private fun askNotificationPermission() {
    //     // This is only necessary for API level >= 33 (TIRAMISU)
    //     if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
    //         if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS) ==
    //             PackageManager.PERMISSION_GRANTED
    //         ) {
    //             // FCM SDK (and your app) can post notifications.
    //         } else if (shouldShowRequestPermissionRationale(android.Manifest.permission.POST_NOTIFICATIONS)) {
    //             // TODO: display an educational UI explaining to the user the features that will be enabled
    //             //       by them granting the POST_NOTIFICATION permission. This UI should provide the user
    //             //       "OK" and "No thanks" buttons. If the user selects "OK," directly request the permission.
    //             //       If the user selects "No thanks," allow the user to continue without notifications.
    //         } else {
    //             // Directly ask for the permission
    //             requestPermissionLauncher.launch(android.Manifest.permission.POST_NOTIFICATIONS)
    //         }
    //     }
    // }
}
