plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id 'com.google.gms.google-services'
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.chendermei.efshop"
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.efshop"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 24
        // minSdkVersion flutter.minSdkVersion
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    signingConfigs {
        prod {
            storeFile file('shangching-release-key.keystore')
            storePassword 'sc073433785'
            keyAlias 'shangching'
            keyPassword 'sc073433785'
            // v1SigningEnabled true
            // v2SigningEnabled true
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            // applicationIdSuffix '.debug'
            // versionNameSuffix '-DEBUG'
        }
        release {
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.prod
        }
    }

    flavorDimensions 'dim'
    productFlavors {
        sandbox {
            dimension 'dim'
            applicationId 'com.efshop.app.efshopandroid'
            resValue 'string', 'facebook_app_id', '522314041213398'
            resValue 'string', 'facebook_app_scheme', 'fb522314041213398'
            resValue 'string', 'facebook_content_provider_authority', 'com.facebook.app.FacebookContentProvider522314041213398'
            resValue 'string', 'facebook_client_token', '********************************'
        }
        production {
            dimension 'dim'
            resValue 'string', 'facebook_app_id', '2243741228996320'
            resValue 'string', 'facebook_app_scheme', 'fb2243741228996320'
            resValue 'string', 'facebook_content_provider_authority', 'com.facebook.app.FacebookContentProvider2243741228996320'
            resValue 'string', 'facebook_client_token', '********************************'
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // using firebase bom
    implementation platform('com.google.firebase:firebase-bom:33.1.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.google.firebase:firebase-iid:21.1.0'
    implementation 'androidx.work:work-runtime-ktx:2.9.0'

    // Appier SDK
    implementation ('com.appier:appier-android:7.24.3') {
        exclude group: 'com.google.firebase'
        exclude group: 'com.google.android.gms'
    }
}
