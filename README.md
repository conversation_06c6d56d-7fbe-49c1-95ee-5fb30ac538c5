# efshop

bundle id: com.efshop.app

## [api doc](http://api.wabow.com:81/)

* 需要 omos 辦公室 vpn

## [建立 mockito 測試](https://flutter.cn/docs/cookbook/testing/unit/mocking)

1. 加入 mockito 套件

```yaml
dev_dependencies:
  mockito: ^5.4.1
  build_runner: ^2.4.4
```

2. 加入 annotation

```dart
@GenerateMocks([ApiProvider, PrefProvider])
```

3. 執行 build_runner

```bash
flutter pub run build_runner build
```

4. 建立攔截器 (when...thenAnswer)

* when...thenAnswer

```dart
when(mockApiProvider.getProducts()).thenAnswer((_) async => products);
```

* when...thenThrow

```dart
when(mockApiProvider.getProducts()).thenThrow(Exception('error'));
```

5. 執行測試

```bash
flutter test
```

## [下載](https://bitbucket.org/umomos/efshop_flutter/wiki/Home)

### ios

* [sandbox](itms-services://?action=download-manifest&url=https://dl.dropboxusercontent.com/s/dzcgraf8i3simgv/sandbox.plist)
* [production](itms-services://?action=download-manifest&url=https://dl.dropboxusercontent.com/s/hspbo8csyis8h2r/production.plist)

## 設計圖

### [雲端硬碟](https://drive.google.com/drive/folders/1aRDWm_vB_NzCGQhnQdfkJQ3J7BX_ElKv?usp=drive_link)

### [iOS](https://xd.adobe.com/view/0761b243-26ce-458f-a74d-352929788fea-5232/)

### [Android](https://xd.adobe.com/view/26eb22c6-b211-4b4b-b5f4-727737a2476f-73b8/)

## [線下設計圖](https://bitbucket.org/Zoe_Omos/efshopapp_art)

## [Appier](https://aiqua.appier.com/)

* [doc](https://docs.google.com/document/d/1n9xAgIoHiKPZW4qL7ZA9SSQWSK6Q_7m7QVehyBDaJQo/edit)
* [backend](https://aiqua.appier.com/)
  * <EMAIL>
  * 14382jrdfu

## [Appier SDK doc](https://docs.aiqua.appier.com/docs/flutter-sdk-integration-overview)

### id

* 測試站 app id: d021cfac392ead2d2f1c
* 正式站 app id: d7c31d6283d3ccc28bc8

## ios 編譯失敗的問題

### [./ios/gem pristine --all](https://github.com/CocoaPods/CocoaPods/issues/10573#issuecomment-1488759030)

### [cocoapods](https://ithelp.ithome.com.tw/articles/10327936)

* openssl
* libffi
* rvm ruby

## store

* [App Store](https://apps.apple.com/tw/app/id1156791229)
* [Google Play](https://play.google.com/store/apps/details?id=com.efshop)

## [pigeon](https://pub.dev/packages/pigeon)

```bash
flutter pub run pigeon --input pigeons/message.dart
```

## GA

### [GA 全部事件列表](https://developers.google.com/analytics/devguides/collection/ga4/reference/events?hl=zh-tw)

### [GA 電子商務事件列表](https://developers.google.com/analytics/devguides/collection/ga4/ecommerce?hl=zh-tw)

### [Firebase 電子商務事件列表](https://firebase.google.com/docs/analytics/measure-ecommerce?hl=zh-tw#dart)

## [Store]

* [App Store](https://apps.apple.com/tw/app/id1156791229)
* [Google Play](https://play.google.com/store/apps/details?id=com.efshop)
