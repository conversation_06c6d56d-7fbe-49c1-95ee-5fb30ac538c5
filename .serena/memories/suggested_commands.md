# 建議指令

這份文件包含專案開發、建置和部署的常用指令。

## 清理專案

在建置前執行，以避免快取問題。
```bash
flutter clean
```

## Android 建置與部署

### Sandbox (沙箱環境)
建置 APK:
```bash
flutter build apk --flavor sandbox
```
上傳到 Dropbox (需進入 `android` 目錄):
```bash
cd android
bundle exec fastlane upload_sandbox_to_dropbox
cd ..
```

### Production (正式環境)
建置 APK:
```bash
flutter build apk --flavor production
```
上傳到 Dropbox (需進入 `android` 目錄):
```bash
cd android
bundle exec fastlane upload_production_to_dropbox
cd ..
```

## iOS 建置與部署

### Sandbox (沙箱環境)
建置 IPA:
```bash
flutter build ipa --flavor=sandbox --export-options-plist=$PWD/ios/sandbox.plist
```
上傳到 Dropbox (需進入 `ios` 目錄):
```bash
cd ios
bundle exec fastlane upload_sandbox_to_dropbox
cd ..
```

### Production (正式環境)
建置 IPA:
```bash
flutter build ipa --flavor=production --export-options-plist=$PWD/ios/production.plist
```
上傳到 Dropbox (需進入 `ios` 目錄):
```bash
cd ios
bundle exec fastlane upload_production_to_dropbox
cd ..
```

### App Store
建置 IPA:
```bash
flutter build ipa --flavor=appstore --export-options-plist=$PWD/ios/appstore.plist
```
上傳到 Dropbox (需進入 `ios` 目錄):
```bash
cd ios
bundle exec fastlane upload_appstore_to_dropbox
cd ..
```
