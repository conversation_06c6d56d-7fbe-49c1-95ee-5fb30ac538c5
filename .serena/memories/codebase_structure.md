# 程式碼庫結構

這是一個標準的 Flutter 專案結構。

## 關鍵目錄與檔案

- **`lib/`**: 應用程式的主要 Dart 程式碼所在地。
  - **`lib/app/modules/`**: 遵循 GetX Pattern，存放不同功能模組的 `views`, `controllers`, 和 `bindings`。
  - **`lib/app/providers/`**: 存放與後端 API 或其他資料來源互動的類別。
  - **`lib/app/routes/`**: 定義應用程式的路由。
- **`android/` 和 `ios/`**: 平台特定的原生程式碼。
- **`pigeons/`**: 包含 `pigeon` 套件的設定檔，用於生成 Flutter 與原生平台之間的通訊介面。
- **`pubspec.yaml`**: 專案的核心設定檔，定義依賴、版本和資源。
- **`analysis_options.yaml`**: 程式碼靜態分析與 Linting 規則的設定檔。
- **`deploy.py`, `deploy_android.py`, `deploy_ios.py`**: 用於自動化建置和部署的 Python 腳本。
- **`fastlane/` (在 `android` 和 `ios` 目錄內)**: Fastlane 的設定檔，用於自動化部署流程，如上傳到 Dropbox 或 TestFlight。
