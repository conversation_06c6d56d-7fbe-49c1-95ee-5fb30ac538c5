# EFShop 專案概覽

這是一個使用 Flutter 開發的電子商務應用程式，名稱為 `efshop`。

## 主要技術堆疊

- **框架:** Flutter
- **狀態管理:** GetX (get)
- **網路請求:** Dio
- **本地儲存:**
  - GetStorage (鍵值對)
  - Hive (輕量級資料庫)
  - ObjectBox (物件導向 NoSQL 資料庫)
- **後端服務整合:** Firebase (Analytics, Crashlytics, Messaging)
- **第三方登入:** Facebook, Line, Apple
- **原生通訊:** Pigeon
- **日誌與監控:** Talker, Firebase Crashlytics
- **開發工具:** build_runner, flutter_lints
- **自動化:** Fastlane (用於部署)
