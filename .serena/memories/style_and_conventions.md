# 程式碼風格與慣例

本專案遵循 Flutter 官方的標準程式碼風格。

## Linting 規則

專案的 `analysis_options.yaml` 檔案中包含了 `package:flutter_lints/flutter.yaml`。這表示專案採用了 `flutter_lints` 套件提供的預設規則集。

開發時應遵循此規則集提供的所有建議，以確保程式碼的一致性和品質。

## 主要慣例

- **命名:**
  - 變數、函式、檔名使用 `camelCase` (例如 `myVariable`, `myFunction.dart`)。
  - 類別、型別、擴充功能使用 `PascalCase` (例如 `MyClass`, `MyThemeExtension`)。
- **狀態管理:**
  - 專案主要使用 GetX (get) 進行狀態管理、依賴注入和路由。開發新功能時應遵循 GetX 的模式 (Modules, Views, Controllers, Bindings)。
