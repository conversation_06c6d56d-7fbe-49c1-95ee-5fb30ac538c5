import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart';

void main() {
  // test 2024-03-12T03:26:59.299Z to date time
  test('test 2024-03-12T03:26:59.299Z to date time', () {
    final date = DateTime.parse('2024-03-12T03:26:59.299Z');
    expect(date, DateTime.utc(2024, 3, 12, 3, 26, 59, 299));
  });

  // test date time 2024-03-12T03:26:59.299Z to local string 2024-03-12 11:26
  test(
      'test date time 2024-03-12T03:26:59.299Z to local string 2024-03-12 11:26',
      () {
    final utcDate = DateTime.parse('2024-03-12T03:26:59.299Z');
    final local = utcDate.toLocal();
    final actual = DateFormat('yyyy-MM-dd HH:mm').format(local);
    expect(actual, '2024-03-12 11:26');
  });
  // test 2030-08-31 11:36:00 string to date time
  test('test 2030-08-31 11:36:00 string to date time', () {
    final date = DateTime.parse('2030-08-31 11:36:00');
    expect(date, DateTime(2030, 8, 31, 11, 36));
  });
}
