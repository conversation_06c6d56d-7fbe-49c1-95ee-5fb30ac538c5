import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('RegExp', () {
    test('RegExp', () {
      const str =
          '<iframe id="ytplayer" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" src="https://www.youtube.com/embed/Jrm_kRabTi8?rel=0&amp;showinfo=0" frameborder="0" allowfullscreen=""></iframe>';
      final regex = RegExp(r'https://www\.youtube\.com\/embed\/([^"]*)');
      // final match = regExp.firstMatch(str);
      final matches = regex.allMatches(str);
      for (final match in matches) {
        final url = match.group(0);
        if (url != null) {
          print(url);
          final uri = Uri.parse(url);
          // 增加 query
          final uri2 = uri.replace(queryParameters: {
            ...uri.queryParameters,
            'autoplay': '1',
            'muted': '1',
          });
          print(uri2.toString());
        }
      }
    });

    /// 讀入 HTML 字串，找出所有的 <td> 元素
    /// https://regex101.com/r/1Z8Z8v/1
    /// <td[^>]*>(.*?)<\/td>
    test('td', () {
      const input =
          '<ul class="events_area"><li><table border="0" width="100%" cellspacing="0" cellpadding="0"><tbody><tr><td align="center"><a href="https://m.efshop.com.tw/product/107759"><img src="https://vefshop.wimg.tw/web/240329-women-JA.jpg" alt="" border="0"></a></td></tr></tbody></table></li></ul>';
      final regex = RegExp(r'<td[^>]*>(.*?)<\/td>');
      final matches = regex.allMatches(input);
      for (RegExpMatch match in matches) {
        for (int i = 0; i <= match.groupCount; i++) {
          if (i == 0) continue; // skip group(0)
          final matchString = match.group(i);
          if (matchString != null) {
            expect(matchString,
                '<a href="https://m.efshop.com.tw/product/107759"><img src="https://vefshop.wimg.tw/web/240329-women-JA.jpg" alt="" border="0"></a>');
            // debugPrint(matchString);
            break;
          }
        }
      }
    });

    /// 讀入 HTML 字串，找出所有的 <a> 元素
    /// https://regex101.com/r/1Z8Z8v/2
    /// <a[^>]*>(.*?)<\/a>
    /// <a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>
    test('href', () {
      const input =
          '<td align="center"><a href="https://m.efshop.com.tw/product/107759"><img src="https://vefshop.wimg.tw/web/240329-women-JA.jpg" alt="" border="0"></a></td>';
      // final regex = RegExp(r'<a[^>]*>(.*?)<\/a>');
      final regex = RegExp(r'<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>');
      final matches = regex.allMatches(input);
      for (RegExpMatch match in matches) {
        for (int i = 0; i <= match.groupCount; i++) {
          if (i == 0) continue; // skip group(0)
          final matchString = match.group(i);
          if (matchString != null) {
            expect(matchString, 'https://m.efshop.com.tw/product/107759');
            break;
          }
        }
      }
    });
  });

  /// 讀入 HTML 字串，找出 img 的 src
  /// https://regex101.com/r/1Z8Z8v/3
  /// <img[^>]*src="([^"]*)"[^>]*>
  test('img', () {
    const input =
        '<td align="center"><a href="https://m.efshop.com.tw/product/107759"><img src="https://vefshop.wimg.tw/web/240329-women-JA.jpg" alt="" border="0"></a></td>';
    final regex = RegExp(r'<img[^>]*src="([^"]*)"[^>]*>');
    final matches = regex.allMatches(input);
    for (RegExpMatch match in matches) {
      for (int i = 0; i <= match.groupCount; i++) {
        if (i == 0) continue; // skip group(0)
        final matchString = match.group(i);
        if (matchString != null) {
          expect(
              matchString, 'https://vefshop.wimg.tw/web/240329-women-JA.jpg');
          break;
        }
      }
    }
  });

  /// 讀入 HTML 字串，找出 color
  /// https://regex101.com/r/1Z8Z8v/4
  /// color:([^;]*);
  test('color', () {
    const input = '<font color=#ff0200><strong>+℃ 新暖感</strong></font>';
    final regex = RegExp(r'<font[^>]*color=(#[a-fA-F0-9]{6})[^>]*>');
    // final regex = RegExp(r'color=([^;]*);');
    final matches = regex.allMatches(input);
    for (RegExpMatch match in matches) {
      for (int i = 0; i <= match.groupCount; i++) {
        if (i == 0) continue; // skip group(0)
        final matchString = match.group(i);
        if (matchString != null) {
          expect(matchString, '#ff0200');
          break;
        }
      }
    }
  });

  /// 讀入 HTML 字串，找出 strong 元素
  /// https://regex101.com/r/1Z8Z8v/5
  /// <strong>(.*?)<\/strong>
  test('strong', () {
    const input = '<font color=#ff0200><strong>+℃ 新暖感</strong></font>';
    final regex = RegExp(r'<strong>(.*?)<\/strong>');
    final matches = regex.allMatches(input);
    for (RegExpMatch match in matches) {
      for (int i = 0; i <= match.groupCount; i++) {
        if (i == 0) continue; // skip group(0)
        final matchString = match.group(i);
        if (matchString != null) {
          expect(matchString, '+℃ 新暖感');
          break;
        }
      }
    }
  });
}
