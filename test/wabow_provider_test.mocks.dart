// Mocks generated by Mockito 5.4.1 from annotations
// in efshop/test/wabow_provider_test.dart.
// Do not manually edit this file.

// @dart=2.19

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i18;
import 'dart:ui' as _i22;

import 'package:device_info_plus/device_info_plus.dart' as _i3;
import 'package:dio/src/adapter.dart' as _i8;
import 'package:dio/src/cancel_token.dart' as _i20;
import 'package:dio/src/dio.dart' as _i19;
import 'package:dio/src/dio_mixin.dart' as _i10;
import 'package:dio/src/options.dart' as _i7;
import 'package:dio/src/response.dart' as _i11;
import 'package:dio/src/transformer.dart' as _i9;
import 'package:efshop/app/models/login_res.dart' as _i17;
import 'package:efshop/app/models/members_messages_unread_res.dart' as _i6;
import 'package:efshop/app/providers/box_provider.dart' as _i5;
import 'package:efshop/app/providers/pref_provider.dart' as _i16;
import 'package:flutter/widgets.dart' as _i23;
import 'package:get/get.dart' as _i15;
import 'package:get_storage/get_storage.dart' as _i14;
import 'package:hive/hive.dart' as _i12;
import 'package:logger/logger.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i21;
import 'package:objectbox/objectbox.dart' as _i13;
import 'package:package_info_plus/package_info_plus.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLogger_0 extends _i1.SmartFake implements _i2.Logger {
  _FakeLogger_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDeviceInfoPlugin_1 extends _i1.SmartFake
    implements _i3.DeviceInfoPlugin {
  _FakeDeviceInfoPlugin_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePackageInfo_2 extends _i1.SmartFake implements _i4.PackageInfo {
  _FakePackageInfo_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBoxProvider_3 extends _i1.SmartFake implements _i5.BoxProvider {
  _FakeBoxProvider_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMembersMessagesUnreadRes_4 extends _i1.SmartFake
    implements _i6.MembersMessagesUnreadRes {
  _FakeMembersMessagesUnreadRes_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBaseOptions_5 extends _i1.SmartFake implements _i7.BaseOptions {
  _FakeBaseOptions_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeHttpClientAdapter_6 extends _i1.SmartFake
    implements _i8.HttpClientAdapter {
  _FakeHttpClientAdapter_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTransformer_7 extends _i1.SmartFake implements _i9.Transformer {
  _FakeTransformer_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeInterceptors_8 extends _i1.SmartFake implements _i10.Interceptors {
  _FakeInterceptors_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeResponse_9<T1> extends _i1.SmartFake implements _i11.Response<T1> {
  _FakeResponse_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBox_10<E> extends _i1.SmartFake implements _i12.Box<E> {
  _FakeBox_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStore_11 extends _i1.SmartFake implements _i13.Store {
  _FakeStore_11(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLazyBox_12<E1> extends _i1.SmartFake implements _i12.LazyBox<E1> {
  _FakeLazyBox_12(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGetStorage_13 extends _i1.SmartFake implements _i14.GetStorage {
  _FakeGetStorage_13(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMicrotask_14 extends _i1.SmartFake implements _i14.Microtask {
  _FakeMicrotask_14(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGetQueue_15 extends _i1.SmartFake implements _i15.GetQueue {
  _FakeGetQueue_15(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeValueStorage_16<T> extends _i1.SmartFake
    implements _i14.ValueStorage<T> {
  _FakeValueStorage_16(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [PrefProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockPrefProvider extends _i1.Mock implements _i16.PrefProvider {
  MockPrefProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.Logger get logger => (super.noSuchMethod(
        Invocation.getter(#logger),
        returnValue: _FakeLogger_0(
          this,
          Invocation.getter(#logger),
        ),
      ) as _i2.Logger);
  @override
  _i3.DeviceInfoPlugin get deviceInfo => (super.noSuchMethod(
        Invocation.getter(#deviceInfo),
        returnValue: _FakeDeviceInfoPlugin_1(
          this,
          Invocation.getter(#deviceInfo),
        ),
      ) as _i3.DeviceInfoPlugin);
  @override
  _i4.PackageInfo get packageInfo => (super.noSuchMethod(
        Invocation.getter(#packageInfo),
        returnValue: _FakePackageInfo_2(
          this,
          Invocation.getter(#packageInfo),
        ),
      ) as _i4.PackageInfo);
  @override
  _i5.BoxProvider get boxProvider => (super.noSuchMethod(
        Invocation.getter(#boxProvider),
        returnValue: _FakeBoxProvider_3(
          this,
          Invocation.getter(#boxProvider),
        ),
      ) as _i5.BoxProvider);
  @override
  bool get isSandBox => (super.noSuchMethod(
        Invocation.getter(#isSandBox),
        returnValue: false,
      ) as bool);
  @override
  String get host => (super.noSuchMethod(
        Invocation.getter(#host),
        returnValue: '',
      ) as String);
  @override
  String get appierKey => (super.noSuchMethod(
        Invocation.getter(#appierKey),
        returnValue: '',
      ) as String);
  @override
  set fcmToken(String? value) => super.noSuchMethod(
        Invocation.setter(
          #fcmToken,
          value,
        ),
        returnValueForMissingStub: null,
      );
  @override
  String get fcmToken => (super.noSuchMethod(
        Invocation.getter(#fcmToken),
        returnValue: '',
      ) as String);
  @override
  set appierAuthCode(String? value) => super.noSuchMethod(
        Invocation.setter(
          #appierAuthCode,
          value,
        ),
        returnValueForMissingStub: null,
      );
  @override
  String get appierAuthCode => (super.noSuchMethod(
        Invocation.getter(#appierAuthCode),
        returnValue: '',
      ) as String);
  @override
  set loginRes(_i17.LoginRes? value) => super.noSuchMethod(
        Invocation.setter(
          #loginRes,
          value,
        ),
        returnValueForMissingStub: null,
      );
  @override
  bool get isLogin => (super.noSuchMethod(
        Invocation.getter(#isLogin),
        returnValue: false,
      ) as bool);
  @override
  bool get isNotLogin => (super.noSuchMethod(
        Invocation.getter(#isNotLogin),
        returnValue: false,
      ) as bool);
  @override
  String get token => (super.noSuchMethod(
        Invocation.getter(#token),
        returnValue: '',
      ) as String);
  @override
  _i18.Stream<String> get tokenStream => (super.noSuchMethod(
        Invocation.getter(#tokenStream),
        returnValue: _i18.Stream<String>.empty(),
      ) as _i18.Stream<String>);
  @override
  int get cartQuantity => (super.noSuchMethod(
        Invocation.getter(#cartQuantity),
        returnValue: 0,
      ) as int);
  @override
  set cartQuantity(int? value) => super.noSuchMethod(
        Invocation.setter(
          #cartQuantity,
          value,
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i18.Stream<num> get cartQuantityStream => (super.noSuchMethod(
        Invocation.getter(#cartQuantityStream),
        returnValue: _i18.Stream<num>.empty(),
      ) as _i18.Stream<num>);
  @override
  _i6.MembersMessagesUnreadRes get unreadRes => (super.noSuchMethod(
        Invocation.getter(#unreadRes),
        returnValue: _FakeMembersMessagesUnreadRes_4(
          this,
          Invocation.getter(#unreadRes),
        ),
      ) as _i6.MembersMessagesUnreadRes);
  @override
  set unreadRes(_i6.MembersMessagesUnreadRes? value) => super.noSuchMethod(
        Invocation.setter(
          #unreadRes,
          value,
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i18.Future<void> printInfo() => (super.noSuchMethod(
        Invocation.method(
          #printInfo,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);
}

/// A class which mocks [Dio].
///
/// See the documentation for Mockito's code generation for more information.
class MockDio extends _i1.Mock implements _i19.Dio {
  MockDio() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.BaseOptions get options => (super.noSuchMethod(
        Invocation.getter(#options),
        returnValue: _FakeBaseOptions_5(
          this,
          Invocation.getter(#options),
        ),
      ) as _i7.BaseOptions);
  @override
  set options(_i7.BaseOptions? _options) => super.noSuchMethod(
        Invocation.setter(
          #options,
          _options,
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i8.HttpClientAdapter get httpClientAdapter => (super.noSuchMethod(
        Invocation.getter(#httpClientAdapter),
        returnValue: _FakeHttpClientAdapter_6(
          this,
          Invocation.getter(#httpClientAdapter),
        ),
      ) as _i8.HttpClientAdapter);
  @override
  set httpClientAdapter(_i8.HttpClientAdapter? _httpClientAdapter) =>
      super.noSuchMethod(
        Invocation.setter(
          #httpClientAdapter,
          _httpClientAdapter,
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i9.Transformer get transformer => (super.noSuchMethod(
        Invocation.getter(#transformer),
        returnValue: _FakeTransformer_7(
          this,
          Invocation.getter(#transformer),
        ),
      ) as _i9.Transformer);
  @override
  set transformer(_i9.Transformer? _transformer) => super.noSuchMethod(
        Invocation.setter(
          #transformer,
          _transformer,
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i10.Interceptors get interceptors => (super.noSuchMethod(
        Invocation.getter(#interceptors),
        returnValue: _FakeInterceptors_8(
          this,
          Invocation.getter(#interceptors),
        ),
      ) as _i10.Interceptors);
  @override
  void close({bool? force = false}) => super.noSuchMethod(
        Invocation.method(
          #close,
          [],
          {#force: force},
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i18.Future<_i11.Response<T>> get<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
    _i7.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #get,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #get,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> getUri<T>(
    Uri? uri, {
    Object? data,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
    _i7.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #getUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> post<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
    _i7.ProgressCallback? onSendProgress,
    _i7.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #post,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #post,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> postUri<T>(
    Uri? uri, {
    Object? data,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
    _i7.ProgressCallback? onSendProgress,
    _i7.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #postUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #postUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> put<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
    _i7.ProgressCallback? onSendProgress,
    _i7.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #put,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #put,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> putUri<T>(
    Uri? uri, {
    Object? data,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
    _i7.ProgressCallback? onSendProgress,
    _i7.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #putUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #putUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> head<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #head,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #head,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> headUri<T>(
    Uri? uri, {
    Object? data,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #headUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #headUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> delete<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #delete,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> deleteUri<T>(
    Uri? uri, {
    Object? data,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #deleteUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> patch<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
    _i7.ProgressCallback? onSendProgress,
    _i7.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #patch,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #patch,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> patchUri<T>(
    Uri? uri, {
    Object? data,
    _i7.Options? options,
    _i20.CancelToken? cancelToken,
    _i7.ProgressCallback? onSendProgress,
    _i7.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #patchUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #patchUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<dynamic>> download(
    String? urlPath,
    dynamic savePath, {
    _i7.ProgressCallback? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    _i20.CancelToken? cancelToken,
    bool? deleteOnError = true,
    String? lengthHeader = r'content-length',
    Object? data,
    _i7.Options? options,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #download,
          [
            urlPath,
            savePath,
          ],
          {
            #onReceiveProgress: onReceiveProgress,
            #queryParameters: queryParameters,
            #cancelToken: cancelToken,
            #deleteOnError: deleteOnError,
            #lengthHeader: lengthHeader,
            #data: data,
            #options: options,
          },
        ),
        returnValue:
            _i18.Future<_i11.Response<dynamic>>.value(_FakeResponse_9<dynamic>(
          this,
          Invocation.method(
            #download,
            [
              urlPath,
              savePath,
            ],
            {
              #onReceiveProgress: onReceiveProgress,
              #queryParameters: queryParameters,
              #cancelToken: cancelToken,
              #deleteOnError: deleteOnError,
              #lengthHeader: lengthHeader,
              #data: data,
              #options: options,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<dynamic>>);
  @override
  _i18.Future<_i11.Response<dynamic>> downloadUri(
    Uri? uri,
    dynamic savePath, {
    _i7.ProgressCallback? onReceiveProgress,
    _i20.CancelToken? cancelToken,
    bool? deleteOnError = true,
    String? lengthHeader = r'content-length',
    Object? data,
    _i7.Options? options,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadUri,
          [
            uri,
            savePath,
          ],
          {
            #onReceiveProgress: onReceiveProgress,
            #cancelToken: cancelToken,
            #deleteOnError: deleteOnError,
            #lengthHeader: lengthHeader,
            #data: data,
            #options: options,
          },
        ),
        returnValue:
            _i18.Future<_i11.Response<dynamic>>.value(_FakeResponse_9<dynamic>(
          this,
          Invocation.method(
            #downloadUri,
            [
              uri,
              savePath,
            ],
            {
              #onReceiveProgress: onReceiveProgress,
              #cancelToken: cancelToken,
              #deleteOnError: deleteOnError,
              #lengthHeader: lengthHeader,
              #data: data,
              #options: options,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<dynamic>>);
  @override
  _i18.Future<_i11.Response<T>> request<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i20.CancelToken? cancelToken,
    _i7.Options? options,
    _i7.ProgressCallback? onSendProgress,
    _i7.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #request,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #cancelToken: cancelToken,
            #options: options,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #request,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #cancelToken: cancelToken,
              #options: options,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> requestUri<T>(
    Uri? uri, {
    Object? data,
    _i20.CancelToken? cancelToken,
    _i7.Options? options,
    _i7.ProgressCallback? onSendProgress,
    _i7.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #requestUri,
          [uri],
          {
            #data: data,
            #cancelToken: cancelToken,
            #options: options,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #requestUri,
            [uri],
            {
              #data: data,
              #cancelToken: cancelToken,
              #options: options,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
  @override
  _i18.Future<_i11.Response<T>> fetch<T>(_i7.RequestOptions? requestOptions) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetch,
          [requestOptions],
        ),
        returnValue: _i18.Future<_i11.Response<T>>.value(_FakeResponse_9<T>(
          this,
          Invocation.method(
            #fetch,
            [requestOptions],
          ),
        )),
      ) as _i18.Future<_i11.Response<T>>);
}

/// A class which mocks [BoxProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockBoxProvider extends _i1.Mock implements _i5.BoxProvider {
  MockBoxProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i12.Box<dynamic> get defaultBox => (super.noSuchMethod(
        Invocation.getter(#defaultBox),
        returnValue: _FakeBox_10<dynamic>(
          this,
          Invocation.getter(#defaultBox),
        ),
      ) as _i12.Box<dynamic>);
  @override
  set namespace(String? value) => super.noSuchMethod(
        Invocation.setter(
          #namespace,
          value,
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i13.Store get store => (super.noSuchMethod(
        Invocation.getter(#store),
        returnValue: _FakeStore_11(
          this,
          Invocation.getter(#store),
        ),
      ) as _i13.Store);
  @override
  set domain(String? value) => super.noSuchMethod(
        Invocation.setter(
          #domain,
          value,
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i18.Future<_i12.Box<E>> getHiveBox<E>(String? name) => (super.noSuchMethod(
        Invocation.method(
          #getHiveBox,
          [name],
        ),
        returnValue: _i18.Future<_i12.Box<E>>.value(_FakeBox_10<E>(
          this,
          Invocation.method(
            #getHiveBox,
            [name],
          ),
        )),
      ) as _i18.Future<_i12.Box<E>>);
  @override
  _i18.Future<_i12.LazyBox<E>> getHiveLazyBox<E>(String? name) =>
      (super.noSuchMethod(
        Invocation.method(
          #getHiveLazyBox,
          [name],
        ),
        returnValue: _i18.Future<_i12.LazyBox<E>>.value(_FakeLazyBox_12<E>(
          this,
          Invocation.method(
            #getHiveLazyBox,
            [name],
          ),
        )),
      ) as _i18.Future<_i12.LazyBox<E>>);
  @override
  _i18.Future<bool> initGsBox(String? name) => (super.noSuchMethod(
        Invocation.method(
          #initGsBox,
          [name],
        ),
        returnValue: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);
  @override
  _i14.GetStorage getGsBox(
    String? name, {
    bool? withNamespace = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getGsBox,
          [name],
          {#withNamespace: withNamespace},
        ),
        returnValue: _FakeGetStorage_13(
          this,
          Invocation.method(
            #getGsBox,
            [name],
            {#withNamespace: withNamespace},
          ),
        ),
      ) as _i14.GetStorage);
}

/// A class which mocks [GetStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetStorage extends _i1.Mock implements _i14.GetStorage {
  MockGetStorage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i14.Microtask get microtask => (super.noSuchMethod(
        Invocation.getter(#microtask),
        returnValue: _FakeMicrotask_14(
          this,
          Invocation.getter(#microtask),
        ),
      ) as _i14.Microtask);
  @override
  _i15.GetQueue get queue => (super.noSuchMethod(
        Invocation.getter(#queue),
        returnValue: _FakeGetQueue_15(
          this,
          Invocation.getter(#queue),
        ),
      ) as _i15.GetQueue);
  @override
  set queue(_i15.GetQueue? _queue) => super.noSuchMethod(
        Invocation.setter(
          #queue,
          _queue,
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i18.Future<bool> get initStorage => (super.noSuchMethod(
        Invocation.getter(#initStorage),
        returnValue: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);
  @override
  set initStorage(_i18.Future<bool>? _initStorage) => super.noSuchMethod(
        Invocation.setter(
          #initStorage,
          _initStorage,
        ),
        returnValueForMissingStub: null,
      );
  @override
  Map<String, dynamic> get changes => (super.noSuchMethod(
        Invocation.getter(#changes),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);
  @override
  _i14.ValueStorage<Map<String, dynamic>> get listenable => (super.noSuchMethod(
        Invocation.getter(#listenable),
        returnValue: _FakeValueStorage_16<Map<String, dynamic>>(
          this,
          Invocation.getter(#listenable),
        ),
      ) as _i14.ValueStorage<Map<String, dynamic>>);
  @override
  T? read<T>(String? key) => (super.noSuchMethod(Invocation.method(
        #read,
        [key],
      )) as T?);
  @override
  T getKeys<T>() => (super.noSuchMethod(
        Invocation.method(
          #getKeys,
          [],
        ),
        returnValue: _i21.dummyValue<T>(
          this,
          Invocation.method(
            #getKeys,
            [],
          ),
        ),
      ) as T);
  @override
  T getValues<T>() => (super.noSuchMethod(
        Invocation.method(
          #getValues,
          [],
        ),
        returnValue: _i21.dummyValue<T>(
          this,
          Invocation.method(
            #getValues,
            [],
          ),
        ),
      ) as T);
  @override
  bool hasData(String? key) => (super.noSuchMethod(
        Invocation.method(
          #hasData,
          [key],
        ),
        returnValue: false,
      ) as bool);
  @override
  _i22.VoidCallback listen(_i22.VoidCallback? value) => (super.noSuchMethod(
        Invocation.method(
          #listen,
          [value],
        ),
        returnValue: () {},
      ) as _i22.VoidCallback);
  @override
  _i22.VoidCallback listenKey(
    String? key,
    _i23.ValueSetter<dynamic>? callback,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #listenKey,
          [
            key,
            callback,
          ],
        ),
        returnValue: () {},
      ) as _i22.VoidCallback);
  @override
  _i18.Future<void> write(
    String? key,
    dynamic value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #write,
          [
            key,
            value,
          ],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);
  @override
  void writeInMemory(
    String? key,
    dynamic value,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #writeInMemory,
          [
            key,
            value,
          ],
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i18.Future<void> writeIfNull(
    String? key,
    dynamic value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #writeIfNull,
          [
            key,
            value,
          ],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);
  @override
  _i18.Future<void> remove(String? key) => (super.noSuchMethod(
        Invocation.method(
          #remove,
          [key],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);
  @override
  _i18.Future<void> erase() => (super.noSuchMethod(
        Invocation.method(
          #erase,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);
  @override
  _i18.Future<void> save() => (super.noSuchMethod(
        Invocation.method(
          #save,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);
}
