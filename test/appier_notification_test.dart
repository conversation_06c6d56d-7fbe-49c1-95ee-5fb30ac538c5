import 'package:efshop/app/models/appier_notification.dart';
import 'package:efshop/extension.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('timestamp', () {
    final appierNotification = AppierNotification(
      qgTimestamp: "2023-09-21T23:48:11",
      qgts: "1694769363",
    );

    test('androidTimestamp', () {
      final actual = appierNotification.androidTimestamp;
      expect(actual, DateTime(2023, 9, 21, 23, 48, 11));
    });

    test('iosTimestamp', () {
      final actual = appierNotification.iosTimestamp;
      expect(actual, DateTime(2023, 9, 15, 17, 16, 3));
    });
  });
}
