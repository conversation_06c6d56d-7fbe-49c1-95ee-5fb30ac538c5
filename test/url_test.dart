import 'package:flutter_test/flutter_test.dart';

void main() {
  test('url fail', () {
    const url =
        'https://m.efshop.com.tw/CartFinish/fail/7278177?payment_name=%E4%BF%A1%E7%94%A8%E5%8D%A1%E4%BB%98%E6%AC%BE&source=android';
    final uri = Uri.parse(url);
    expect(uri.path, '/CartFinish/fail/7278177');
  });

  test('url success', () {
    const url =
        'https://m.efshop.com.tw/CartFinish/success/7278195?payment_name=%E4%BF%A1%E7%94%A8%E5%8D%A1%E4%BB%98%E6%AC%BE&source=android';
    final uri = Uri.parse(url);
    expect(uri.path, '/CartFinish/success/7278195');
    uri.pathSegments.forEach((element) {
      print(element);
    });
  });

  group('content_main', () {
    test('1045', () {
      final uri = Uri.parse('1045');
      expect(uri.path, '1045');
      expect(uri.pathSegments.first, '1045');
    });
    test('1045/display', () {
      final uri = Uri.parse('1045/display');
      expect(uri.path, '1045/display');
      expect(uri.pathSegments.first, '1045');
    });
  });
}
