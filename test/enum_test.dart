import 'package:efshop/enums.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MessageType', () {
    test('name', () {
      expect(MessageType.activities.display, '優惠活動');
      expect(MessageType.orders.display, '通知消息');
      expect(MessageType.ships.display, '物流通知');
      expect(MessageType.questions.display, '客服紀錄');
    });

    test('identifier', () {
      expect(MessageType.activities.value, 'activities');
      expect(MessageType.orders.value, 'orders');
      expect(MessageType.ships.value, 'ships');
      expect(MessageType.questions.value, 'questions');
    });
  });
}
