import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:efshop/app/models/checkout_post_req.dart';
import 'package:efshop/app/models/checkout_post_res.dart';
import 'package:efshop/app/models/checkout_res.dart';
import 'package:efshop/app/models/configs_res.dart';
import 'package:efshop/app/models/error_res.dart';
import 'package:efshop/app/models/index_module_app_res.dart';
import 'package:efshop/app/models/login_res.dart';
import 'package:efshop/app/models/members_addresses_post_req.dart';
import 'package:efshop/app/models/members_feedbacks_req.dart';
import 'package:efshop/app/models/members_messages_preference_res.dart';
import 'package:efshop/app/models/members_messages_unread_res.dart';
import 'package:efshop/app/models/members_my_bonus_res.dart';
import 'package:efshop/app/models/members_my_favorite_post_req.dart';
import 'package:efshop/app/models/members_my_favorite_put_res.dart';
import 'package:efshop/app/models/members_my_favorite_res.dart';
import 'package:efshop/app/models/members_orders_invoices_res.dart';
import 'package:efshop/app/models/members_orders_questions_post_req.dart';
import 'package:efshop/app/models/members_orders_refund_post_req.dart';
import 'package:efshop/app/models/members_orders_refund_res.dart';
import 'package:efshop/app/models/members_orders_res.dart';
import 'package:efshop/app/models/members_password_patch_req.dart';
import 'package:efshop/app/models/members_popup_res.dart';
import 'package:efshop/app/models/members_preorders_res.dart';
import 'package:efshop/app/models/members_profile_patch_req.dart';
import 'package:efshop/app/models/members_profile_patch_res.dart';
import 'package:efshop/app/models/members_profile_res.dart';
import 'package:efshop/app/models/members_refund_post_req.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/models/payment_options_res.dart';
import 'package:efshop/app/models/products_preorder_post_res.dart';
import 'package:efshop/app/models/register_req.dart';
import 'package:efshop/app/providers/api_provider.dart';
import 'package:efshop/app/providers/appier_provider.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import 'package:logger/logger.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'wabow_provider_test.mocks.dart';

@GenerateMocks([
  PrefProvider,
  Dio,
  BoxProvider,
  GetStorage,
])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  //
  final dio = Dio(BaseOptions());
  final dioAdapter = DioAdapter(dio: dio, matcher: const UrlRequestMatcher());
  late WabowProvider wabow;
  late ErrorRes error;
  final mockPrefProvider = MockPrefProvider();
  final mockBoxProvider = MockBoxProvider();
  final mockBox = MockGetStorage();
  late ApiProvider apiProvider;
  // final apiProvider = ApiProvider(dio: dio, prefProvider: mockPrefProvider);
  late AppierProvider appierProvider;

  setUpAll(() {
    print('setUpAll');
    when(mockPrefProvider.host).thenReturn('');
    when(mockPrefProvider.token).thenReturn('');
    when(mockPrefProvider.logger).thenReturn(Logger());
    when(mockPrefProvider.boxProvider).thenReturn(mockBoxProvider);
    when(mockBoxProvider.getGsBox(any,
            withNamespace: anyNamed('withNamespace')))
        .thenAnswer((realInvocation) => mockBox);
    // 使用 mockito 建立假的 apiProvider
    apiProvider = ApiProvider(dio: dio, prefProvider: mockPrefProvider);
    appierProvider = AppierProvider(apiProvider: apiProvider);
    error = ErrorRes(status: false);
  });
  setUp(() {
    print('setUp');
    // 每次執行職都重置 wabowProvider
    // 建立 wabowProvider 並傳入假的 apiProvider
    wabow =
        WabowProvider(apiProvider: apiProvider, appierProvider: appierProvider);
  });
  group('check email exist', () {
    test('register check', () async {
      const email = '<EMAIL>';
      // 當呼叫 dio.post 時，回傳 true
      dioAdapter.onPost(
        'https:v1/register/check',
        (server) {
          server.reply(
            200,
            {
              'status': true,
            },
          );
        },
        queryParameters: {},
        data: FormData.fromMap({
          'email': email,
        }),
      );
      // 呼叫 registerCheck，並驗證回傳值為 true，因為 api.post 被 mockito 攔截
      final actual = await wabow.registerCheck(email);
      expect(actual, true);
    });

    test('returns false when email is invalid', () async {
      const email = 'invalid-email';
      // 當呼叫 api.post 時，回傳 false
      dioAdapter.onPost(
        'https:v1/register/check',
        (server) {
          server.reply(
            200,
            {
              'email': email,
            },
          );
        },
      );
      // when(mockApiProvider.post(
      //   any,
      //   any,
      //   data: {
      //     'email': email,
      //   },
      // )).thenThrow(error);
      // 建立 wabowProvider 並傳入假的 apiProvider
      // wabow = WabowProvider(
      //     apiProvider: mockApiProvider, appierProvider: appierProvider);
      // 呼叫 registerCheck，並驗證回傳值為 true，因為 api.post 被 mockito 攔截
      final actual = await wabow.registerCheck(email);
      expect(actual, false);
    });
  });

  group('註冊', () {
    const email = '';
    const password = '';
    const fullname = '';
    final args = RegisterReq(
      email: email,
      password: password,
      fullname: fullname,
    );
    test('成功', () async {
      // 當呼叫 api.post 時，回傳 LoginRes 結構
      dioAdapter.onPost(
        'https:v1/register',
        (server) {
          server.reply(
            200,
            {
              "payload": {
                "iat": **********,
                "exp": **********,
                "jti": "5de720d22540c401aa5c6a31e63b20a7",
                "for": "authentication",
                "session": "113bb93cada1d17eec64e16a4e5f4e9d",
                "userid": "1265996",
                "email": "<EMAIL>",
                "fullname": "<EMAIL>",
                "provider": "local"
              },
              "token":
                  "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************.k3Q5ZIRng0AKmksriby85boEycF-NVKNRATVZAmlYOA"
            },
          );
        },
        queryParameters: {},
        data: FormData.fromMap({
          'email': email,
          'password': password,
          'fullname': fullname,
        }),
      );
      final actual = await wabow.register(RegisterReq(
        email: email,
        password: password,
        fullname: fullname,
      ));
      expect(actual, isA<LoginRes>());
    });
    test('失敗', () async {
      // 當呼叫 api.post 時，回傳 LoginRes 結構
      // when(mockApiProvider.post(
      //   any,
      //   any,
      //   data: {
      //     'email': email,
      //     'password': password,
      //     'fullname': fullname,
      //   },
      // )).thenThrow(error);
      // 建立 wabowProvider 並傳入假的 apiProvider
      // wabow = WabowProvider(
      //     apiProvider: mockApiProvider, appierProvider: appierProvider);
      dioAdapter.onPost(
        'https:v1/register',
        (server) {
          server.reply(
            200,
            {
              "status": false,
              "message": "Email already exists.",
            },
          );
        },
      );
      expect(
        () => wabow.register(args),
        throwsA(isA<ErrorRes>()),
      );
    });
  });

  group('忘記密碼', () {
    const email = '';
    test('成功', () async {
      dioAdapter.onGet(
        'https:v1/login/reset/password?email',
        (server) {
          server.reply(
            200,
            {
              "status": true,
              "message": "Sending notification successed.",
            },
          );
        },
      );
      final actual = await wabow.forgetPassword(email);
      expect(actual.status, true);
    });
    test('失敗', () {
      // when(mockApiProvider.getResData(
      //   any,
      //   any,
      //   filter: {
      //     'email': email,
      //   },
      // )).thenThrow(error);
      // 建立 wabowProvider 並傳入假的 apiProvider
      // wabow = WabowProvider(
      //     apiProvider: mockApiProvider, appierProvider: appierProvider);
      dioAdapter.onGet(
        'https:v1/login/reset/password?email',
        (server) {
          server.reply(
            200,
            error,
          );
        },
      );
      expect(
        () => wabow.forgetPassword(email),
        throwsA(isA<ErrorRes>()),
      );
    });
  });

  group('超商', () {
    test('取得有 7-11 的所有縣市資料', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/store_seven_eleven_cities_res.json');
      final jsonObject = jsonDecode(jsonString);
      // 使用 MockDio 的方式
      // when(dio.getUri<Map<String, dynamic>>(any)).thenAnswer((_) async {
      //   return Response(
      //     requestOptions:
      //         RequestOptions(method: 'GET', path: 'store/seven_eleven/cities'),
      //     data: jsonObject,
      //   );
      // });
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/store/seven_eleven/cities?',
        (server) => server.reply(
          200,
          jsonDecode(jsonString),
          // Reply would wait for one-sec before returning data.
          // delay: const Duration(seconds: 1),
        ),
      );
      final actual = await wabow.getStoreSevenElevenCities();
      expect(actual, jsonObject);
    });

    test('取得有 7-11 的所有鄉鎮資料', () async {
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/store/seven_eleven/towns?',
        (server) => server.reply(
          200,
          [
            {
              "name": "台北市",
              "data": [
                {
                  "code": "100",
                  "name": "中正區",
                },
              ]
            },
            {
              "name": "基隆市",
              "data": [
                {
                  "code": "200",
                  "name": "仁愛區",
                },
              ],
            },
          ],
        ),
      );
      final res = await wabow.getStoreSevenElevenTowns();
      expect(res, isA<Iterable>());
    });

    test('取得某區域的所有 7-11 店家資料', () async {
      const zipCode = '100';
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/store/seven_eleven/zip_code/$zipCode?',
        (server) => server.reply(
          200,
          {
            "中山南路": [
              {
                "name": "台北市",
                "data": [
                  {
                    "code": "100",
                    "name": "中正區",
                  },
                ]
              },
              {
                "name": "基隆市",
                "data": [
                  {
                    "code": "200",
                    "name": "仁愛區",
                  },
                ],
              },
            ],
          },
        ),
      );
      final res = await wabow.getStoreSevenElevenWithZip(zipCode);
      expect(res, isA<Map>());
    });

    test('取得有全家的所有縣市資料', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/store_family_mart_cities_res.json');
      final jsonObject = jsonDecode(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/store/family_mart/cities?',
        (server) => server.reply(200, jsonObject),
      );
      final actual = await wabow.getStoreFamilyMartCities();
      expect(jsonEncode(actual), jsonEncode(jsonObject));
    });
  });

  group('產品搜尋', () {
    test('產品搜尋', () async {
      // TODO: implement
    });
  });

  group('產品標籤搜尋', () {
    test('產品標籤搜尋', () async {
      // TODO: implement
    });
  });

  group('熱門關鍵字', () {
    test('筆數由後台設定', () async {
      // TODO: implement
    });
  });

  group('首頁廣告', () {
    test('index', () async {
      // TODO: implement
    });

    test('首頁廣告中 - 期間限定資料', () async {
      // TODO: implement
    });
  });

  group('分類廣告', () {
    test('分類廣告', () async {
      // TODO: implement
    });
  });

  group('靜態頁面', () {
    test('靜態頁面', () async {
      // TODO: implement
    });
  });

  group('搜尋欄關聯字詞', () {
    test('搜尋欄關聯字詞', () async {
      // TODO: implement
    });
  });

  group('產品資料', () {
    test('取得巢狀的兩層類別列表', () async {
      // TODO: implement
    });
    test('取得新版巢狀類別列表', () async {
      // TODO: implement
    });
    test('取得單一類別的所有產品', () async {
      // TODO: implement
    });
    test('取得單一類別的所有產品', () async {
      // TODO: implement
    });
    test('取得單一類別的 seo', () async {
      // TODO: implement
    });
    test('取得類別商品', () async {
      // TODO: implement
    });
    test('取得單一產品資料', () async {
      // TODO: implement
    });
    test('取得某產品的同系列產品資料', () async {
      // TODO: implement
    });
    test('取得某產品的評價資料', () async {
      // TODO: implement
    });
    test('取得某產品的搭配購產品資料', () async {
      // TODO: implement
    });
    test('取得同質產品的搭配購產品資料', () async {
      // TODO: implement
    });
  });

  group('購物車', () {
    test('新增商品到購物車', () async {
      // TODO: implement
    });
    test('更新商品到購物車', () async {
      // TODO: implement
    });
    test('點重新購買時，購物車內若已有相同項目則直接取代，確保與訂單項目相同，不累計；也不刪除其他品項', () async {
      // TODO: implement
    });
    test('刪除購物車商品', () async {
      // TODO: implement
    });
    test('新增多筆加價購商品到購物車', () async {
      // TODO: implement
    });
    test('查看購物車商品數量', () async {
      // TODO: implement
    });
    test('cart 與 checkout 頁面取得購物車包含商品與總價等計算的詳細資訊', () async {
      // TODO: implement
    });
  });

  group('結帳流程', () {
    test('設定本次結帳使用紅利點數', () async {
      // TODO: implement
    });
    test('設定本次結帳使用付款方式', () async {
      // TODO: implement
    });
    test('設定本次結帳使用的折扣', () async {
      // TODO: implement
    });
  });

  group('裝置ID', () {
    test('新增一筆裝置ID', () async {
      // TODO: implement
    });
  });

  group('常見問題', () {
    test('取得常見問題', () async {
      // TODO: implement
    });
  });

  group('任選館', () {
    test('所有任選', () async {
      // TODO: implement
    });
    test('單一任選', () async {
      // TODO: implement
    });
  });

  group('加價購', () {
    test('加價購所有商品', () async {
      // TODO: implement
    });
    test('所有加價購活動資訊', () async {
      // TODO: implement
    });
  });

  group('訂單', () {
    test('取得指定會員的發票證明聯', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_orders_invoices_res.json');
      final matcher = MembersOrdersInvoicesRes.fromRawJson(jsonString);
      const id = '1234567890';
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/orders/$id/invoices?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersOrdersInvoices(id);
      expect(res.toJson(), matcher.toJson());
    });

    test('取得所有訂單', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/members_orders_res.json');
      final matcher = jsonDecode(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/orders?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersOrders();
      final actual = res.map((e) => e.toJson()).toList(growable: false);
      final expected = List.from(matcher)
          .map((e) => MembersOrdersRes.fromJson(e))
          .map((e) => e.toJson())
          .toList(growable: false);
      expect(actual, expected);
    });

    test('取得所有訂單狀態數量', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_orders_count_res.json');
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/orders/count?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersOrdersCount();
      expect(res, jsonDecode(jsonString));
    });

    test('取得一筆訂單', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/members_orders_id_res.json');
      const id = '1234567890';
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/orders/$id?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersOrdersWithId(id);
      expect(res.toJson(), MembersOrdersRes.fromRawJson(jsonString).toJson());
    });

    test('訂單取消', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_orders_cancel_res.json');
      const id = '1234567890';
      // 使用 Dio adapter 的方式
      dioAdapter.onPatch(
        'https:v1/members/orders/$id/cancel',
        (server) => server.reply(200, jsonDecode(jsonString)),
        // data: FormData.fromMap({}),
      );
      dioAdapter.onGet(
        'https:v1/members/orders/$id?',
        (server) {
          server.reply(200, jsonDecode('{}'));
        },
      );
      final res = await wabow.postMembersOrdersCancel(id);
      expect(res.toJson(), jsonDecode(jsonString));
    });

    test('取得特定訂單的詢問記錄', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_orders_questions_res.json');
      final matcher = jsonDecode(jsonString);
      const id = '1234567890';
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/orders/$id/questions?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersOrdersQuestions(id);
      expect(jsonEncode(res.toList()), jsonEncode(matcher));
    });

    test('新增一筆特定訂單的詢問記錄', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_orders_questions_post_res.json');
      const id = '1234567890';
      // 使用 Dio adapter 的方式
      dioAdapter.onPost(
        'https:v1/members/orders/$id/questions',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.postMembersOrdersQuestions(
          id, MembersOrdersQuestionsPostReq());
      expect(res.toJson(), jsonDecode(jsonString));
    });

    test('取得物流記錄', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_orders_ships_res.json');
      final jsonObject = jsonDecode(jsonString);
      const id = '1234567890';
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/orders/$id/ships?',
        (server) => server.reply(200, jsonObject),
      );
      final res = await wabow.getMembersOrdersShips(id);
      expect(jsonEncode(res.toList()), jsonEncode(jsonObject));
    });

    test('取得退貨物流', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_orders_ships_refunds_res.json');
      final jsonObject = jsonDecode(jsonString);
      const id = '1234567890';
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/orders/$id/ships/refunds?',
        (server) => server.reply(200, jsonObject),
      );
      final res = await wabow.getMembersOrdersShipsRefunds(id);
      expect(jsonEncode(res.toList()), jsonEncode(jsonObject));
    });

    test('取得退貨訊息', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_orders_refund_res.json');
      final matcher = MembersOrdersRefundRes.fromRawJson(jsonString);
      const id = '1234567890';
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/orders/$id/refund?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersOrdersRefundMessages(id);
      expect(res.toJson(), matcher.toJson());
    });

    test('新增退貨 (使用者退貨資料 id 或 銀行代碼+名稱+戶名+帳號 擇一必填)', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_refund_post_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      const id = '1234567890';
      // 使用 Dio adapter 的方式
      dioAdapter.onPost(
        'https:v1/members/orders/$id/refund',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res =
          await wabow.postMembersOrdersRefund(id, MembersOrdersRefundPostReq());
      expect(res.toJson(), matcher.toJson());
    });

    test('取得訂單商品評價', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_orders_comments_res.json');
      final jsonObject = jsonDecode(jsonString);
      const id = '1234567890';
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/orders/$id/comments?',
        (server) => server.reply(200, jsonObject),
      );
      final res = await wabow.getMembersOrdersComments(id);
      // expect(jsonEncode(res.toList()), jsonEncode(jsonObject));
      expect(res, isA<Iterable>());
    });

    test('新增多筆訂單商品評價', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_orders_comments_post_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      const id = '1234567890';
      // 使用 Dio adapter 的方式
      dioAdapter.onPost(
        'https:v1/members/orders/$id/comments',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({'comments': '[]'}),
      );
      final res = await wabow.postMembersOrdersComments(id, Iterable.empty());
      expect(res.toJson(), matcher.toJson());
    });
  });

  group('意見回饋', () {
    test('新增', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/members_feedbacks_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onPost(
        'https:v1/members/feedbacks',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.postFeedback(MembersFeedbacksReq());
      expect(res.toJson(), matcher.toJson());
    });
  });

  group('個人資料', () {
    test('取得個人資料', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/members_profile_res.json');
      final matcher = MembersProfileRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/profile?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersProfile();
      expect(res.toJson(), matcher.toJson());
    });

    test('更新個人資料', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_profile_patch_res.json');
      final matcher = MembersProfilePatchRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onPatch(
        'https:v1/members/profile',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.patchMembersProfile(MembersProfilePatchReq());
      expect(res.toJson(), matcher.toJson());
    });

    test('變更密碼', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_password_patch_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onPatch(
        'https:v1/members/password',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.patchMembersPassword(MembersPasswordPatchReq());
      expect(res.toJson(), matcher.toJson());
    });
  });

  group('個人地址管理', () {
    test('取得個人的所有地址', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/members_addresses_res.json');
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/addresses?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersAddresses();
      expect(res, isA<Iterable>());
    });

    test('刪除一筆個人地址', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_addresses_delete_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      const input = '123';
      dioAdapter.onDelete(
        'https:v1/members/addresses/$input',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.deleteMembersAddresses(input);
      expect(res.toJson(), matcher.toJson());
    });

    test('新增一組個人地址', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_addresses_post_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      const input = AddressType.home;
      dioAdapter.onPost(
        'https:v1/members/addresses/${input.value}',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res =
          await wabow.postMembersAddresses(input, MembersAddressesPostReq());
      expect(res.toJson(), matcher.toJson());
    });

    test('更新一筆個人的地址，不傳入的欄位表示不更新', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_addresses_patch_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      const id = '123';
      const type = AddressType.home;
      dioAdapter.onPatch(
        'https:v1/members/addresses/${type.value}/$id',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.patchMembersAddresses(
          type, id, MembersAddressesPostReq());
      expect(res.toJson(), matcher.toJson());
    });
  });

  group('個人退貨資料管理', () {
    test('取得個人的所有退貨資料', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/members_refund_res.json');
      final matcher = jsonDecode(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/refund?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersRefund();
      expect(jsonEncode(res.toList()), jsonEncode(matcher));
    });

    test('新增一組個人退貨資料', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_refund_post_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onPost(
        'https:v1/members/refund',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.postMembersRefund(MembersRefundPostReq());
      expect(res.toJson(), matcher.toJson());
    });

    test('更新一筆個人的退貨資料，不傳入的欄位表示不更新', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_refund_post_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      const input = "123";
      // 使用 Dio adapter 的方式
      dioAdapter.onPatch(
        'https:v1/members/refund/$input',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.patchMembersRefund(input, MembersRefundPostReq());
      expect(res.toJson(), matcher.toJson());
    });

    test('刪除一筆個人退貨資料', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_refund_post_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      const input = "123";
      // 使用 Dio adapter 的方式
      dioAdapter.onDelete(
        'https:v1/members/refund/$input',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.deleteMembersRefund(input);
      expect(res.toJson(), matcher.toJson());
    });
  });

  group('Preorder 到貨通知', () {
    test('新增 Preorder 貨到通知', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/products_preorder_post_res.json');
      final matcher = ProductsPreorderPostRes.fromRawJson(jsonString);
      const productId = '123';
      const email = '';
      // 使用 Dio adapter 的方式
      dioAdapter.onPost(
        'https:v1/products/$productId/preorder',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({'email': email}),
      );
      final res = await wabow.postMemberPreorders(productId, email);
      expect(res.toJson(), matcher.toJson());
    });

    test('取得 Preorder 貨到通知', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/members_preorders_res.json');
      final jsonObject = jsonDecode(jsonString);
      final matcher = List.from(jsonObject ?? [])
          .map((e) => MembersPreordersRes.fromJson(e));
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/preorders?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMemberPreorders();
      expect(res, isA<Iterable>());
    });

    test('刪除該使用者的所有 Preorder 貨到通知', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_preorders_delete_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onDelete(
        'https:v1/members/preorders',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.deleteMemberPreorders();
      expect(res.toJson(), matcher.toJson());
    });

    test('刪除一筆貨到通知', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_preorders_id_delete_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      const id = '123';
      // 使用 Dio adapter 的方式
      dioAdapter.onDelete(
        'https:v1/members/preorders/$id',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.deleteMemberPreorder(id);
      expect(res.toJson(), matcher.toJson());
    });
  });

  test('修改此使用者的推播偏好設定', () async {
    final jsonString = await rootBundle
        .loadString('docs/models/members_messages_preference_res.json');
    final matcher = MembersMessagesPreferenceRes.fromRawJson(jsonString);
    const input = SwitchStatus.on;
    // 使用 Dio adapter 的方式
    dioAdapter.onPost(
      'https:v1/members/messages/preference/${input.value}',
      (server) => server.reply(200, jsonDecode(jsonString)),
      data: FormData.fromMap({}),
    );
    final res = await wabow.postMembersMessagesPreference(input);
    expect(res.toJson(), matcher.toJson());
  });

  group('訊息中心', () {
    test('客服紀錄', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_messages_questions_res.json');
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/messages/questions?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersMessagesQuestions();
      expect(res, isA<Iterable>());
    });

    test('優惠活動 (推播)', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_messages_activities_res.json');
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/messages/activities?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersMessagesActivities();
      expect(res, isA<Iterable>());
    });

    test('未登入優惠活動 (推播)', () async {
      final jsonString = await rootBundle.loadString(
          'docs/models/members_messages_activities_nologin_res.json');
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/messages/activities/nologin?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersMessagesActivitiesNologin();
      expect(res, isA<Iterable>());
    });

    test('通知消息 (訂單狀態改變)', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_messages_orders_res.json');
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/messages/orders?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersMessagesOrders();
      expect(res, isA<Iterable>());
    });

    test('物流通知 (推播)', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_messages_ships_res.json');
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/messages/ships?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersMessagesShips();
      expect(res, isA<Iterable>());
    });

    test('取得訊息中心摘要', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_messages_summarys_res.json');
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/messages/summarys?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersMessagesSummarys();
      expect(res, isA<Iterable>());
    });

    test('優惠活動清空 (DB 標記為前端不可讀取)', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_messages_activities_clear_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onPatch(
        'https:v1/members/messages/activities/clear',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.patchMembersMessagesActivitiesClear();
      expect(res.toJson(), matcher.toJson());
    });

    test('推播已讀取', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_messages_read_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      const input = MessageType.activities;
      // 使用 Dio adapter 的方式
      dioAdapter.onPut(
        'https:v1/members/messages/read/${input.value}',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.putMembersMessagesRead(input);
      expect(res.toJson(), matcher.toJson());
    });

    test('推播未讀取數量', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_messages_unread_res.json');
      final matcher = MembersMessagesUnreadRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/messages/unread?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersMessagesUnread();
      expect(res.toJson(), matcher.toJson());
    });
  });

  group('搜尋紀錄', () {
    test('取得搜尋紀錄', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_search_histories_get_res.json');
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/search_histories?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersSearchHistories();
      expect(res, jsonDecode(jsonString));
    });

    test('新增搜尋紀錄', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_search_histories_post_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      const input = '';
      // 使用 Dio adapter 的方式
      dioAdapter.onPost(
        'https:v1/members/search_histories',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({'keyword': input}),
      );
      final res = await wabow.postMembersSearchHistories(input);
      expect(res.toRawJson(), matcher.toRawJson());
    });

    test('刪除該使用者的所有搜尋紀錄', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_search_histories_delete_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onDelete(
        'https:v1/members/search_histories',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final res = await wabow.deleteMembersSearchHistories();
      expect(res.toRawJson(), matcher.toRawJson());
    });
  });

  group('我的收藏', () {
    test('取得我的收藏', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_my_favorite_res.json');
      final matcher = MembersMyFavoriteRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/my_favorite?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersMyFavorite();
      expect(res.toRawJson(), matcher.toRawJson());
    });

    test('刪除所有收藏', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_my_favorite_delete_res.json');
      final matcher = MembersMyFavoriteRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/my_favorite?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersMyFavorite();
      expect(res.toRawJson(), matcher.toRawJson());
    });

    test('同步', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_my_favorite_put_res.json');
      final matcher = MembersMyFavoritePutRes.fromRawJson(jsonString);
      final input = <String, String>{};
      // 使用 Dio adapter 的方式
      dioAdapter.onPut(
        'https:v1/members/my_favorite',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({'client_items': jsonEncode(input)}),
      );
      final res = await wabow.putMembersMyFavorite(input);
      expect(res.toRawJson(), matcher.toRawJson());
    });

    test('新增', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_my_favorite_put_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      final inputString = await rootBundle
          .loadString('docs/models/members_my_favorite_post_req.json');
      final input = MembersMyFavoritePostReq.fromRawJson(inputString);
      // 使用 Dio adapter 的方式
      dioAdapter.onPost(
        'https:v1/members/my_favorite',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap(input.toJson()),
      );
      final res = await wabow.postMembersMyFavorite(input);
      expect(res.toRawJson(), matcher.toRawJson());
    });

    test('取得收藏數量', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_my_favorite_count_res.json');
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/my_favorite/count?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final actual = await wabow.getMembersMyFavoriteCount();
      expect(actual, 1);
    });

    test('取得是否為收藏商品', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_my_favorite_id_res.json');
      const input = 'a';
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/my_favorite/$input?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final actual = await wabow.isMembersMyFavorite(input);
      expect(actual, true);
    });

    test('移除', () async {
      final jsonString = await rootBundle
          .loadString('docs/models/members_my_favorite_delete_res.json');
      final matcher = MessageRes.fromRawJson(jsonString);
      const input = 'a';
      // 使用 Dio adapter 的方式
      dioAdapter.onDelete(
        'https:v1/members/my_favorite/$input',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap({}),
      );
      final actual = await wabow.deleteMembersMyFavoriteWithId(input);
      expect(actual.toJson(), matcher.toJson());
    });
  });

  group('紅利點數', () {
    test('取得會員紅利點數資料', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/members_my_bonus_res.json');
      final matcher = MembersMyBonusRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/my_bonus?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final actual = await wabow.getMembersMyBonus();
      expect(actual.toRawJson(), matcher.toRawJson());
    });
  });

  group('設定', () {
    test('取得所有設定值', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/configs_res.json');
      final jsonObject = jsonDecode(jsonString);
      final matcher = ConfigsRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/configs?',
        (server) => server.reply(200, jsonObject),
      );
      final res = await wabow.getConfigs();
      expect(res.toRawJson(), matcher.toRawJson());
    });
  });

  group('結帳', () {
    test('取結帳前預覽資訊', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/checkout_res.json');
      final matcher = CheckoutRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/checkout?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final actual = await wabow.getCheckout();
      expect(actual.toRawJson(), matcher.toRawJson());
    });

    test('post 結帳表單', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/checkout_post_res.json');
      final matcher = CheckoutPostRes.fromRawJson(jsonString);
      final jsonStringReq =
          await rootBundle.loadString('docs/models/checkout_post_req.json');
      final data = CheckoutPostReq.fromRawJson(jsonStringReq);
      // 使用 Dio adapter 的方式
      dioAdapter.onPost(
        'https:v1/checkout',
        (server) => server.reply(200, jsonDecode(jsonString)),
        data: FormData.fromMap(data.toJson()),
      );
      final actual = await wabow.postCheckout(data);
      expect(actual.toRawJson(), matcher.toRawJson());
    });
  });

  group('付款方式選擇頁', () {
    test('付款方式選擇頁', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/payment_options_res.json');
      final matcher = PaymentOptionsRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/payment/options?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final actual = await wabow.getPaymentOptions();
      expect(actual.toRawJson(), matcher.toRawJson());
    });
  });

  test('首頁模組', () async {
    final jsonString =
        await rootBundle.loadString('docs/models/index_module_app_res.json');
    // final matcher = IndexModuleApp.fromRawJson(jsonString);
    final jsonObject = jsonDecode(jsonString);
    // final matcher = IndexModuleApp.fromJson(jsonObject);
    // 使用 Dio adapter 的方式
    dioAdapter.onGet(
      'https:v1/index/module/app?',
      (server) => server.reply(200, jsonObject),
    );
    final actual = await wabow.getIndexModuleApp();
    expect(actual, isA<Map<String, IndexModuleAppRes>>());
  });

  group('彈窗', () {
    test('取得會員彈窗資料', () async {
      // test: getMembersPopup
      final jsonString =
          await rootBundle.loadString('docs/models/members_popup_res.json');
      final matcher = MembersPopupRes.fromRawJson(jsonString);
      // 使用 Dio adapter 的方式
      dioAdapter.onGet(
        'https:v1/members/popup/app?',
        (server) => server.reply(200, jsonDecode(jsonString)),
      );
      final res = await wabow.getMembersPopup();
      expect(res.toJson(), matcher.toJson());
    });
    test('彈窗完成', () async {
      // Arrange
      const id = 1234567890;
      dioAdapter.onPut(
        'https:v1/members/popup/$id',
        (server) => server.reply(200, jsonDecode('{}')),
        data: FormData.fromMap({}),
      );
      // Act
      final res = await wabow.putMembersPopup(id);
      // Assert
      expect(res, isA<MessageRes>());
    });
  });
}
