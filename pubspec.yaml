name: efshop
version: 3.1.21+391
publish_to: none
description: A new Flutter project.
environment: 
  sdk: '>=3.1.0 <4.0.0'

dependencies: 
  cupertino_icons: ^1.0.8
  get: 4.6.6
  dio: ^5.4.3+1
  pretty_dio_logger: ^1.3.1
  get_storage: ^2.1.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  logger: ^2.3.0
  device_info_plus: ^10.1.0
  package_info_plus: ^8.0.0
  # network_info_plus: ^5.0.3
  objectbox: ^4.0.1
  stream_transform: ^2.1.0
  # shared_preferences: ^2.1.1
  url_launcher: ^6.3.1
  url_launcher_ios: ^6.3.1
  # uni_links: ^0.5.1
  app_links: ^6.1.3
  image_picker: ^1.1.2
  path_provider: ^2.1.3
  path: ^1.8.3
  flutter_local_notifications: ^17.2.3
  # permission_handler: ^10.2.0
  webview_flutter: ^4.10.0
  webview_flutter_android: ^4.0.0
  webview_flutter_wkwebview: ^3.13.1
  webview_flutter_platform_interface: ^2.10.0
  # flutter_webview_plugin: ^0.4.0
  # flutter_inappwebview: ^6.0.0
  # android_intent_plus: ^5.0.2
  video_player: ^2.9.0
  intl: ^0.19.0
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.4.0
  extended_image: ^8.2.1
  # flutter_native_splash: ^2.3.0
  # flutter_slidable: ^3.0.0
  carousel_slider: ^5.0.0
  # badges: ^3.1.2
  # rxdart: ^0.28.0
  # camera: ^0.10.5+2
  jwt_decoder: ^2.0.1
  # in_app_purchase: ^3.1.7
  # timeline_tile: ^2.0.0
  # isar: ^3.1.0+1
  sizer: ^3.0.0
  # retrofit: ^4.0.1
  # flutter_dotenv: ^5.1.0
  firebase_core: ^3.2.0
  firebase_analytics: ^11.1.0
  firebase_crashlytics: ^4.3.5
  firebase_messaging: ^15.2.5
  # firebase_performance: ^0.10.0+1
  # firebase_in_app_messaging: ^0.7.4+8
  # firebase_dynamic_links: ^5.4.17
  # oauth2: ^2.0.2
  flutter_web_auth: ^0.5.0
  flutter_facebook_auth: ^7.0.1
  # oauth_webauth: ^4.0.1+2
  flutter_line_sdk: 2.5.2
  sign_in_with_apple: ^7.0.1
  fluttertoast: ^8.2.6
  # flutter_html: ^2.2.1
  # share_plus: ^9.0.0
  # qr_flutter: ^4.1.0
  barcode: ^2.2.8
  appier_flutter: ^2.7.0
  dots_indicator: ^3.0.0
  # flutter_animate: ^4.5.0
  # animations: ^2.0.11
  # flutter_share_me: ^1.4.0
  flutter_share_me:
    git:
      url: https://github.com/umomos/flutter_share_me.git
      ref: 4b13fa6e2a4677c504293926bd0259d2b94e8fa7
  # social_share: ^2.3.1
  flutter_staggered_grid_view: ^0.7.0
  talker_flutter: 4.5.4
  talker_dio_logger: 4.5.4
  # better_player: ^0.0.83
  # chewie: ^1.8.1
  webview_cookie_manager: ^2.0.6
  objectid: ^3.0.0
  # riverpod: ^2.5.1
  # fpdart: ^1.1.0
  # flutter_hooks: ^0.20.5
  flutter_app_badger: ^1.5.0
  image_cropper: ^8.0.2
  image_picker_android: ^0.8.12+18
  image_picker_platform_interface: ^2.10.0
  flutter_widget_from_html: ^0.16.0
  # flutter_localization: ^0.3.1
  flutter: 
    sdk: flutter

dev_dependencies: 
  flutter_lints: 5.0.0
  mockito: ^5.4.4
  http_mock_adapter: ^0.6.1
  build_runner: ^2.4.13
  pigeon: ^22.5.0
  flutter_test: 
    sdk: flutter
  flutter_localizations:
    sdk: flutter

flutter: 
  uses-material-design: true

  assets:
    - assets/images/
    # - docs/models/
    - docs/xca.csv
