import os

from deploy_base import execute, flutter_clean

IOS_ACCOUNT = "<EMAIL>"
IOS_PASSWORD = "dejd-yyue-sidu-artm"
IPA_FILE = "build/ios/ipa/production.ipa"

CMD_BUILD_APPSORE_IPA = "flutter build ipa --flavor=appstore --export-options-plist=$PWD/ios/appstore.plist"
CMD_BUILD_PRODUCTION_IPA = "flutter build ipa --flavor=production --export-options-plist=$PWD/ios/production.plist"
CMD_BUILD_SANDBOX_IPA = (
    "flutter build ipa --flavor=sandbox --export-options-plist=$PWD/ios/sandbox.plist"
)
CMD_UPLOAD_TO_TEST_FLIGHT = (
    f"xcrun altool --upload-app -t ios -f {IPA_FILE} -u {IOS_ACCOUNT} -p {IOS_PASSWORD}"
)
CMD_UPLOAD_SANDBOX_TO_DROPBOX = "bundle exec fastlane upload_sandbox_to_dropbox"
CMD_UPLOAD_PRODUCTION_TO_DROPBOX = "bundle exec fastlane upload_production_to_dropbox"
CMD_UPLOAD_APPSTORE_TO_DROPBOX = "bundle exec fastlane upload_appstore_to_dropbox"
CMD_UPLOAD_DEBUG_SYMBOL = "bundle exec fastlane upload_debug_symbol"


def deploy_production():
    execute(CMD_BUILD_PRODUCTION_IPA)
    # execute(CMD_UPLOAD_TO_TEST_FLIGHT)
    os.chdir("./ios")
    execute(CMD_UPLOAD_PRODUCTION_TO_DROPBOX)
    # execute(CMD_UPLOAD_DEBUG_SYMBOL)
    os.chdir("..")


def deploy_appstore():
    execute(CMD_BUILD_APPSORE_IPA)
    # execute(CMD_UPLOAD_TO_TEST_FLIGHT)
    os.chdir("./ios")
    execute(CMD_UPLOAD_APPSTORE_TO_DROPBOX)
    # execute(CMD_UPLOAD_DEBUG_SYMBOL)
    os.chdir("..")


def deploy_sandbox():
    execute(CMD_BUILD_SANDBOX_IPA)
    os.chdir("./ios")
    execute(CMD_UPLOAD_SANDBOX_TO_DROPBOX)
    # execute(CMD_UPLOAD_DEBUG_SYMBOL)
    os.chdir("..")


def deploy(with_clean=True):
    if with_clean:
        flutter_clean()
    deploy_sandbox()
    deploy_production()
    deploy_appstore()


if __name__ == "__main__":
    deploy()
