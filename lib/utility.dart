import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_picker_android/image_picker_android.dart';
import 'package:image_picker_platform_interface/image_picker_platform_interface.dart';

import 'app/components/image_selector.dart';

Future<String> getImage(ImageSource res) async {
  final path1 = await _getImage(res);
  if (path1.isNotEmpty) {
    // return await _cropImage(path1, ratio);
    return path1;
  }
  return '';
}

Future<String> _getImage(ImageSource source) async {
  if (GetPlatform.isAndroid) {
    ImagePickerAndroid.registerWith();
    (ImagePickerPlatform.instance as ImagePickerAndroid).useAndroidPhotoPicker =
        true;
  }
  final pickedFile = await ImagePicker().pickImage(
    source: source,
    maxWidth: 1024,
    maxHeight: 1024,
  );
  // controller.talker.info('pickedFile: $pickedFile');
  if (pickedFile == null) {
    return '';
  }
  return pickedFile.path;
}

Future<String> _cropImage(String filePath, Size ratio) async {
  final imageCropper = ImageCropper();
  final croppedFile = await imageCropper.cropImage(
    sourcePath: filePath,
    aspectRatio: CropAspectRatio(
      ratioX: ratio.width,
      ratioY: ratio.height,
    ),
    uiSettings: [
      AndroidUiSettings(
        toolbarTitle: 'Cropper',
        toolbarColor: Colors.deepOrange,
        toolbarWidgetColor: Colors.white,
        aspectRatioPresets: [
          // CropAspectRatioPreset.original,
          // CropAspectRatioPreset.ratio16x9,
          // CropAspectRatioPreset.ratio7x5,
          // CropAspectRatioPreset.ratio5x4,
          // CropAspectRatioPreset.ratio4x3,
          // CropAspectRatioPreset.ratio5x3,
          // CropAspectRatioPreset.ratio3x2,
          // CropAspectRatioPresetCustom(),
        ],
      ),
      IOSUiSettings(
        title: 'Cropper',
        aspectRatioPresets: [
          // CropAspectRatioPreset.original,
          // CropAspectRatioPreset.ratio16x9,
          // CropAspectRatioPreset.ratio3x2,
          // CropAspectRatioPreset.ratio5x3,
          // CropAspectRatioPresetCustom(), // IMPORTANT: iOS supports only one custom aspect ratio in preset list
        ],
      ),
    ],
  );
  if (croppedFile == null) {
    return '';
  }
  return croppedFile.path;
}

Future<ImageSource?> getImageSelector() async {
  return ImageSelector(
    onCameraPressed: () {
      // Camera action
      Get.back(result: ImageSource.camera);
    },
    onAlbumPressed: () {
      // Gallery action
      Get.back(result: ImageSource.gallery);
    },
  ).sheet<ImageSource>();
}
