import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../ef_colors.dart';
import 'ef_icon_button.dart';

class ImageSelector extends StatelessWidget {
  final VoidCallback? onCameraPressed;
  final VoidCallback? onAlbumPressed;

  const ImageSelector({
    super.key,
    this.onCameraPressed,
    this.onAlbumPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const Text(
      '編輯大頭照',
      style: TextStyle(
        fontSize: 13,
        color: Colors.white,
      ),
      textAlign: TextAlign.center,
      softWrap: false,
    );
    yield SizedBox(height: 20.dh);
    yield Row(
      mainAxisSize: MainAxisSize.min,
      children: _buttons().toList(growable: false),
    );
    yield SizedBox(height: 8.dh);
    yield EfIconButton(
      icon: DecoratedBox(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white,
            width: 1,
          ),
        ),
        child: Icon(
          Icons.close,
          size: 30.dw,
          color: Colors.white,
        ),
      ),
      onPressed: () {
        Get.back();
      },
    );
    yield SizedBox(height: 20.dh);
  }

  Iterable<Widget> _buttons() sync* {
    yield EfIconButton.icon(
      text: '相機',
      icon: Icons.camera_alt,
      onPressed: onCameraPressed,
      backgroundColor: EfColors.primary,
    );
    yield SizedBox(width: 54.dw);
    yield EfIconButton.icon(
      text: '相簿',
      icon: Icons.photo_library,
      onPressed: onAlbumPressed,
      backgroundColor: EfColors.primary,
    );
  }
}
