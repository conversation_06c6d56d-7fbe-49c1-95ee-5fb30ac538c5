import 'package:efshop/ef_colors.dart';
import 'package:flutter/material.dart';

import '../models/members_messages_activity.dart';
import 'thumbnail_image.dart';

class ActivityItem extends StatelessWidget {
  final MembersMessagesActivity data;
  final VoidCallback? onPressed;

  const ActivityItem(
    this.data, {
    super.key,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield ColoredBox(
      color: EfColors.grayD5,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 4,
        ),
        child: Text(
          // '2022-09-14 18:29',
          data.createDatetime ?? '',
          style: const TextStyle(
            fontSize: 12,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
    yield const SizedBox(height: 10);
    yield _titleWithImage();
  }

  Widget _titleWithImage() {
    Iterable<Widget> children() sync* {
      yield Text(
        // '【本週限定\$168】 抗UV吸排圓領涼感上衣',
        data.title ?? '',
        style: const TextStyle(
          fontSize: 12,
          color: EfColors.grayTextDark,
        ),
        softWrap: false,
      );
      yield const SizedBox(height: 6);
      yield const Divider(height: 1);
      yield const SizedBox(height: 10);
      yield ThumbnailImage(data.thumbnail);
    }

    return ColoredBox(
      color: Colors.white,
      child: TextButton(
        onPressed: onPressed,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 10,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: children().toList(growable: false),
          ),
        ),
      ),
    );
  }
}
