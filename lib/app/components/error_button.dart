import 'package:efshop/app/models/login_res.dart';
import 'package:efshop/error_type.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

class ErrorButton extends StatelessWidget {
  final String? errorType;
  final VoidCallback? onTap;
  final _handleWidget = <String, ValueGetter<Widget>>{};

  ErrorButton(
    this.errorType, {
    super.key,
    this.onTap,
  }) {
    _handleWidget[ErrorType.disconnected] = _disconnected;
    _handleWidget[ErrorType.unauthorized] = _unauthorized;
  }

  @override
  Widget build(BuildContext context) {
    if (_handleWidget.containsKey(errorType)) {
      final widget = _handleWidget[errorType];
      return widget!();
    }
    return Center(
      child: Text(errorType ?? ''),
    );
  }

  Widget _disconnected() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('沒有網路連線'),
          const SizedBox(height: 8),
          OutlinedButton(
            onPressed: onTap,
            child: const Text('重試'),
          ),
        ],
      ),
    );
  }

  Widget _unauthorized() {
    return Center(
      child: OutlinedButton(
        onPressed: () async {
          final res = await getLoginRes();
          if (res != null && res.isAvailable) {
            onTap?.call();
          }
        },
        child: const Text('登入註冊'),
      ),
    );
  }
}
