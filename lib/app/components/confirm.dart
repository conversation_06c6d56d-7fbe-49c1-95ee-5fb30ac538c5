import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

class Confirm extends StatelessWidget {
  final VoidCallback? onConfirmPressed;
  final VoidCallback? onCancelPressed;
  final String? confirmButtonText;
  final String? cancelButtonText;
  final String content;

  const Confirm({
    super.key,
    this.onConfirmPressed,
    this.onCancelPressed,
    this.confirmButtonText,
    this.cancelButtonText,
    this.content = '',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 210.dw,
      constraints: BoxConstraints(
        minHeight: 116.dh,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.0),
        boxShadow: const [
          BoxShadow(
            color: Color(0x14000000),
            offset: Offset(0, 0),
            blurRadius: 10,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: _children.toList(growable: false),
      ),
    );
  }

  Iterable<Widget> get _children sync* {
    yield Container(
      constraints: BoxConstraints(
        minHeight: 70.dh,
      ),
      padding: EdgeInsets.symmetric(
        vertical: 24.dh,
        horizontal: 16.dw,
      ),
      alignment: Alignment.center,
      child: Text(
        content,
        style: const TextStyle(
          fontSize: 15,
          color: EfColors.gray93,
        ),
        textAlign: TextAlign.left,
      ),
    );
    yield Divider(
      height: 1.dh,
      thickness: 1.dh,
      color: EfColors.grayD5,
    );
    yield SizedBox(
      height: 48.dh,
      width: double.infinity,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: _buttons().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _buttons() sync* {
    yield TextButton(
      style: TextButton.styleFrom(
        padding: EdgeInsets.zero,
      ),
      onPressed: onCancelPressed,
      child: Text(
        cancelButtonText ?? Button.cancel.display,
        style: const TextStyle(
          fontSize: 15,
          color: EfColors.gray,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      ),
    ).expanded();
    yield const VerticalDivider(
      width: 1,
      thickness: 1,
      color: EfColors.grayD5,
    );
    yield TextButton(
      style: TextButton.styleFrom(
        padding: EdgeInsets.zero,
      ),
      onPressed: onConfirmPressed,
      child: Text(
        confirmButtonText ?? Button.confirm.display,
        style: const TextStyle(
          fontSize: 15,
          color: EfColors.gray,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      ),
    ).expanded();
  }
}
