import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

class HorizontalProgressing extends StatelessWidget {
  final String name;
  final bool leftLine;
  final bool rightLine;
  final bool beginPoint;
  final bool endPoint;

  const HorizontalProgressing({
    super.key,
    this.name = '',
    this.leftLine = true,
    this.rightLine = true,
    this.beginPoint = false,
    this.endPoint = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Widget _line() {
    return Row(
      children: [
        Expanded(
          child: Visibility(
            visible: beginPoint,
            replacement: Divider(
              height: 1,
              thickness: 1,
              color: leftLine ? EfColors.primary : EfColors.grayCC,
            ),
            child: const SizedBox(),
          ),
        ),
        Expanded(
          child: Visibility(
            visible: endPoint,
            replacement: Divider(
              height: 1,
              thickness: 1,
              color: rightLine ? EfColors.primary : EfColors.grayCC,
            ),
            child: const SizedBox(),
          ),
        ),
      ],
    );
  }

  Color _shapeColor() {
    if (leftLine == true) {
      return EfColors.primary;
    }
    if (rightLine == true) {
      return EfColors.primary;
    }
    return EfColors.grayCC;
  }

  Widget _shape() {
    return SizedBox(
      height: 12.dh,
      width: double.infinity,
      child: Stack(
        alignment: Alignment.center,
        children: [
          _line(),
          SizedBox.square(
            dimension: 10.dw,
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: _shapeColor(),
                // color: EfColors.primary,
                shape: BoxShape.circle,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: 4);
    yield _shape();
    yield const SizedBox(height: 4);
    yield Text(
      // '訂單成立',
      name,
      style: const TextStyle(
        fontSize: 12,
        color: EfColors.gray94,
      ),
      textAlign: TextAlign.center,
    );
  }
}
