import 'dart:math' show max;

import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/models/product_detail.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../models/product_series_res.dart';
import '../modules/color_picker/views/color_picker_view.dart';
import 'price.dart';
import 'thumbnail_image.dart';

class SearchItem extends StatelessWidget {
  static const aspectRatio = 172.0 / 308.0; // 寬高比
  ProductDetail get data => _data.value;
  final VoidCallback? onPressed;
  final WabowProvider wabowProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  Talker get talker => wabowProvider.talker;
  final Rx<ProductDetail> _data;

  SearchItem(
    ProductDetail data, {
    super.key,
    this.onPressed,
    required this.wabowProvider,
  }) : _data = data.obs;

  @override
  Widget build(BuildContext context) {
    return Background(
      alignment: Alignment.bottomRight,
      background: Padding(
        padding: const EdgeInsets.only(right: 12),
        child: Obx(() {
          final contains = prefProvider.containsCartItems(data.number ?? '');
          final file = contains
              ? 'assets/images/cart_selected.svg'
              : 'assets/images/cart_unselect.svg';
          return SvgPicture.asset(
            file,
            fit: BoxFit.contain,
            width: 30,
            height: 24,
          );
        }),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  final serials = <ProductSeriesRes>[].obs;
  final _bannerIndex = 0.obs;
  int get bannerIndex => _bannerIndex.value;
  set bannerIndex(int value) => _bannerIndex.value = value;
  int get colorIndex => bannerIndex.clamp(0, max(colors.length - 1, 0));
  final _colors = <ProductSeriesRes>[].obs;

  // distinct with color
  List<ProductSeriesRes> get colors {
    if (_colors.isEmpty) {
      _colors.assignAll(_buildColors());
    }
    return _colors;
  }

  List<ProductSeriesRes> _buildColors() {
    final ls =
        serials.where((element) => '${element.id}' == '${data.id}').toList();
    return serials.fold<List<ProductSeriesRes>>(<ProductSeriesRes>[],
        (previousValue, element) {
      if (previousValue.every((e) => e.mainImage != element.mainImage)) {
        if (ls.isNotEmpty && element.mainImage == ls.first.mainImage) {
          if (element.id == ls.first.id) {
            _bannerIndex.value = previousValue.length;
            previousValue.add(element);
          }
        } else {
          previousValue.add(element);
        }
      }
      return previousValue;
    });
  }

  Future<void> _showColorPicker() async {
    Get.showLoading();
    // get products
    try {
      _data.value = await wabowProvider.getProducts(data.id ?? '');
    } catch (e, s) {
      talker.error(e.toString(), e, s);
    }
    // get products group
    try {
      final it = await wabowProvider.getProductsGroup(data.id ?? '');
      serials.assignAll(it);
    } catch (e, s) {
      talker.error(e.toString(), e, s);
    }
    Get.back();
    final res = await ColorPickerView(
      id: data.id,
      ids: colors.map((element) => element.id),
      index: colorIndex,
    ).sheet();
    if (res is num) {
      // controller.playAnimation(AnimationTarget.cart, res);
    }
  }

  Future<void> _onPressed() async {
    // GA: log select item
    await _logSelectItem();
    final url = data.getUrl();
    final parameters = url.toJson();
    parameters.removeNull();
    await Get.toNamed(
      url.action ?? '',
      parameters: Map.castFrom<String, dynamic, String, String>(parameters),
    );
  }

  ///
  /// GA: log select item
  ///
  Future<void> _logSelectItem() async {
    try {
      await FirebaseAnalytics.instance.logSelectItem(
        itemListId: data.number,
        itemListName: data.name,
        items: [data.toAnalyticsEventItem()],
      );
    } catch (e, s) {
      talker.error(e.toString(), e, s);
    }
  }

  Iterable<Widget> _children() sync* {
    yield AspectRatio(
      aspectRatio: data.thumbnail?.aspectRatio ?? 1,
      child: ClipRRect(
        borderRadius: Constants.borderRadius,
        child: ThumbnailImage(
          data.thumbnail,
          onTap: onPressed ?? _onPressed,
        ),
      ),
    );
    yield SizedBox(height: 12.dh);
    yield InkWell(
      onTap: _showColorPicker,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              // '-°C 冰感【UPF50+防曬】',
              data.name ?? '',
              style: const TextStyle(
                fontSize: 13,
                color: EfColors.gray94,
              ),
              textAlign: TextAlign.start,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(
            height: 8.dh,
            width: double.infinity,
          ),
          Price(
            // 880,
            data.finalPrice,
            fontSize: 16,
            originalPrice: data.originalPrice,
          ),
        ],
      ),
    );
  }
}
