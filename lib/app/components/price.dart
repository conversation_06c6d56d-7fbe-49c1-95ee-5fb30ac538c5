import 'dart:math';

import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

class Price extends StatelessWidget {
  final num price;
  final Color color;
  final double fontSize;
  final num originalPrice;

  const Price(
    this.price, {
    super.key,
    this.color = EfColors.gray,
    this.fontSize = 15,
    this.originalPrice = 0,
  });

  @override
  Widget build(BuildContext context) {
    return RichText(
      softWrap: false,
      text: TextSpan(
        text: '\$ ',
        style: TextStyle(
          fontSize: max(0, fontSize - 3),
          color: color,
        ),
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<TextSpan> _children() sync* {
    yield TextSpan(
      // text: '399',
      text: price.decimalStyle,
      style: TextStyle(
        fontSize: fontSize,
        color: color,
      ),
    );
    if (_showOriginalPrice) {
      yield TextSpan(
        // text: '399',
        text: ' ',
        style: TextStyle(
          fontSize: fontSize,
          color: color,
        ),
      );
      yield TextSpan(
        // text: '399',
        text: originalPrice.decimalStyle,
        style: TextStyle(
          fontSize: max(0, fontSize * 0.8),
          color: EfColors.grayTextLight,
          decoration: TextDecoration.lineThrough, // 刪除線
        ),
      );
    }
  }

  bool get _showOriginalPrice => originalPrice > price && price > 0;
}
