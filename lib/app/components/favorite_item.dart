import 'package:efshop/app/components/price.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import '../models/favorite_data.dart';
import 'thumbnail_image.dart';

class FavoriteItem extends StatelessWidget {
  final VoidCallback? onSizePressed;
  final VoidCallback? onCartPressed;
  final VoidCallback? onRemovePressed;
  final VoidCallback? onPressed;
  final FavoriteData data;
  final bool isLogin;

  const FavoriteItem(
    this.data, {
    super.key,
    this.onSizePressed,
    this.onCartPressed,
    this.onRemovePressed,
    this.onPressed,
    this.isLogin = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 14,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: _body(),
    );
  }

  Widget _body() {
    Iterable<Widget> children() sync* {
      yield TextButton(
        onPressed: onPressed,
        child: _thumbnail(),
      );
      yield const SizedBox(width: 10);
      yield _detail().expanded();
    }

    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _thumbnail() {
    return SizedBox(
      width: 110,
      child: AspectRatio(
        aspectRatio: 110.0 / 160.0,
        child: ClipRRect(
          borderRadius: Constants.borderRadius,
          child: ThumbnailImage(
            data.thumbnail,
            onTap: onPressed,
          ),
        ),
      ),
    );
  }

  Widget _detail() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: TextButton(
            onPressed: onPressed,
            child: Align(
              alignment: Alignment.topLeft,
              child: Text(
                // '保暖柔軟舒適=升溫保暖拉鍊口袋內刷毛長褲',
                data.name ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  color: EfColors.gray,
                ),
              ),
            ),
          ),
        ),
        SizedBox.square(
          dimension: 30,
          child: IconButton(
            // iconSize: 30,
            padding: EdgeInsets.zero,
            onPressed: onRemovePressed,
            icon: const Icon(
              Icons.close,
              color: EfColors.gray70,
              // size: 30,
            ),
          ),
        ),
      ],
    );
    yield _name();
    yield Price(data.finalPrice, originalPrice: data.price ?? 0);
    if (data.size != null && data.size!.isNotEmpty && onSizePressed != null) {
      yield _size();
    }
    // yield const SizedBox(height: 4);
    // yield ConstrainedBox(
    //     constraints: const BoxConstraints(
    //   minHeight: 8,
    // ));
    if (isLogin) {
      yield Flexible(
        child: ConstrainedBox(
          constraints: const BoxConstraints(
            minHeight: 36,
          ),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: SizedBox(
              height: 28,
              child: TextButton(
                onPressed: onCartPressed,
                style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  disabledBackgroundColor: EfColors.grayBB,
                  // backgroundColor: EfColors.primary,
                  backgroundColor: data.displayColor,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                  ),
                  minimumSize: const Size.fromHeight(28),
                ),
                child: Text(
                  // '加到購物車',
                  data.displayText,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ),
      );
    }
  }

  Widget _name() {
    Iterable<Widget> children() sync* {
      yield Text(
        // '深藍',
        data.color ?? '',
        style: const TextStyle(
          fontSize: 13,
          color: EfColors.gray93,
        ),
      );
      if (onSizePressed == null) {
        yield const SizedBox(width: 4);
        yield Text(
          data.size ?? '',
          style: const TextStyle(
            fontSize: 13,
            color: EfColors.gray93,
          ),
        );
      }
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children().toList(growable: false),
    );
  }

  Widget _size() {
    Iterable<Widget> children() sync* {
      yield const Text(
        '尺寸',
        style: TextStyle(
          fontSize: 13,
          color: EfColors.gray,
        ),
        softWrap: false,
      );
      yield const Spacer();
      if (isLogin) {
        yield SizedBox(
          width: 113,
          height: 30,
          // decoration: BoxDecoration(
          //   color: const Color(0xffffffff),
          //   borderRadius: BorderRadius.circular(4.0),
          //   border: Border.all(
          //     width: 1.0,
          //     color: const Color(0xffd5d5d5),
          //   ),
          // ),
          child: OutlinedButton(
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.zero,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(4)),
              ),
              side: const BorderSide(
                width: 1,
                color: EfColors.grayD5,
              ),
            ),
            onPressed: onSizePressed,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(width: 8),
                Text(
                  data.size ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: EfColors.gray93,
                  ),
                ),
                const Spacer(),
                const VerticalDivider(
                  width: 1,
                  color: EfColors.gray93,
                  indent: 4,
                  endIndent: 4,
                ),
                const Icon(
                  Icons.expand_more,
                  color: EfColors.gray93,
                ),
              ],
            ),
          ),
        );
      }
    }

    return children().row();
  }
}
