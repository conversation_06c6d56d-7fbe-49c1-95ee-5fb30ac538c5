import 'package:efshop/app/models/product.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'price.dart';
import 'thumbnail_image.dart';

class CommentItem extends StatelessWidget {
  final Product data;
  final TextEditingController? controller;

  const CommentItem(
    this.data, {
    super.key,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield ListTile(
      leading: SizedBox(
        width: 54,
        height: 78,
        child: ThumbnailImage(data.thumbnail),
      ),
      title: Text(
        // '柔軟.舒適.MIT環保材質-抗UI吸排抗菌七分褲...',
        data.productName ?? '',
        style: const TextStyle(
          fontSize: 13,
          color: EfColors.gray47,
        ),
      ),
      subtitle: Price(
        data.numOfFinalPrice,
        originalPrice: data.numOfOriginalPrice,
      ),
    );
    yield ConstrainedBox(
      constraints: const BoxConstraints(
        minHeight: 100,
      ),
      child: TextFormField(
        controller: controller,
        decoration: const InputDecoration(
          hintText: '請輸入評價內容，字數300個字以內',
          hintStyle: TextStyle(
            fontSize: 13,
            color: EfColors.grayD5,
          ),
          enabledBorder: InputBorder.none,
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 14,
            vertical: 8,
          ),
        ),
      ),
    );
  }
}
