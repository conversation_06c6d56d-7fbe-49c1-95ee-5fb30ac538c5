import 'package:efshop/ef_colors.dart';
import 'package:flutter/material.dart';

class Input extends StatelessWidget {
  final String? hintText;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final bool obscureText;
  final VoidCallback? onClear;
  final String? initialValue;
  final TextInputType? keyboardType;

  const Input({
    super.key,
    this.hintText,
    this.controller,
    this.onChanged,
    this.obscureText = false,
    this.onClear,
    this.initialValue,
    this.keyboardType,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: TextFormField(
        keyboardType: keyboardType,
        initialValue: initialValue,
        controller: controller,
        obscureText: obscureText,
        onChanged: onChanged,
        decoration: InputDecoration(
          contentPadding: EdgeInsets.zero,
          hintText: hintText,
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          hintStyle: const TextStyle(
            fontSize: 15,
            color: EfColors.grayD5,
          ),
          // icon: IconButton(
          //   onPressed: () {},
          //   icon: const Icon(
          //     Icons.cancel,
          //     color: EfColors.grayD5,
          //   ),
          // ),
        ),
      ),
      trailing: IconButton(
        onPressed: onClear,
        icon: const Icon(
          Icons.cancel,
          color: EfColors.grayD5,
        ),
      ),
    );
  }
}
