import 'package:efshop/app/components/search_item.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import '../models/product_detail.dart';

class EfGridItem extends StatelessWidget {
  final ProductDetail left;
  final ProductDetail right;
  final WabowProvider wabowProvider;

  const EfGridItem({
    super.key,
    required this.left,
    required this.right,
    required this.wabowProvider,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield SearchItem(
      left,
      wabowProvider: wabowProvider,
    ).expanded();
    if (right.id == null || right.id!.isEmpty) {
      yield const SizedBox().expanded();
    } else {
      yield SizedBox(width: 8.dw);
      yield SearchItem(
        right,
        wabowProvider: wabowProvider,
      ).expanded();
    }
  }
}
