import 'package:efshop/ef_colors.dart';
import 'package:flutter/material.dart';

import '../models/product_series_res.dart';
import 'thumbnail_image.dart';

class ColorItem extends StatelessWidget {
  final ProductSeriesRes data;
  final double size;
  final VoidCallback? onTap;
  final bool selected;

  const ColorItem(
    this.data, {
    this.selected = false,
    super.key,
    this.onTap,
    this.size = 44,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: DecoratedBox(
        decoration: BoxDecoration(
          shape: BoxShape.rectangle,
          color: Colors.white,
          border: Border.all(
            color: selected ? EfColors.grayText : Colors.transparent,
            width: 0.6,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: DecoratedBox(
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              // color: color,
              border: Border.all(
                color: EfColors.grayD3,
                width: 1,
              ),
            ),
            child: SizedBox.square(
              dimension: size,
              child: ThumbnailImage.url(data.colorImage),
            ),
          ),
        ),
      ),
    );
  }
}
