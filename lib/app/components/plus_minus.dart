import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

class PlusMinus extends StatelessWidget {
  final VoidCallback? onPlus;
  final VoidCallback? onMinus;
  final String title;

  const PlusMinus({
    super.key,
    this.onPlus,
    this.onMinus,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40.dh,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield _minusButton();
    yield Container(
      padding: EdgeInsets.zero,
      height: 40.dh,
      alignment: Alignment.center,
      constraints: BoxConstraints(
        minWidth: 56.dw,
      ),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          color: EfColors.gray1B,
        ),
      ),
    );
    yield _plusButton();
  }

  Widget _minusButton() {
    return IconButton(
      padding: EdgeInsets.zero,
      onPressed: onMinus,
      icon: Container(
        width: 40.dw,
        height: 40.dh,
        decoration: const BoxDecoration(
          color: EfColors.grayF5,
          borderRadius: BorderRadius.horizontal(
            left: Radius.circular(6),
            right: Radius.circular(0),
          ),
        ),
        child: const Icon(Icons.remove),
      ),
    );
  }

  Widget _plusButton() {
    return IconButton(
      padding: EdgeInsets.zero,
      onPressed: onPlus,
      icon: Container(
        width: 40.dw,
        height: 40.dh,
        decoration: const BoxDecoration(
          color: EfColors.grayF5,
          borderRadius: BorderRadius.horizontal(
            left: Radius.circular(0),
            right: Radius.circular(6),
          ),
        ),
        child: const Icon(Icons.add),
      ),
    );
  }
}
