import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/widgets.dart';

import '../models/message_data.dart';
import 'icon_text.dart';

class RefundCode extends StatelessWidget {
  final AddressType type;
  final String code;

  const RefundCode({
    super.key,
    required this.type,
    required this.code,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield IconText(
      MessageData(
        // iconPath: 'assets/images/seven_eleven.png',
        iconPath: type.icon,
        titleText: '完成退貨申請',
      ),
      iconSize: 30.dw,
    );
    yield SizedBox(height: 14.dh);
    yield Text.rich(
      TextSpan(
        style: const TextStyle(
          fontSize: 14,
          color: EfColors.gray47,
        ),
        children: [
          const TextSpan(
            text: '退貨便代號：',
          ),
          TextSpan(
            // text: 'A55159390535',
            text: code,
            style: const TextStyle(
              color: EfColors.primary,
            ),
          ),
        ],
      ),
      textHeightBehavior:
          const TextHeightBehavior(applyHeightToFirstAscent: false),
      softWrap: false,
    );
    yield SizedBox(height: 10.dh);
    yield const Text(
      '請於5天內完成寄件',
      style: TextStyle(
        fontSize: 13,
        color: EfColors.gray93,
      ),
      softWrap: false,
    );
  }
}
