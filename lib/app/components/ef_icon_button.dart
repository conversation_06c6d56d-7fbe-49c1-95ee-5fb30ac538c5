import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

class EfIconButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget? icon;
  final Widget? text;
  final num badgeCount;
  final num size;
  final Color? backgroundColor;

  const EfIconButton({
    super.key,
    this.onPressed,
    this.icon,
    this.text,
    this.badgeCount = 0,
    this.size = 56,
    this.backgroundColor = Colors.transparent,
  });

  factory EfIconButton.icon({
    VoidCallback? onPressed,
    required IconData icon,
    String? text,
    num badgeCount = 0,
    num size = 56,
    Color? backgroundColor,
  }) {
    return EfIconButton(
      backgroundColor: backgroundColor,
      onPressed: onPressed,
      icon: Icon(
        icon,
        size: 28.dw,
                color: Colors.white,
      ),
      text: text == null
          ? null
          : Text(
              text,
              style: const TextStyle(
                fontSize: 13,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
              softWrap: false,
            ),
      badgeCount: badgeCount,
      size: size,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield IconButton(
      color: backgroundColor,
      iconSize: size.dw,
      onPressed: onPressed,
      icon: Badge.count(
        isLabelVisible: badgeCount > 0,
        count: badgeCount.toInt(),
        child: CircleAvatar(
          backgroundColor: backgroundColor,
          radius: 20.dw,
          child: icon,
        ),
      ),
    );
    if (text != null) {
      // yield SizedBox(height: 10.dh);
      yield text!;
    }
  }
}
