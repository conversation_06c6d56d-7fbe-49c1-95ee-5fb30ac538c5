import 'dart:math';

import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../models/members_orders_res.dart';
import '../routes/app_pages.dart';
import 'price.dart';
import 'thumbnail_image.dart';

class OrderItem extends StatelessWidget {
  final MembersOrdersRes data;
  final VoidCallback? onTapCancel;
  final VoidCallback? onTapReorder;
  final VoidCallback? onRefresh;

  const OrderItem(
    this.data, {
    super.key,
    this.onTapCancel,
    this.onTapReorder,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        await Get.toNamed(Routes.ORDER_DETAIL, parameters: {
          Keys.id: data.id ?? '',
        });
        onRefresh?.call();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 18),
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: _children().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: 12);
    yield _header();
    yield const Divider(height: 24);
    yield _body().sizedBox(height: 62);
    if (Constants.orderStatusCancelled.contains(data.status)) {
      // 已取消的訂單不顯示底部按鈕
      yield const SizedBox(height: 12);
    } else {
      yield* _bottom();
    }
  }

  Widget _body() {
    Iterable<Widget> children() sync* {
      final products = data.normalProducts;
      yield ListView.separated(
        // padding: const EdgeInsets.symmetric(horizontal: 18),
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          final element = products.elementAt(index);
          return SizedBox.square(
            dimension: 62,
            // child: Placeholder(),
            child: ThumbnailImage(element.thumbnail),
          );
        },
        separatorBuilder: (context, index) {
          return const SizedBox(width: 4);
        },
        itemCount: min(products.length, 3),
      ).expanded();
      yield const SizedBox(width: 2);
      final amount = products.fold<num>(0, (previousValue, element) {
        return previousValue + (num.tryParse(element.quantity ?? '1') ?? 1);
      });
      yield Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            // '共${products.length.decimalStyle}件商品',
            '共 $amount 件商品',
            style: const TextStyle(
              fontSize: 14,
              color: EfColors.gray94,
            ),
            textAlign: TextAlign.right,
            softWrap: false,
          ),
          const SizedBox(height: 5),
          // Text(
          //   // '210',
          //   (data.total ?? 0).decimalStyle,
          //   style: const TextStyle(
          //     fontSize: 14,
          //     color: EfColors.gray,
          //   ),
          //   textAlign: TextAlign.right,
          //   softWrap: false,
          // ),
          Price(
            data.total ?? 0,
            fontSize: 14,
          ),
        ],
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Iterable<Widget> _bottom() sync* {
    Iterable<Widget> children() sync* {
      if (Constants.shippingButton.contains(data.status)) {
        yield const SizedBox(width: 8);
        yield OutlinedButton(
          onPressed: () {
            Get.toNamed(Routes.SHIPPING_DETAIL, parameters: {
              Keys.id: data.id ?? '',
            });
          },
          child: const Text(
            '物流詳情',
            style: TextStyle(
              fontSize: 13,
              color: EfColors.gray,
            ),
            textAlign: TextAlign.center,
          ),
        );
      }
      // if (Constants.refundButton.contains(data.status)) {
      if (data.isRefund == true) {
        yield const SizedBox(width: 8);
        yield OutlinedButton(
          onPressed: () {
            Get.toNamed(Routes.REFUND_DETAIL, parameters: {
              Keys.id: data.id ?? '',
            });
          },
          child: const Text(
            '退貨詳情',
            style: TextStyle(
              fontSize: 13,
              color: EfColors.gray,
            ),
            textAlign: TextAlign.center,
          ),
        );
      }
      // if (['已取貨', '處理中', '已出貨', '待評價'].contains(data.status)) {
      // 由旗標決定是否顯示退貨按鈕
      if (data.isRefundAble == true) {
        yield const SizedBox(width: 8);
        yield OutlinedButton(
          onPressed: () async {
            await Get.toNamed(Routes.REFUND, parameters: {
              Keys.id: data.id ?? '',
            });
            onRefresh?.call();
          },
          child: const Text(
            '申請退貨',
            style: TextStyle(
              fontSize: 13,
              color: EfColors.gray,
            ),
            textAlign: TextAlign.center,
          ),
        );
      }
      if (Constants.invoiceButton.contains(data.status)) {
        yield const SizedBox(width: 8);
        yield OutlinedButton(
          onPressed: () {
            Get.toNamed(Routes.INVOICE, parameters: {
              Keys.id: data.id ?? '',
            });
          },
          child: const Text(
            '查看發票',
            style: TextStyle(
              fontSize: 13,
              color: EfColors.gray,
            ),
            textAlign: TextAlign.center,
          ),
        );
      }
      // if (data.isProductComment != true) {
      if (Constants.ratingButton.contains(data.status)) {
        yield const SizedBox(width: 8);
        yield OutlinedButton(
          onPressed: () {
            Get.toNamed(Routes.LEAVE_COMMENT, parameters: {
              Keys.id: data.id ?? '',
            });
          },
          child: const Text(
            '評價商品',
            style: TextStyle(
              fontSize: 13,
              color: EfColors.gray,
            ),
            textAlign: TextAlign.center,
          ),
        );
      }
      // 取消訂單按鈕
      if (data.isCancelAble ?? false) {
        yield const SizedBox(width: 8);
        yield OutlinedButton(
          onPressed: onTapCancel,
          child: const Text(
            '取消訂單',
            style: TextStyle(
              fontSize: 13,
              color: EfColors.gray,
            ),
            textAlign: TextAlign.center,
          ),
        );
      }
    }

    final it = children();
    if (it.isNotEmpty) {
      yield const Divider(height: 24);
      yield SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        reverse: true,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: it.toList(growable: false),
        ),
      ).sizedBox(height: 30, width: double.infinity);
    }
    yield const SizedBox(height: 12);
  }

  Widget _header() {
    Iterable<Widget> children() sync* {
      // yield const Text('訂單編號：55664456').expanded();
      yield Text(
        '訂單編號：${data.number}',
        style: const TextStyle(
          fontSize: 13,
          color: EfColors.gray,
        ),
      ).expanded();
      // if (Constants.orderStatusFailed.contains(data.status)) {
      if (data.isReAddToCart == true) {
        // 重新付款按鈕
        yield SizedBox(
          height: 26.dh,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              shape: const StadiumBorder(),
            ),
            onPressed: onTapReorder,
            child: const Text(
              '重新付款',
              style: TextStyle(
                fontSize: 11,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        );
        yield SizedBox(width: 4.dw);
      }
      yield Text(
        data.displayStatus,
        style: const TextStyle(
          fontSize: 13,
          color: EfColors.gray94,
        ),
      );
    }

    return Row(
      children: children().toList(growable: false),
    );
  }
}
