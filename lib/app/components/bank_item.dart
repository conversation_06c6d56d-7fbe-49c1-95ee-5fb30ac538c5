import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import '../models/members_refund_res.dart';

class BankItem extends StatelessWidget {
  final MembersRefundRes data;
  final VoidCallback? onPressed;
  final VoidCallback? onEditPressed;
  final VoidCallback? onDeletePressed;
  final ValueChanged<bool?>? onChanged;

  const BankItem(
    this.data, {
    super.key,
    this.onPressed,
    this.onEditPressed,
    this.onDeletePressed,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Widget _body() {
    Iterable<Widget> children() sync* {
      yield Text(
        '銀行代碼：${data.bankCode}',
        style: const TextStyle(
          fontSize: 14,
          color: EfColors.gray,
        ),
        softWrap: false,
      );
      yield Text(
        '銀行名稱：${data.bankBranch}',
        style: const TextStyle(
          fontSize: 14,
          color: EfColors.gray,
        ),
        softWrap: false,
      );
      yield Text(
        '銀行戶名：${data.accountName}',
        style: const TextStyle(
          fontSize: 14,
          color: EfColors.gray,
        ),
        softWrap: false,
      );
      yield Text(
        '帳號：${data.accountNumber}',
        style: const TextStyle(
          fontSize: 14,
          color: EfColors.gray,
        ),
        softWrap: false,
      );
    }

    return TextButton(
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 16.dw),
      ),
      onPressed: onPressed,
      child: SizedBox(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: children().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield SizedBox(height: 16.dh);
    yield _body();
    // yield Text(
    //   '銀行代碼：${data.bankCode}',
    //   style: const TextStyle(
    //     fontSize: 14,
    //     color: EfColors.gray,
    //   ),
    //   softWrap: false,
    // ).paddingSymmetric(horizontal: 16.dw);
    // yield Text(
    //   '銀行名稱：${data.bankBranch}',
    //   style: const TextStyle(
    //     fontSize: 14,
    //     color: EfColors.gray,
    //   ),
    //   softWrap: false,
    // ).paddingSymmetric(horizontal: 16.dw);
    // yield Text(
    //   '銀行戶名：${data.accountName}',
    //   style: const TextStyle(
    //     fontSize: 14,
    //     color: EfColors.gray,
    //   ),
    //   softWrap: false,
    // ).paddingSymmetric(horizontal: 16.dw);
    // yield Text(
    //   '帳號：${data.accountNumber}',
    //   style: const TextStyle(
    //     fontSize: 14,
    //     color: EfColors.gray,
    //   ),
    //   softWrap: false,
    // ).paddingSymmetric(horizontal: 16.dw);
    yield SizedBox(height: 20.dh);
    yield const Divider(height: 1);
    yield _bottom();
  }

  Widget _bottom() {
    Iterable<Widget> children() sync* {
      yield SizedBox(width: 8.dw);
      yield CheckboxListTile(
        contentPadding: EdgeInsets.zero,
        title: Transform.translate(
          offset: Offset(-16.dw, 0),
          child: const Text(
            '預設',
            style: TextStyle(
              fontSize: 14,
              color: EfColors.gray,
            ),
            softWrap: false,
          ),
        ),
        value: data.isDefault == true,
        onChanged: onChanged,
        controlAffinity: ListTileControlAffinity.leading,
      ).expanded();
      yield OutlinedButton(
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.symmetric(horizontal: 24.dw),
        ),
        onPressed: onEditPressed,
        child: const Text(
          '編輯',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          textAlign: TextAlign.center,
        ),
      );
      yield SizedBox(width: 8.dw);
      yield OutlinedButton(
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.symmetric(horizontal: 24.dw),
        ),
        onPressed: onDeletePressed,
        child: const Text(
          '刪除',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          textAlign: TextAlign.center,
        ),
      );
      yield SizedBox(width: 14.dw);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }
}
