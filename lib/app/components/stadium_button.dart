import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class StadiumButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? buttonText;
  final Color backgroundColor;
  final Color textColor;
  final EdgeInsetsGeometry? padding;
  final Widget? child;

  const StadiumButton({
    super.key,
    this.child,
    this.onPressed,
    this.padding,
    this.buttonText,
    this.textColor = Colors.white,
    this.backgroundColor = EfColors.primary,
  });

  StadiumButton.chip({
    super.key,
    this.child,
    this.onPressed,
    this.buttonText,
    this.textColor = Colors.white,
    this.backgroundColor = EfColors.primary,
  }) : padding = Constants.chipPadding;

  factory StadiumButton.chipV2({
    Key? key,
    final child,
    final onPressed,
    final buttonText,
    final textColor,
    final backgroundColor,
  }) {
    return StadiumButton(
      key: key,
      child: child,
      onPressed: onPressed,
      buttonText: buttonText,
      textColor: textColor,
      backgroundColor: backgroundColor,
      padding: Constants.chipPadding,
    );
  }

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        shape: const StadiumBorder(),
        minimumSize: Size.zero,
        backgroundColor: backgroundColor,
        padding: padding,
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: 16.dw,
          minHeight: 16.dh,
        ),
        child: _child(),
      ),
    );
  }

  Widget _child() {
    if (child != null) {
      return child!;
    }
    return Text(
      buttonText ?? '',
      style: TextStyle(
        fontSize: 10.dsp,
        color: textColor,
      ),
      textAlign: TextAlign.left,
    );
  }
}
