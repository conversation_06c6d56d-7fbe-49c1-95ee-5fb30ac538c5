import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class EfPlaceholder extends StatelessWidget {
  final double dimension;
  const EfPlaceholder({
    super.key,
    this.dimension = 50,
  });

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: const Color(0xfff0eff1),
      child: Center(
        child: SvgPicture.asset(
          'assets/images/placeholder.svg',
          fit: BoxFit.contain,
          width: dimension,
          height: dimension,
        ),
      ),
    );
  }
}
