import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:efshop/extension.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

import '../models/thumbnail.dart';
import 'ef_placeholder.dart';

class ThumbnailImage extends StatefulWidget {
  final Thumbnail? data;
  final VoidCallback? onTap;
  final bool mixWithOthers;
  final bool enableVolume;
  // on play end
  final VoidCallback? onVideoEnded;
  final VoidCallback? onVideoStarted;
  final bool looping;

  const ThumbnailImage(
    this.data, {
    super.key,
    this.onTap,
    this.mixWithOthers = true,
    this.enableVolume = false,
    this.onVideoEnded,
    this.onVideoStarted,
    this.looping = true,
  });

  factory ThumbnailImage.url(
    String? url, {
    Key? key,
  }) {
    return ThumbnailImage(
      Thumbnail(
        src: url,
        width: 0,
        height: 0,
      ),
      key: key,
    );
  }

  @override
  State<ThumbnailImage> createState() => _ThumbnailImageState();
}

class _ThumbnailImageState extends State<ThumbnailImage> {
  VideoPlayerController? videoController;

  @override
  void dispose() {
    videoController?.removeListener(_onVideoEvent);
    videoController?.pause();
    videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final data = widget.data ?? Thumbnail.fromJson({});
    return InkWell(
      onTap: widget.onTap,
      child: data.isVideo ? _video() : _image(),
    );
  }

  Future<void> _replay() async {
    // 檢查組件是否仍然掛載
    if (!mounted || videoController == null) return;
    
    try {
      await videoController?.seekTo(Duration.zero);
      await videoController?.play();
      // 確保在安全的上下文中調用回調
      if (mounted) {
        widget.onVideoStarted?.call();
      }
    } catch (e) {
      log('Error during video replay: $e');
    }
  }

  void _onVideoEvent() {
    // 檢查組件是否仍然掛載
    if (!mounted) return;
    
    if (videoController != null) {
      if (videoController!.isPlayEnd) {
        // 影片播放結束時的回調
        log('影片播放結束');
        // 在這裡添加你想要執行的操作
        _replay();
        // 確保在安全的上下文中調用回調
        if (mounted) {
          widget.onVideoEnded?.call();
        }
      }
    }
  }

  Future<VideoPlayerController> _initializeVideoController(Uri uri) async {
    if (videoController == null) {
      final videoController = VideoPlayerController.networkUrl(
        uri,
        videoPlayerOptions: VideoPlayerOptions(
          mixWithOthers: widget.mixWithOthers,
        ),
      );
      this.videoController = videoController;
      // videoController.value.size;
      // videoController.value.aspectRatio;
      await videoController.initialize();
      if (widget.looping) {
        await videoController.setLooping(true);
      } else {
        await videoController.setLooping(false);
        videoController.addListener(_onVideoEvent);
      }
      if (widget.enableVolume) {
        // await videoController.setVolume(1.0);
      } else {
        await videoController.setVolume(0.0);
      }
      await videoController.play();
      widget.onVideoStarted?.call();
    }
    return videoController!;
  }

  Widget _video() {
    final data = widget.data ?? Thumbnail.fromJson({});
    return FutureBuilder<VideoPlayerController>(
      future: _initializeVideoController(data.uri),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          try {
            final videoController = snapshot.data as VideoPlayerController;
            return SizedBox(
              width: double.infinity,
              child: FittedBox(
                alignment: Alignment.center,
                fit: BoxFit.cover,
                child: SizedBox(
                  width: videoController.value.size.width,
                  height: videoController.value.size.height,
                  child: VideoPlayer(videoController),
                ),
              ),
            );
          } catch (e) {
            // log(e.toString());
          }
        }
        return AspectRatio(
          aspectRatio: data.aspectRatio,
          child: const EfPlaceholder(),
        );
      },
    );
  }

  Widget _image() {
    final data = widget.data ?? Thumbnail.fromJson({});
    // final imageProvider = ExtendedNetworkImageProvider(data.src ?? '');
    // if (data.width == null || data.width == 0) {
    //   data.width = 1;
    //   final image = Image(image: imageProvider);
    //   image.image
    //       .resolve(const ImageConfiguration())
    //       .addListener(ImageStreamListener((info, call) {
    //     data.width = info.image.width;
    //     data.height = info.image.height;
    //   }));
    // }
    // return AspectRatio(
    //   aspectRatio: data.aspectRatio,
    //   child: ExtendedImage(
    //     image: ExtendedResizeImage(
    //       imageProvider,
    //       compressionRatio: 0.8,
    //       allowUpscaling: true,
    //     ),
    //     fit: BoxFit.cover,
    //     loadStateChanged: (state) {
    //       switch (state.extendedImageLoadState) {
    //         case LoadState.loading:
    //           return const EfPlaceholder();
    //         case LoadState.completed:
    //           data.width = state.extendedImageInfo?.image.width;
    //           data.height = state.extendedImageInfo?.image.height;
    //           return AspectRatio(
    //             aspectRatio: data.aspectRatio,
    //             child: ExtendedRawImage(
    //               image: state.extendedImageInfo?.image,
    //               fit: BoxFit.cover,
    //             ),
    //           );
    //         case LoadState.failed:
    //           return const EfPlaceholder();
    //       }
    //     },
    //   ),
    // );
    if (data.src == null || data.src!.isEmpty) {
      return AspectRatio(
        aspectRatio: data.aspectRatio,
        child: const EfPlaceholder(),
      );
    }
    return CachedNetworkImage(
      fadeInDuration: 0.milliseconds,
      fadeOutDuration: 0.milliseconds,
      maxWidthDiskCache: 800,
      maxHeightDiskCache: 800,
      filterQuality: FilterQuality.none,
      fit: BoxFit.cover,
      imageUrl: data.src ?? '',
      imageBuilder: (context, imageProvider) {
        final image = Image(image: imageProvider);
        // 在这里获取图像尺寸
        if (data.width == null || data.width == 0) {
          data.width = 1;
          image.image
              .resolve(const ImageConfiguration())
              .addListener(ImageStreamListener((info, call) {
            data.width = info.image.width;
            data.height = info.image.height;
          }));
        }
        return AspectRatio(
          aspectRatio: data.aspectRatio,
          child: DecoratedBox(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.cover,
                // colorFilter: ColorFilter.mode(Colors.red, BlendMode.colorBurn),
              ),
            ),
          ),
        );
      },
      placeholder: (context, url) {
        return AspectRatio(
          aspectRatio: data.aspectRatio,
          child: const EfPlaceholder(),
        );
      },
      // errorWidget: (context, url, error) => const Icon(Icons.error),
      errorWidget: (context, url, error) {
        // return const SizedBox();
        return AspectRatio(
          aspectRatio: data.aspectRatio,
          child: const EfPlaceholder(),
        );
      },
    );
  }
}

// class ThumbnailImage extends StatelessWidget {
//   final Thumbnail? data;
//   final VoidCallback? onTap;

//   const ThumbnailImage(
//     this.data, {
//     this.onTap,
//     super.key,
//   });

//   factory ThumbnailImage.url(
//     String? url, {
//     Key? key,
//   }) {
//     return ThumbnailImage(
//       Thumbnail(
//         src: url,
//         width: 0,
//         height: 0,
//       ),
//       key: key,
//     );
//   }

//   Widget _video() {
//     final data = this.data ?? Thumbnail.fromJson({});
//     final videoController = VideoPlayerController.networkUrl(data.uri);
//     videoController.initialize();
//     videoController.setLooping(true);
//     videoController.setVolume(0.0);
//     videoController.play();
//     return VideoPlayer(videoController);
//   }

//   Widget _image() {
//     final data = this.data ?? Thumbnail.fromJson({});
//     return CachedNetworkImage(
//       maxWidthDiskCache: 800,
//       maxHeightDiskCache: 800,
//       filterQuality: FilterQuality.none,
//       fit: BoxFit.cover,
//       imageUrl: data.src ?? '',
//       imageBuilder: (context, imageProvider) {
//         final image = Image(image: imageProvider);
//         // 在这里获取图像尺寸
//         if (data.width == null || data.width == 0) {
//           data.width = 1;
//           image.image
//               .resolve(const ImageConfiguration())
//               .addListener(ImageStreamListener((info, call) {
//             data.width = info.image.width;
//             data.height = info.image.height;
//           }));
//         }
//         return AspectRatio(
//           aspectRatio: data.aspectRatio,
//           child: DecoratedBox(
//             decoration: BoxDecoration(
//               image: DecorationImage(
//                 image: imageProvider,
//                 fit: BoxFit.cover,
//                 // colorFilter: ColorFilter.mode(Colors.red, BlendMode.colorBurn),
//               ),
//             ),
//           ),
//         );
//       },
//       placeholder: (context, url) {
//         return AspectRatio(
//           aspectRatio: data.aspectRatio,
//           child: const EfPlaceholder(),
//         );
//       },
//       // errorWidget: (context, url, error) => const Icon(Icons.error),
//       errorWidget: (context, url, error) {
//         // return const SizedBox();
//         return AspectRatio(
//           aspectRatio: data.aspectRatio,
//           child: const EfPlaceholder(),
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     final data = this.data ?? Thumbnail.fromJson({});
//     return InkWell(
//       onTap: onTap,
//       child: data.isVideo ? _video() : _image(),
//     );
//   }
// }

extension on VideoPlayerController {
  bool get isPlayEnd => value.position == value.duration;
}
