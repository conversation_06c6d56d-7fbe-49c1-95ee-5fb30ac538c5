import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import '../models/members_orders_message.dart';

class VerticalProcessing extends StatelessWidget {
  final num index;
  final num length;
  final MembersOrdersMessage data;

  bool get isFirst => index == 0;
  bool get isLast => index == length - 1;

  const VerticalProcessing(
    this.index,
    this.length,
    this.data, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return _body();
  }

  Widget _verticalLine() {
    return SizedBox(
      width: 10.dw,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            height: 90.dh,
            child: VerticalDivider(
              indent: isFirst ? 20.dh : 0,
              endIndent: isLast ? 70.dh : 0,
              width: 1,
              thickness: 1,
              color: EfColors.grayDD,
            ),
          ),
          Transform.translate(
            offset: const Offset(0, -25),
            child: SizedBox.square(
              dimension: 10.dw,
              child: DecoratedBox(
                decoration: BoxDecoration(
                  color: _shapeColor,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _body() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _verticalLine(),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: _children().toList(growable: false),
          ),
        ),
      ],
    );
  }

  Color get _color {
    if (isFirst) {
      return EfColors.primary;
    } else {
      return EfColors.gray;
    }
  }

  Color get _shapeColor {
    if (isFirst) {
      return EfColors.primary;
    } else {
      return EfColors.grayDD;
    }
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: 10);
    yield Text(
      // '已出貨',
      data.message ?? '',
      style: TextStyle(
        fontSize: 13,
        color: _color,
      ),
      softWrap: true,
    );
    yield Text(
      // '2022-09-14 15:52:32',
      data.createDatetime ?? '',
      style: TextStyle(
        fontSize: 13,
        color: _color,
      ),
      softWrap: false,
    );
    // yield const Text(
    //   '配送方式：宅配',
    //   style: TextStyle(
    //     fontSize: 13,
    //     color: EfColors.gray,
    //   ),
    //   softWrap: false,
    // );
    // yield const Text(
    //   '配送編號：10301610',
    //   style: TextStyle(
    //     fontSize: 13,
    //     color: EfColors.gray,
    //   ),
    //   softWrap: false,
    // );
  }
}
