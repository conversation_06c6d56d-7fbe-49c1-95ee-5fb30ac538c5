import 'package:flutter/material.dart';

class EfCenter extends StatelessWidget {
  final Widget? child;

  const EfCenter({
    this.child,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Expanded(
          child: Align(
            alignment: Alignment.bottomCenter,
            child: child,
          ),
        ),
        const Spacer(),
      ],
    );
  }
}
