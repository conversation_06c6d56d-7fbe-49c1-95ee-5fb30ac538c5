import 'dart:math' show max;

import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../models/product.dart';
import '../routes/app_pages.dart';
import 'price.dart';
import 'thumbnail_image.dart';

class ProductItem extends StatelessWidget {
  final Product data;
  const ProductItem(
    this.data, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: _children().toList(growable: false),
        ),
      ),
    ).click(() {
      if (data.numOfFinalPrice > 0) {
        Get.toNamed(Routes.PRODUCT, parameters: {
          Keys.id: data.productId ?? '',
        });
      }
    });
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(width: 20);
    yield SizedBox.square(
      dimension: 68,
      child: ThumbnailImage(data.thumbnail),
    );
    yield const SizedBox(width: 12);
    yield Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.max,
      children: _subChildren1().toList(growable: false),
    ).expanded();
    yield const SizedBox(width: 12);
    yield Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.max,
      children: _subChildren2().toList(growable: false),
    );
    yield const SizedBox(width: 14);
  }

  Iterable<Widget> _subChildren1() sync* {
    // const SizedBox(height: 12),
    yield Text(
      data.productName ?? '',
      style: const TextStyle(
        fontSize: 13,
        color: EfColors.gray,
      ),
    );
    yield const SizedBox(height: 4);
    yield Text(
      data.displaySubtitle,
      style: const TextStyle(
        fontSize: 13,
        color: EfColors.gray,
      ),
    );
    // if (data.isRefund) {
    //   yield Text(
    //     '退貨原因：${data.reason}', // 退貨原因
    //     style: const TextStyle(
    //       fontSize: 12,
    //       color: EfColors.gray,
    //     ),
    //   );
    // }
  }

  Iterable<Widget> _subChildren2() sync* {
    yield const SizedBox(height: 16);
    yield Price(
      data.subtotal ?? 0,
    );
    if (data.hasOriginalPrice) {
      yield const SizedBox(height: 4);
      yield RichText(
        softWrap: false,
        text: TextSpan(
          text: '\$ ',
          style: TextStyle(
            fontSize: max(0, 15 - 3),
            // color: color,
          ),
          children: [
            TextSpan(
              text: data.numOfOriginalSubtotal.decimalStyle,
              style: TextStyle(
                fontSize: max(0, 15 - 2),
                color: EfColors.grayTextLight,
                decoration: TextDecoration.lineThrough, // 刪除線
              ),
            ),
          ],
        ),
      );
    }
  }
}
