import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import '../models/members_orders_invoices_res.dart';

class InvoiceItem extends StatelessWidget {
  final MembersOrdersInvoicesRes data;

  const InvoiceItem(
    this.data, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield SizedBox(height: 20.dh);
    yield const Text(
      '交易明細',
      style: TextStyle(
        fontSize: 25,
        color: Colors.black,
        height: 0.8,
      ),
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      textAlign: TextAlign.center,
      softWrap: false,
    );
    yield SizedBox(height: 8.dh);
    yield const Text(
      '詳購物清單',
      style: TextStyle(
        fontSize: 18,
        color: Colors.black,
        height: 1,
      ),
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      textAlign: TextAlign.center,
      softWrap: false,
    );
    yield SizedBox(height: 23.dh);
    yield Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: _elements().toList(growable: false),
    );
  }

  Iterable<Widget> _elements() sync* {
    yield Text(
      '銷售額：\$${data.numOfInvoiceAmount.decimalStyle}',
      style: const TextStyle(
        fontSize: 16,
        color: Colors.black,
        height: 1.25,
      ),
      textHeightBehavior: const TextHeightBehavior(
        applyHeightToFirstAscent: false,
      ),
      softWrap: false,
    );
    yield SizedBox(height: 16.dh);
    yield Text(
      '税別：${data.taxType ?? ''}',
      style: const TextStyle(
        fontSize: 16,
        color: Colors.black,
        height: 1.25,
      ),
      textHeightBehavior: const TextHeightBehavior(
        applyHeightToFirstAscent: false,
      ),
      softWrap: false,
    );
    yield SizedBox(height: 16.dh);
    yield Text(
      '税額：\$${data.numOfTaxAmount.decimalStyle}',
      style: const TextStyle(
        fontSize: 16,
        color: Colors.black,
        height: 1.25,
      ),
      textHeightBehavior: const TextHeightBehavior(
        applyHeightToFirstAscent: false,
      ),
      softWrap: false,
    );
    yield SizedBox(height: 16.dh);
    yield Text(
      '總計：\$${data.numOfInvoiceAmount.decimalStyle}',
      style: const TextStyle(
        fontSize: 16,
        color: Colors.black,
        height: 1.25,
      ),
      textHeightBehavior: const TextHeightBehavior(
        applyHeightToFirstAscent: false,
      ),
      softWrap: false,
    );
    yield SizedBox(height: 30.dh);
    yield Text(
      '發票類型：${data.displayTaxType}-電子發票',
      style: const TextStyle(
        fontSize: 16,
        color: Colors.black,
        height: 1.25,
      ),
      textHeightBehavior: const TextHeightBehavior(
        applyHeightToFirstAscent: false,
      ),
      softWrap: false,
    );
  }
}
