import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/components/thumbnail_image.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

import '../models/members_popup_res.dart';

class EfPopUp extends StatelessWidget {
  final MembersPopupRes data;
  final VoidCallback? onPressed;
  late final WebViewController webViewController;

  EfPopUp(
    this.data, {
    super.key,
    this.onPressed,
  }) {
    _initWebViewController();
    webViewController.loadHtmlString(data.htmlString);
  }

  void _initWebViewController() {
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }
    webViewController = WebViewController.fromPlatformCreationParams(params);
    if (webViewController.platform is AndroidWebViewController) {
      // AndroidWebViewController.enableDebugging(true);
      (webViewController.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }
    webViewController.setJavaScriptMode(JavaScriptMode.unrestricted);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 340.dw,
      // height: 374.dh,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.0),
        border: Border.all(width: 1.0, color: const Color(0xffd5d5d5)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: _children().toList(),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield SizedBox(
      height: 224.dh,
      child: Background(
        alignment: Alignment.topRight,
        // background: const Placeholder(),
        background: ThumbnailImage(data.thumbnail),
        child: CloseButton(
          color: EfColors.grayText,
          onPressed: () {
            Get.back();
          },
        ),
      ),
    );
    yield const SizedBox(height: 16);
    yield Text(
      // '【APP-首購優惠】',
      data.name ?? '',
      style: const TextStyle(
        fontSize: 18,
        color: EfColors.grayText,
      ),
      textAlign: TextAlign.center,
    );
    yield const SizedBox(height: 8);
    yield SizedBox(
      height: 20.dh,
      child: WebViewWidget(controller: webViewController),
    );
    // yield Text(
    //   // '全館滿額1288，現折88元',
    //   data.popupTitle ?? '',
    //   style: const TextStyle(
    //     fontSize: 18,
    //     color: EfColors.grayText,
    //   ),
    //   textAlign: TextAlign.center,
    // );
    yield const SizedBox(height: 16);
    yield ElevatedButton(
      style: ElevatedButton.styleFrom(
        minimumSize: Size(290.dw, 42.dh),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4.0),
        ),
      ),
      onPressed: onPressed,
      // onPressed: () {
      //   var path = data.popupButtonPath ?? '';
      //   if (!path.startsWith('/')) {
      //     path = '/$path';
      //   }
      //   final uri = Uri.parse(path);
      //   if (Get.canLaunchUrl(uri)) {
      //     Get.launchUrl(uri);
      //   }
      // },
      child: Text(
        // '領取會員好禮',
        data.popupButtonTitle ?? '',
        style: const TextStyle(
          fontSize: 18,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
    yield const SizedBox(height: 16);
  }
}
