import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../models/message_data.dart';
import 'background.dart';

class MessageMenu extends StatelessWidget {
  final String titleText;
  final String subtitleText;
  final String iconPath;
  final num badgeCount;
  final VoidCallback? onTap;

  const MessageMenu({
    super.key,
    required this.iconPath,
    this.titleText = '',
    this.subtitleText = '',
    this.badgeCount = 0,
    this.onTap,
  });

  MessageMenu.data(
    MessageData item, {
    super.key,
    this.onTap,
  })  : iconPath = item.iconPath ?? '',
        titleText = item.titleText ?? '',
        subtitleText = item.subtitleText ?? '',
        badgeCount = item.badgeCount ?? 0;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      horizontalTitleGap: 8,
      // minLeadingWidth: 48,
      dense: false,
      tileColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(
        vertical: 0,
        horizontal: 16,
      ),
      leading: _icon(),
      title: Text(
        titleText,
        style: const TextStyle(
          fontSize: 17,
          color: EfColors.gray69,
        ),
        softWrap: false,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        subtitleText,
        style: const TextStyle(
          fontSize: 11,
          color: EfColors.grayTextLight,
        ),
        softWrap: false,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: _trailing(),
      onTap: onTap,
    );
  }

  Widget _trailing() {
    Iterable<Widget> children() sync* {
      yield Badge(
        // count: badgeCount.toInt(),
        label: Text(
          badgeCount.decimalStyle,
          style: const TextStyle(
            fontSize: 10,
            color: Colors.white,
          ),
        ),
        isLabelVisible: badgeCount > 0,
        backgroundColor: EfColors.primary,
      );
      yield const Icon(
        Icons.chevron_right,
        color: EfColors.grayTextLight,
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Widget _icon() {
    return Background(
      alignment: Alignment.centerRight,
      background: const SizedBox.square(dimension: 52),
      child: Container(
        // padding: const EdgeInsets.all(2),
        width: 52,
        height: 52,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: EfColors.primary,
        ),
        child: SvgPicture.asset(
          iconPath,
          fit: BoxFit.contain,
          alignment: Alignment.centerRight,
          width: 52,
          height: 52,
        ),
      ),
    );
  }
}
