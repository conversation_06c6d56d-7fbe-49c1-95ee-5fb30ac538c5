import 'package:efshop/constants.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SignInButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String icon;
  final String text;
  final Color backgroundColor;

  const SignInButton({
    super.key,
    this.onPressed,
    required this.icon,
    required this.text,
    required this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Constants.buttonHeight.dh,
      child: ClipRRect(
        borderRadius: Constants.buttonBorderRadius,
        child: TextButton(
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            backgroundColor: backgroundColor,
            // shape: RoundedRectangleBorder(
            //   borderRadius: Constants.buttonBorderRadius,
            // ),
          ),
          onPressed: onPressed,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                icon,
                width: Constants.buttonHeight.dw,
                height: Constants.buttonHeight.dh,
                // color: EfColors.line,
              ),
              Container(
                width: 1.dw,
                color: Colors.white,
              ),
              Expanded(
                child: Text(
                  text,
                  style: TextStyle(
                    fontSize: Constants.buttonFontSize.dsp,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
