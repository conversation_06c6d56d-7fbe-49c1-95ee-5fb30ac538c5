import 'package:flutter/material.dart';

class Background extends StatelessWidget {
  final Widget background;
  final Widget child;
  final AlignmentGeometry alignment;

  const Background({
    super.key,
    required this.background,
    required this.child,
    this.alignment = AlignmentDirectional.center,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: alignment,
      children: [
        background,
        child,
      ],
    );
  }
}
