import 'package:efshop/app/models/message_data.dart';
import 'package:efshop/ef_colors.dart';
import 'package:flutter/material.dart';

class MessageItem extends StatelessWidget {
  final VoidCallback? onTap;
  final MessageData data;

  const MessageItem(
    this.data, {
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return _body();
  }

  Widget _body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield ColoredBox(
      color: EfColors.grayD5,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 4,
        ),
        child: Text(
          // '2022-09-14 18:29',
          data.createDatetime ?? '',
          style: const TextStyle(
            fontSize: 12,
            color: Colors.white,
            // backgroundColor: EfColors.grayD5,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
    yield const SizedBox(height: 10);
    yield Container(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
        top: 10,
        bottom: 10,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: _items().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _items() sync* {
    yield const Text(
      '客服回覆',
      // data.title ?? '',
      style: TextStyle(
        fontSize: 12,
        color: EfColors.grayTextDark,
      ),
      softWrap: false,
    );
    yield const SizedBox(height: 6);
    yield const Divider(height: 1);

    var needSeparator = false;
    if (data.titleText != null && data.titleText!.isNotEmpty) {
      yield const SizedBox(height: 10);
      // create date time
      if (data.createDatetime != null && data.createDatetime!.isNotEmpty) {
        yield Text(
          // '2022-09-14 18:29',
          data.createDatetime ?? '',
          style: const TextStyle(
            fontSize: 12,
            color: EfColors.grayA7,
          ),
        );
      }
      yield Text(
        // '您好：\n您所登記的商品「機能防水連帽外套（黑L）」目前已經到貨，請您立即購買，我們會為您安排出貨。\n此通知並不會預先保留商品，亦不保留您所登記時的當下價格，商品補貨後將直接於賣場中開放銷售，期間內如有其他消費者早一步購買，仍有可能因貨量銷售太快而導致您購買不到，如有該情形發生，則請再次重新登記，敬請見諒。',
        data.titleText ?? '',
        style: const TextStyle(
          fontSize: 12,
          color: EfColors.grayA7,
        ),
        // softWrap: true,
      );
      needSeparator = true;
    }

    if (data.subtitleText != null && data.subtitleText!.isNotEmpty) {
      if (needSeparator == true) {
        yield const SizedBox(height: 10);
        yield const Divider(height: 1);
      }
      // reply date time
      if (data.replyDatetime != null && data.replyDatetime!.isNotEmpty) {
        yield Text(
          // '2022-09-14 18:29',
          data.replyDatetime ?? '',
          style: const TextStyle(
            fontSize: 12,
            color: EfColors.grayA7,
          ),
        );
      }
      yield ListTile(
        // horizontalTitleGap: 0,
        // minVerticalPadding: 8,
        // minLeadingWidth: 0,
        titleAlignment: ListTileTitleAlignment.center,
        onTap: onTap,
        visualDensity: const VisualDensity(
          horizontal: 0,
          vertical: VisualDensity.minimumDensity,
        ),
        contentPadding: EdgeInsets.zero,
        // dense: true,
        title: Text(
          // '您好：\n您所登記的商品「機能防水連帽外套（黑L）」目前已經到貨，請您立即購買，我們會為您安排出貨。\n此通知並不會預先保留商品，亦不保留您所登記時的當下價格，商品補貨後將直接於賣場中開放銷售，期間內如有其他消費者早一步購買，仍有可能因貨量銷售太快而導致您購買不到，如有該情形發生，則請再次重新登記，敬請見諒。',
          data.subtitleText ?? '',
          style: const TextStyle(
            fontSize: 12,
            color: EfColors.grayA7,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: const Icon(
          Icons.chevron_right,
        ),
      );
    }
  }
}
