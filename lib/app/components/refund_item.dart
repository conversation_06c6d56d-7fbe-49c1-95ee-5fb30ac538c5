import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../models/members_orders_refund_product.dart';
import '../models/product.dart';
import 'plus_minus.dart';
import 'price.dart';
import 'thumbnail_image.dart';

class RefundItem extends StatelessWidget {
  final Product data;
  final MembersOrdersRefundProduct draft;
  final VoidCallback? onPlus;
  final VoidCallback? onMinus;
  final VoidCallback? onTap;
  final ValueSetter<bool?>? onCheckboxChanged;

  const RefundItem(
    this.data,
    this.draft, {
    super.key,
    this.onPlus,
    this.onMinus,
    this.onTap,
    this.onCheckboxChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield SizedBox(height: 10.dh);
    yield SizedBox(
      height: 118.dh,
      child: _body(),
    );
    yield SizedBox(height: 14.dh);
    yield const Divider(height: 1);
    if (draft.hasReason) {
      yield ListTile(
        onTap: onTap,
        title: Text(
          // '尺寸太小',
          draft.refundReason.display,
          style: const TextStyle(
            fontSize: 15,
            color: EfColors.gray6B,
          ),
          softWrap: false,
        ),
        trailing: const Icon(Icons.chevron_right),
      );
    }
  }

  Widget _body() {
    Iterable<Widget> children() sync* {
      yield Checkbox(
        splashRadius: 20.dw,
        value: draft.hasReason,
        onChanged: onCheckboxChanged,
        shape: const CircleBorder(),
        activeColor: EfColors.primary,
      );
      yield SizedBox(
        width: 78.dw,
        height: 88.dh,
        // child: const Placeholder(),
        child: ThumbnailImage(data.thumbnail),
      );
      yield const SizedBox(width: 14);
      yield Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _productInfo().toList(growable: false),
      ).expanded();
      yield const SizedBox(width: 12);
      yield Align(
        alignment: Alignment.topCenter,
        child: Price(
          data.numOfFinalPrice,
          originalPrice: data.numOfOriginalPrice,
        ).paddingSymmetric(vertical: 4.dh),
      );
      yield SizedBox(width: 14.dw);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Iterable<Widget> _productInfo() sync* {
    yield Text(
      // '保暖柔軟舒適=升溫保暖拉鍊口袋內刷毛長褲',
      data.displayRefundProductName,
      style: const TextStyle(
        fontSize: 13,
        color: EfColors.gray47,
      ),
      softWrap: true,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
    yield Text(
      // '深藍',
      data.productSpecName ?? '',
      style: const TextStyle(
        fontSize: 13,
        color: EfColors.gray93,
      ),
      softWrap: false,
    );
    yield const Spacer();
    yield PlusMinus(
      title: '${draft.numOfQuantity}',
      onPlus: onPlus,
      onMinus: onMinus,
    );
  }
}
