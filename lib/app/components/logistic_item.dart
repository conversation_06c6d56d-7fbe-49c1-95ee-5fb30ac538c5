import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import '../models/members_messages_ship.dart';
import 'thumbnail_image.dart';

class LogisticItem extends StatelessWidget {
  final MembersMessagesShip data;
  final VoidCallback? onTap;

  const LogisticItem(
    this.data, {
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield Text(
      // '2022-09-14 18:29',
      data.createDatetime ?? '',
      style: const TextStyle(
        fontSize: 12,
        color: Colors.white,
        backgroundColor: EfColors.grayD5,
      ),
      textAlign: TextAlign.center,
    );
    yield const SizedBox(height: 10);
    yield Container(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
        top: 10,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            // '門市取件通知（65838661）',
            data.title ?? '',
            style: const TextStyle(
              fontSize: 12,
              color: EfColors.grayTextDark,
            ),
            softWrap: false,
          ),
          const SizedBox(height: 6),
          const Divider(height: 1),
          ListTile(
            // minLeadingWidth: 0,
            horizontalTitleGap: 8,
            onTap: onTap,
            subtitleTextStyle: const TextStyle(
              fontSize: 12,
              color: EfColors.grayTextLight,
            ),
            titleTextStyle: const TextStyle(
              fontSize: 13,
              color: EfColors.gray6D,
            ),
            contentPadding: EdgeInsets.zero,
            leading: SizedBox.square(
              dimension: 42,
              child: ThumbnailImage(data.thumbnail),
            ),
            title: Text(
              // '純色連袖吸排運動上衣',
              data.productName ?? '',
            ),
            subtitle: Text(
              '共${data.numOfQuantity.decimalStyle}件商品',
            ),
            trailing: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }
}
