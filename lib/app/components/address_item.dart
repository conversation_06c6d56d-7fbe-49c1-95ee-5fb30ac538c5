import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../models/members_address_res.dart';

class AddressItem extends StatelessWidget {
  final MembersAddressRes data;
  final VoidCallback? onDefaultPressed;
  final VoidCallback? onEditPressed;
  final VoidCallback? onDeletePressed;

  const AddressItem(
    this.data, {
    super.key,
    required this.onDefaultPressed,
    required this.onEditPressed,
    required this.onDeletePressed,
  });

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: 20);
    yield Text(
      // '王小明',
      data.receiverName ?? '',
      style: const TextStyle(
        fontSize: 14,
        color: EfColors.gray,
      ),
      softWrap: false,
    ).paddingSymmetric(horizontal: 14);
    yield Text(
      // '7-11 三灣門市',
      data.storeName ?? '',
      style: const TextStyle(
        fontSize: 14,
        color: EfColors.gray,
      ),
      softWrap: false,
    ).paddingSymmetric(horizontal: 14);
    yield Text(
      // '苗栗縣三灣鄉中正路255號',
      data.address ?? '',
      style: const TextStyle(
        fontSize: 14,
        color: EfColors.gray,
      ),
      softWrap: false,
    ).paddingSymmetric(horizontal: 14);
    yield const SizedBox(height: 20);
    yield const Divider(
      height: 1,
      thickness: 1,
      color: EfColors.grayF6,
    );
    yield SizedBox(
      height: 44,
      child: _bottom(),
    );
  }

  Widget _bottom() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(width: 10);
      yield TextButton.icon(
        style: TextButton.styleFrom(
          padding: EdgeInsets.zero,
        ),
        onPressed: onDefaultPressed,
        icon: data.isDefault == true
            ? const Icon(
                Icons.check_circle_rounded,
                size: 18,
                color: EfColors.primary,
              )
            : const Icon(
                Icons.circle_outlined,
                size: 18,
                color: EfColors.gray,
              ),
        label: const Text(
          '預設',
          style: TextStyle(
            fontSize: 14,
            color: EfColors.gray,
          ),
          softWrap: false,
        ),
      );
      yield const Spacer();
      yield OutlinedButton(
        onPressed: onEditPressed,
        style: OutlinedButton.styleFrom(
          side: const BorderSide(
            width: 1,
            color: Color(0xffe0e0e0),
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(2)),
          ),
        ),
        child: const Text(
          '編輯',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          textAlign: TextAlign.center,
        ),
      ).sizedBox(width: 74, height: 30);
      yield const SizedBox(width: 8);
      yield OutlinedButton(
        onPressed: onDeletePressed,
        style: OutlinedButton.styleFrom(
          fixedSize: const Size(74, 30),
        ),
        child: const Text(
          '刪除',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          textAlign: TextAlign.center,
        ),
      ).sizedBox(width: 74, height: 30);
      yield const SizedBox(width: 14);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }
}
