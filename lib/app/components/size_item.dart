import 'package:efshop/app/components/background.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../models/product_series_res.dart';

class SizeItem extends StatelessWidget {
  final ProductSeriesRes data;
  final VoidCallback? onTap;
  final bool selected;

  const SizeItem(
    this.data, {
    this.selected = true,
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: SizedBox(
        width: 54.dw,
        height: 46.dh,
        child: Background(
          background: Visibility(
            visible: data.isAvailableForSale,
            replacement: _dotRect(),
            child: const SizedBox(),
          ),
          child: _body(),
        ),
      ),
    );
  }

  Widget _dotRect() {
    return SvgPicture.asset(
      'assets/images/dot_rect.svg',
      width: 54.dw,
      height: 46.dh,
      fit: BoxFit.fill,
      allowDrawingOutsideViewBox: true,
    );
  }

  Widget _body() {
    return Background(
      alignment: Alignment.bottomRight,
      background: _background(),
      child: _checkedIcon(),
    );
  }

  Widget _checkedIcon() {
    return Visibility(
      visible: selected,
      child: Visibility(
        visible: data.isAvailableForSale,
        replacement: SvgPicture.asset(
          'assets/images/icon_checked_gray.svg',
          width: 20.dw,
          height: 20.dh,
        ),
        child: SvgPicture.asset(
          'assets/images/icon_checked.svg',
          width: 20.dw,
          height: 20.dh,
        ),
      ),
    );
  }

  Widget _background() {
    return DecoratedBox(
      decoration: BoxDecoration(
        // color: Colors.white,
        borderRadius: BorderRadius.circular(6.0),
        border: _border(),
      ),
      child: Align(
        alignment: Alignment.center,
        child: Text(
          // 'S',
          data.size ?? '',
          style: const TextStyle(
            fontSize: 16,
            color: EfColors.grayTextDark,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  BoxBorder? _border() {
    if (data.isAvailableForSale) {
      if (selected) {
        return Border.all(
          width: 2.0,
          color: EfColors.primary,
        );
      }
      return Border.all(
        width: 1.0,
        color: EfColors.border,
      );
    }
    return null;
  }
}
