import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';

class SnowBall {
  double x;
  double y;
  double radius;
  double density;
  double rotation = 0;
  bool clockwise;

  SnowBall({
    required this.x, // X-Axis
    required this.y, // Y-Axis
    required this.radius, // Radius of the ball
    required this.density, // Density of the ball
    required this.clockwise,
  });

  num get clockwiseValue => clockwise ? 1.0 : -1.0;
}

class SnowPainter extends CustomPainter {
  final List<SnowBall> snows;
  final bool isRunning;
  final Color snowColor;
  final bool hasSpinningEffect;
  final ui.Image? image;

  SnowPainter({
    required this.isRunning,
    required this.snows,
    required this.snowColor,
    required this.hasSpinningEffect,
    required this.image,
  });

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint;

    if (hasSpinningEffect) {
      paint = Paint()
        ..shader = LinearGradient(
          colors: [snowColor, snowColor.withOpacity(0.6)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          tileMode: TileMode.mirror,
        ).createShader(Rect.fromCircle(
          center: const Offset(0, 0),
          radius: 15,
        ));
    } else {
      paint = Paint()..color = snowColor;
    }

    for (int i = 0; i < snows.length; i++) {
      SnowBall snow = snows[i];
      if (image != null) {
        if (snow.y != -10) {
          // canvas.drawImage(image!, Offset(snow.x, snow.y), paint);
          final srcRect = Offset.zero &
              Size(image!.width.toDouble(), image!.height.toDouble());
          final dstRect =
              Offset(snow.x, snow.y) & Size(snow.radius * 2, snow.radius * 2);

          canvas.save();
          canvas.translate(snow.x + snow.radius, snow.y + snow.radius);
          // snow.rotation is the rotation angle in radians
          canvas.rotate(snow.rotation);
          canvas.translate(-snow.x - snow.radius, -snow.y - snow.radius);
          canvas.drawImageRect(image!, srcRect, dstRect, paint);
          canvas.restore();
        }
      } else {
        canvas.drawCircle(Offset(snow.x, snow.y), snow.radius, paint);
      }
    }
  }

  @override
  bool shouldRepaint(SnowPainter oldDelegate) => isRunning;
}

const double angleIncrementation = 0.01;

class SnowWidget extends StatefulWidget {
  ///
  /// Give the amount of particles to display on the screen
  ///
  final int totalSnow;

  ///
  /// Give the speed of the snow particles
  /// note that the velocity of each ball will depend on the its size
  /// (radius)
  /// The bigger snow balls will fall faster and the smaller snow balls will
  /// fall slower
  ///
  final double speed;

  ///
  /// Tells whether the animation is starting or not
  ///
  final bool isRunning;

  ///
  /// Give the max radius size of the snow ball object
  ///
  final double maxRadius;

  ///
  /// Give the main color of the Snowball
  ///
  final Color snowColor;

  ///
  /// Display the linear gradient with  [snowColor] and [Colors.white60] on the snowball
  /// if true else just display given [snowColor]
  ///
  final bool hasSpinningEffect;

  ///
  /// Start the snowing animation from the top if set to true
  /// otherwise start from the whole screens boundaries
  ///
  final bool startSnowing;

  ///
  /// If true , set a linear fall off otherwise stormy falls
  ///
  final bool linearFallOff;

  final String imageUrl;

  const SnowWidget({
    super.key,
    required this.totalSnow,
    required this.speed,
    required this.isRunning,
    required this.snowColor,
    this.maxRadius = 4,
    this.linearFallOff = false,
    this.hasSpinningEffect = true,
    this.startSnowing = false,
    this.imageUrl = '',
  });

  @override
  State createState() => _SnowWidgetState();
}

class _SnowWidgetState extends State<SnowWidget>
    with SingleTickerProviderStateMixin {
  late final AnimationController controller;
  late final Animation animation;

  var W = 0.0;
  var H = 0.0;

  final _rnd = Random();
  final _snows = <SnowBall>[];
  var angle = 0.0;

  @override
  void initState() {
    super.initState();
  }

  static const double angleIncrementation = 0.01;

  Future<void> update() async {
    angle += angleIncrementation;

    if (widget.totalSnow != _snows.length) {
      await _createSnowBall(newBallToAdd: widget.totalSnow);
    }

    for (int i = 0; i < widget.totalSnow; i++) {
      final SnowBall snow = _snows.elementAt(i);
      final double sinX = widget.linearFallOff ? snow.density : snow.radius;

      /// make the snow heavier, faster for bigger snow balls
      snow.y += (cos(angle + snow.density) + snow.radius).abs() * widget.speed;
      snow.x += sin(sinX) * 2 * widget.speed;
      snow.rotation += snow.density * snow.clockwiseValue;

      // If the flake is exiting widget parent's frame
      if (snow.x > W + (snow.radius) ||
          snow.x < -(snow.radius) ||
          snow.y > H + (snow.radius) ||
          snow.y < -(snow.radius)) {
        if (i % 4 > 0) {
          _snows[i] = SnowBall(
            x: _rnd.nextDouble() * W,
            y: -10,
            radius: snow.radius,
            density: snow.density,
            clockwise: _rnd.nextBool(),
          );
        } else if (i % 5 > 0) {
          _snows[i] = SnowBall(
            x: (_rnd.nextDouble() * W) - _rnd.nextDouble() * 10,
            y: 0,
            radius: snow.radius,
            density: snow.density,
            clockwise: _rnd.nextBool(),
          );
        } else {
          _snows[i] = SnowBall(
            x: (_rnd.nextDouble() * W) - _rnd.nextDouble() * 10,
            y: -_rnd.nextDouble() * 10,
            radius: snow.radius,
            density: snow.density,
            clockwise: _rnd.nextBool(),
          );
        }
      }
    }
  }

  Future<void> _createSnowBall({required int newBallToAdd}) async {
    final int inverseYAxis = widget.startSnowing ? -1 : 1;

    for (int i = 0; i < newBallToAdd; i++) {
      final double radius = _rnd.nextDouble() * widget.maxRadius + 2;
      final double density = _rnd.nextDouble() * widget.speed;

      final double x = _rnd.nextDouble() * W;

      /// if [widget.startSnowing] is true the we reverse the Y axis
      /// so that it lies outside of the frame to give
      /// the feeling of starting snow.
      /// otherwise just keep the Y axis as is.
      final double y = _rnd.nextDouble() * H * inverseYAxis;

      _snows.add(
        SnowBall(
          x: x,
          y: y,
          radius: radius,
          density: density,
          clockwise: _rnd.nextBool(),
        ),
      );
    }
  }

  Future<void> init({bool hasInit = false, int previousTotalSnow = 0}) async {
    W = MediaQuery.of(context).size.width;
    H = MediaQuery.of(context).size.height;

    if (hasInit) {
      /// only reset balls after the first init is done
      await _createSnowBall(newBallToAdd: 0);
    } else {
      controller = AnimationController(
          lowerBound: 0,
          upperBound: 1,
          vsync: this,
          duration: const Duration(milliseconds: 5000))
        ..addListener(() {
          if (mounted) {
            setState(() {
              update();
            });
          }
        });

      controller.repeat();
    }
  }

  bool _hasParametersChanged(covariant SnowWidget oldWidget) {
    // check only parameters that are used for initialization
    return oldWidget.startSnowing != widget.startSnowing ||
        oldWidget.totalSnow != widget.totalSnow ||
        oldWidget.maxRadius != widget.maxRadius ||
        oldWidget.snowColor != widget.snowColor ||
        oldWidget.startSnowing != widget.startSnowing;
  }

  @override
  void didUpdateWidget(covariant SnowWidget oldWidget) {
    if (_hasParametersChanged(oldWidget)) {
      init(hasInit: true, previousTotalSnow: oldWidget.totalSnow);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Initialize snowballs and start animation in didChangeDependencies
    if (_snows.isEmpty) {
      init();
    }
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  Future<ui.Image> _loadImage(String imageUrl) async {
    final completer = Completer<ui.Image>();
    final image = NetworkImage(imageUrl);
    const config = ImageConfiguration();
    image.resolve(config).addListener(
          ImageStreamListener(
            (ImageInfo info, bool _) => completer.complete(info.image),
          ),
        );
    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isRunning && !controller.isAnimating) {
      controller.repeat();
    }

    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
      /// update Boundaries when Constraints change
      W = constraints.maxWidth;
      H = constraints.maxHeight;

      return FutureBuilder<ui.Image>(
        future: _loadImage(widget.imageUrl),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            // return CustomPaint(
            //   painter: ImagePainter(snapshot.data),
            // );
            return CustomPaint(
              willChange: widget.isRunning,
              isComplex: true,
              size: Size.infinite,
              painter: SnowPainter(
                image: snapshot.data,
                isRunning: widget.isRunning,
                snows: _snows,
                snowColor: widget.snowColor,
                hasSpinningEffect: widget.hasSpinningEffect,
              ),
            );
          } else {
            return const Center(child: CircularProgressIndicator());
          }
        },
      );
    });
  }
}
