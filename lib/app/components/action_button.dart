import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ActionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget icon;
  final Widget? selectedIcon;
  final Widget text;
  final EdgeInsetsGeometry iconMargin;
  final num badgeCount;
  final bool isSelected;
  final Offset? badgeOffset;

  const ActionButton({
    super.key,
    this.onPressed,
    required this.icon,
    required this.text,
    this.selectedIcon,
    this.iconMargin = EdgeInsets.zero,
    this.badgeCount = 0,
    this.isSelected = false,
    this.badgeOffset,
  });

  factory ActionButton.menuStyle1({
    VoidCallback? onPressed,
    required String icon,
    required String text,
    num badgeCount = 0,
    bool isSelected = false,
    EdgeInsetsGeometry? iconMargin,
    Offset? badgeOffset,
  }) {
    return ActionButton(
      badgeCount: badgeCount,
      isSelected: isSelected,
      onPressed: onPressed,
      icon: SvgPicture.asset(
        icon,
        width: 24.dw,
        height: 24.dh,
      ),
      text: Text(
        text,
        style: TextStyle(
          fontSize: 12.dsp,
          color: EfColors.grayText,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      ),
      iconMargin: iconMargin ??
          EdgeInsets.only(
            bottom: 2.dh,
          ),
      badgeOffset: badgeOffset,
    );
  }

  factory ActionButton.menu({
    VoidCallback? onPressed,
    required String icon,
    required String text,
    num badgeCount = 0,
    Offset? badgeOffset,
    bool isSelected = false,
  }) {
    return ActionButton(
      badgeOffset: badgeOffset,
      badgeCount: badgeCount,
      isSelected: isSelected,
      onPressed: onPressed,
      icon: SvgPicture.asset(
        icon,
        width: 40.dw,
        height: 32.dh,
      ),
      text: Text(
        text,
        style: const TextStyle(
          fontSize: 13,
          color: EfColors.grayTextDark,
          fontWeight: FontWeight.w300,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      ),
      iconMargin: EdgeInsets.only(
        bottom: 2.dh,
      ),
    );
  }

  factory ActionButton.bottom({
    VoidCallback? onPressed,
    required String icon,
    required String text,
    String? selectedIcon,
    int badgeCount = 0,
    bool isSelected = false,
    Offset? badgeOffset,
  }) {
    return ActionButton(
      badgeOffset: badgeOffset,
      badgeCount: badgeCount,
      isSelected: isSelected,
      onPressed: onPressed,
      icon: SvgPicture.asset(
        icon,
        width: 34.dw,
        height: 34.dw,
      ),
      selectedIcon: SvgPicture.asset(
        selectedIcon ?? icon,
        width: 34.dw,
        height: 34.dw,
      ),
      text: Text(
        text,
        style: TextStyle(
          fontSize: 13.dsp,
          color: EfColors.primary,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      ),
      iconMargin: EdgeInsets.only(
        bottom: 2.dh,
      ),
    );
  }

  factory ActionButton.top({
    VoidCallback? onPressed,
    required String icon,
    required String text,
    num badgeCount = 0,
  }) {
    return ActionButton(
      badgeCount: badgeCount,
      onPressed: onPressed,
      icon: SvgPicture.asset(
        icon,
        width: 30.dw,
        height: 30.dw,
      ),
      text: Text(
        text,
        style: TextStyle(
          fontSize: 12.dsp,
          color: EfColors.grayTextDark,
          fontWeight: FontWeight.w300,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      ),
      iconMargin: EdgeInsets.only(
        bottom: 2.dh,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return TextButton(
      style: TextButton.styleFrom(
        padding: EdgeInsets.zero,
      ),
      onPressed: onPressed,
      child: Badge.count(
        offset: badgeOffset,
        isLabelVisible: badgeCount > 0,
        count: badgeCount.toInt(),
        child: Tab(
          icon: Visibility(
            visible: isSelected == true && selectedIcon != null,
            replacement: icon,
            child: selectedIcon ?? const SizedBox(),
          ),
          // height: 10,
          iconMargin: iconMargin,
          child: text,
        ),
      ),
      // child: Background(
      //   background: Tab(
      //     icon: Visibility(
      //       visible: isSelected == true && selectedIcon != null,
      //       replacement: icon,
      //       child: selectedIcon ?? const SizedBox(),
      //     ),
      //     // height: 10,
      //     iconMargin: iconMargin,
      //     child: text,
      //   ),
      //   child: Visibility(
      //     visible: _showBadge,
      //     child: Transform.translate(
      //       offset: Offset(15.dw, -22.dh),
      //       child: _badge(),
      //     ),
      //   ),
      // ),
    );
  }

  // Widget _badge() {
  //   return StadiumButton(
  //     padding: Constants.chipPadding,
  //     child: Text(
  //       badgeCount.toString(),
  //       style: TextStyle(
  //         fontSize: 13.dsp,
  //         color: Colors.white,
  //       ),
  //       textAlign: TextAlign.center,
  //       softWrap: false,
  //     ),
  //     // width: 80.dw,
  //     // height: 80.dw,
  //   );
  // }
}
