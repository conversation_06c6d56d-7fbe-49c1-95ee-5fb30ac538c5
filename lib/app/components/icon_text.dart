import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/widgets.dart';

import '../models/message_data.dart';

class IconText extends StatelessWidget {
  final MessageData data;
  final double iconSize;

  const IconText(
    this.data, {
    super.key,
    this.iconSize = 60,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    if (data.iconPath != null && data.iconPath!.isNotEmpty) {
      yield Image.asset(
        // 'assets/images/seven_eleven.png',
        data.iconPath ?? '',
        fit: BoxFit.contain,
        width: iconSize,
        height: iconSize,
      );
    }
    yield SizedBox(width: 8.dw);
    yield Text(
      // '完成退貨申請',
      data.titleText ?? '',
      style: const TextStyle(
        fontSize: 14,
        color: EfColors.gray47,
        fontWeight: FontWeight.w600,
      ),
      softWrap: false,
    );
  }
}
