import 'package:flutter/material.dart';

class ColoredDivider extends StatelessWidget {
  final Color backgroundColor;

  const ColoredDivider({
    super.key,
    this.backgroundColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: backgroundColor,
      child: const Divider(
        height: 1,
        indent: 14,
        endIndent: 14,
      ),
    );
  }
}
