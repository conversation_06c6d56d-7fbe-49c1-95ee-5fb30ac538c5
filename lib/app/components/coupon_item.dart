import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import '../models/voucher_res.dart';

class CouponItem extends StatelessWidget {
  final VoucherRes data;
  final VoidCallback? onTap;

  const CouponItem(
    this.data, {
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 348.dw,
      height: 178.dh,
      padding: EdgeInsets.only(
        left: 14.dw,
        right: 14.dw,
        top: 10.dh,
        bottom: 12.dh,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.0),
        border: Border.all(width: 1.0, color: const Color(0xffd5d5d5)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    // yield const SizedBox(height: 10);
    yield Row(
      children: [
        Flexible(
          fit: FlexFit.tight,
          child: Text(
            // '現金88元',
            data.title ?? '',
            style: TextStyle(
              fontSize: 30,
              color: data.textColor,
              fontWeight: FontWeight.w600,
            ),
            softWrap: false,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Visibility(
          visible: !data.isAvailable,
          child: Text(
            // '已失效',
            data.displayStatus,
            style: const TextStyle(
              fontSize: 14,
              color: EfColors.gray93,
            ),
            textAlign: TextAlign.right,
            softWrap: false,
          ),
        ),
      ],
    );
    yield const SizedBox(height: 8);
    yield Flexible(
      fit: FlexFit.tight,
      child: Text(
        // '全館滿額1288\nAPP會員首購禮',
        data.subTitle,
        style: const TextStyle(
          fontSize: 14,
          color: EfColors.gray93,
          fontWeight: FontWeight.w500,
        ),
        softWrap: true,
        overflow: TextOverflow.ellipsis,
        maxLines: 2,
      ),
    );
    yield Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          fit: FlexFit.tight,
          child: Text(
            // '有效期至：2023-12-20 07:11',
            '有效期至：${data.displayExpireAt}',
            style: const TextStyle(
              fontSize: 14,
              color: EfColors.gray93,
            ),
            softWrap: false,
          ),
        ),
        Visibility(
          visible: data.canTap,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.zero,
              disabledBackgroundColor: EfColors.grayLight,
              backgroundColor: EfColors.grayBB,
              shape: const StadiumBorder(),
              // shape: RoundedRectangleBorder(
              //   borderRadius: BorderRadius.circular(4.0),
              // ),
              // side: BorderSide.none,
              // fixedSize: Size(82.dw, 28.dh),
            ),
            onPressed: data.canTap ? onTap : null,
            child: const Text(
              '馬上選購',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ).sizedBox(width: 82, height: 36),
        ),
      ],
    );
    // yield const SizedBox(height: 12);
  }
}
