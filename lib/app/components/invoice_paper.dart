import 'package:barcode/barcode.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../models/members_orders_invoices_res.dart';

class InvoicePaper extends StatelessWidget {
  final MembersOrdersInvoicesRes data;

  const InvoicePaper(
    this.data, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 210.dw,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield SizedBox(height: 20.dh);
    yield Image.asset(
      'assets/images/logo.png',
      width: 58.dw,
      height: 58.dh,
    );
    yield SizedBox(height: 4.dh);
    yield const Text(
      '電子發票證明聯',
      style: TextStyle(
        fontSize: 25,
        color: Colors.black,
        height: 0.8,
      ),
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      textAlign: TextAlign.center,
      softWrap: false,
    );
    yield SizedBox(height: 4.dh);
    yield Text(
      // '112年7-8月',
      data.displayInvoiceDate,
      style: const TextStyle(
        fontSize: 25,
        color: Colors.black,
        fontWeight: FontWeight.w600,
        height: 0.8,
      ),
      textHeightBehavior: const TextHeightBehavior(
        applyHeightToFirstAscent: false,
      ),
      textAlign: TextAlign.center,
      softWrap: false,
    );
    yield SizedBox(height: 4.dh);
    yield Text(
      // 'QZ-9220009',
      data.invoiceNumber ?? '',
      style: const TextStyle(
        fontSize: 23,
        color: Colors.black,
        fontWeight: FontWeight.w600,
        height: 0.87,
      ),
      textHeightBehavior: const TextHeightBehavior(
        applyHeightToFirstAscent: false,
      ),
      textAlign: TextAlign.center,
      softWrap: false,
    );
    yield SizedBox(height: 6.dh);
    yield _LeftRight(
      // leftText: '2023-07-05',
      leftText: data.invoiceDate ?? '',
    );
    yield SizedBox(height: 6.dh);
    yield _LeftRight(
      leftText: '隨機碼${data.randomNumber ?? ''}',
      rightText: '總計${data.numOfInvoiceAmount.decimalStyle}元',
    );
    yield SizedBox(height: 6.dh);
    yield _LeftRight(
      leftText: '賣方${data.displaySellerBan}',
      rightText: data.numOfTaxAmount == 0 ? '' : '買方${data.displayBuyerBan}',
    );
    yield SizedBox(height: 18.dh);
    yield SvgPicture.string(
      Barcode.code39().toSvg(
        data.code39 ?? '',
        width: 175.dw,
        height: 25.dh,
        drawText: false,
        // fontFamily: 'Libre Barcode 39',
      ),
    );
    yield SizedBox(height: 18.dh);
    yield Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.string(
          // leftQrSvgString,
          Barcode.qrCode(typeNumber: 6).toSvg(
            data.qrcodeLeft ?? '',
            height: 88.dh,
            width: 88.dw,
          ),
        ),
        SizedBox(width: 20.dh),
        SvgPicture.string(
          // rightQrSvgString,
          Barcode.qrCode(typeNumber: 6).toSvg(
            data.qrcodeRight ?? '',
            height: 88.dh,
            width: 88.dw,
          ),
        ),
      ],
    );
    yield SizedBox(height: 27.dh);
  }
}

class _LeftRight extends StatelessWidget {
  final String leftText;
  final String rightText;
  const _LeftRight({
    super.key,
    this.leftText = '',
    this.rightText = '',
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    if (leftText.isNotEmpty) {
      yield Text(
        leftText,
        style: const TextStyle(
          fontSize: 12,
          color: Colors.black,
          height: 1.67,
        ),
        textHeightBehavior: const TextHeightBehavior(
          applyHeightToFirstAscent: false,
        ),
        softWrap: false,
      );
    }
    yield const Spacer();
    if (rightText.isNotEmpty) {
      yield Text(
        rightText,
        style: const TextStyle(
          fontSize: 12,
          color: Colors.black,
          height: 1.67,
        ),
        textHeightBehavior: const TextHeightBehavior(
          applyHeightToFirstAscent: false,
        ),
        softWrap: false,
      );
    }
  }
}
