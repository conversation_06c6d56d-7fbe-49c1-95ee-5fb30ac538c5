import 'package:get/get.dart';

import '../modules/activity/bindings/activity_binding.dart';
import '../modules/activity/views/activity_view.dart';
import '../modules/address/bindings/address_binding.dart';
import '../modules/address/views/address_view.dart';
import '../modules/address_editor/bindings/address_editor_binding.dart';
import '../modules/address_editor/views/address_editor_view.dart';
import '../modules/address_picker/bindings/address_picker_binding.dart';
import '../modules/address_picker/views/address_picker_view.dart';
import '../modules/arrival/bindings/arrival_binding.dart';
import '../modules/arrival/views/arrival_view.dart';
import '../modules/bank/bindings/bank_binding.dart';
import '../modules/bank/views/bank_view.dart';
import '../modules/bank_editor/bindings/bank_editor_binding.dart';
import '../modules/bank_editor/views/bank_editor_view.dart';
import '../modules/bank_manager/bindings/bank_manager_binding.dart';
import '../modules/bank_manager/views/bank_manager_view.dart';
import '../modules/cart_web/bindings/cart_web_binding.dart';
import '../modules/cart_web/views/cart_web_view.dart';
import '../modules/categories/bindings/categories_binding.dart';
import '../modules/categories/views/categories_view.dart';
import '../modules/categories_page/bindings/categories_page_binding.dart';
import '../modules/categories_page/views/categories_page_view.dart';
import '../modules/category/bindings/category_binding.dart';
import '../modules/category/views/category_view.dart';
import '../modules/chat/bindings/chat_binding.dart';
import '../modules/chat/views/chat_view.dart';
import '../modules/color_picker/bindings/color_picker_binding.dart';
import '../modules/color_picker/views/color_picker_view.dart';
import '../modules/comment/bindings/comment_binding.dart';
import '../modules/comment/views/comment_view.dart';
import '../modules/coupon/bindings/coupon_binding.dart';
import '../modules/coupon/views/coupon_view.dart';
import '../modules/customer_service/bindings/customer_service_binding.dart';
import '../modules/customer_service/views/customer_service_view.dart';
import '../modules/delete_account/bindings/delete_account_binding.dart';
import '../modules/delete_account/views/delete_account_view.dart';
import '../modules/devtool/bindings/devtool_binding.dart';
import '../modules/devtool/views/devtool_view.dart';
import '../modules/ef_android_web/bindings/ef_android_web_binding.dart';
import '../modules/ef_android_web/views/ef_android_web_view.dart';
import '../modules/ef_grid/bindings/ef_grid_binding.dart';
import '../modules/ef_grid/views/ef_grid_view.dart';
import '../modules/ef_ios_web/bindings/ef_ios_web_binding.dart';
import '../modules/ef_ios_web/views/ef_ios_web_view.dart';
import '../modules/ef_menu/bindings/ef_menu_binding.dart';
import '../modules/ef_menu/views/ef_menu_view.dart';
import '../modules/ef_web/bindings/ef_web_binding.dart';
import '../modules/ef_web/views/ef_web_view.dart';
import '../modules/faq/bindings/faq_binding.dart';
import '../modules/faq/views/faq_view.dart';
import '../modules/faq_children/bindings/faq_children_binding.dart';
import '../modules/faq_children/views/faq_children_view.dart';
import '../modules/faq_detail/bindings/faq_detail_binding.dart';
import '../modules/faq_detail/views/faq_detail_view.dart';
import '../modules/favorite/bindings/favorite_binding.dart';
import '../modules/favorite/views/favorite_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/index/bindings/index_binding.dart';
import '../modules/index/views/index_view.dart';
import '../modules/invoice/bindings/invoice_binding.dart';
import '../modules/invoice/views/invoice_view.dart';
import '../modules/leave_comment/bindings/leave_comment_binding.dart';
import '../modules/leave_comment/views/leave_comment_view.dart';
import '../modules/leave_message/bindings/leave_message_binding.dart';
import '../modules/leave_message/views/leave_message_view.dart';
import '../modules/loading/bindings/loading_binding.dart';
import '../modules/loading/views/loading_view.dart';
import '../modules/logistic/bindings/logistic_binding.dart';
import '../modules/logistic/views/logistic_view.dart';
import '../modules/message/bindings/message_binding.dart';
import '../modules/message/views/message_view.dart';
import '../modules/message_detail/bindings/message_detail_binding.dart';
import '../modules/message_detail/views/message_detail_view.dart';
import '../modules/message_history/bindings/message_history_binding.dart';
import '../modules/message_history/views/message_history_view.dart';
import '../modules/not_found/bindings/not_found_binding.dart';
import '../modules/not_found/views/not_found_view.dart';
import '../modules/notification/bindings/notification_binding.dart';
import '../modules/notification/views/notification_view.dart';
import '../modules/order_detail/bindings/order_detail_binding.dart';
import '../modules/order_detail/views/order_detail_view.dart';
import '../modules/order_fail/bindings/order_fail_binding.dart';
import '../modules/order_fail/views/order_fail_view.dart';
import '../modules/order_success/bindings/order_success_binding.dart';
import '../modules/order_success/views/order_success_view.dart';
import '../modules/orders/bindings/orders_binding.dart';
import '../modules/orders/views/orders_view.dart';
import '../modules/password_editor/bindings/password_editor_binding.dart';
import '../modules/password_editor/views/password_editor_view.dart';
import '../modules/password_input/bindings/password_input_binding.dart';
import '../modules/password_input/views/password_input_view.dart';
import '../modules/product/bindings/product_binding.dart';
import '../modules/product/views/product_view.dart';
import '../modules/profile/bindings/profile_binding.dart';
import '../modules/profile/views/profile_view.dart';
import '../modules/profile_editor/bindings/profile_editor_binding.dart';
import '../modules/profile_editor/views/profile_editor_view.dart';
import '../modules/refund/bindings/refund_binding.dart';
import '../modules/refund/views/refund_view.dart';
import '../modules/refund_detail/bindings/refund_detail_binding.dart';
import '../modules/refund_detail/views/refund_detail_view.dart';
import '../modules/refund_success/bindings/refund_success_binding.dart';
import '../modules/refund_success/views/refund_success_view.dart';
import '../modules/sale/bindings/sale_binding.dart';
import '../modules/sale/views/sale_view.dart';
import '../modules/search_result/bindings/search_result_binding.dart';
import '../modules/search_result/views/search_result_view.dart';
import '../modules/search_suggestion/bindings/search_suggestion_binding.dart';
import '../modules/search_suggestion/views/search_suggestion_view.dart';
import '../modules/service/bindings/service_binding.dart';
import '../modules/service/views/service_view.dart';
import '../modules/service_detail/bindings/service_detail_binding.dart';
import '../modules/service_detail/views/service_detail_view.dart';
import '../modules/settings/bindings/settings_binding.dart';
import '../modules/settings/views/settings_view.dart';
import '../modules/shipping_detail/bindings/shipping_detail_binding.dart';
import '../modules/shipping_detail/views/shipping_detail_view.dart';
import '../modules/show_empty_cart/bindings/show_empty_cart_binding.dart';
import '../modules/show_empty_cart/views/show_empty_cart_view.dart';
import '../modules/sign_in/bindings/sign_in_binding.dart';
import '../modules/sign_in/views/sign_in_view.dart';
import '../modules/sign_up/bindings/sign_up_binding.dart';
import '../modules/sign_up/views/sign_up_view.dart';
import '../modules/sign_up_mid/bindings/sign_up_mid_binding.dart';
import '../modules/sign_up_mid/views/sign_up_mid_view.dart';
import '../modules/sign_up_uid/bindings/sign_up_uid_binding.dart';
import '../modules/sign_up_uid/views/sign_up_uid_view.dart';
import '../modules/splash/bindings/splash_binding.dart';
import '../modules/splash/views/splash_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH;

  static final routes = [
    GetPage(
      name: _Paths.INDEX,
      page: () => const IndexView(),
      binding: IndexBinding(),
      transitionDuration: const Duration(milliseconds: 0),
    ),
    GetPage(
      name: _Paths.CATEGORY,
      page: () => CategoryView(),
      binding: CategoryBinding(),
    ),
    GetPage(
      name: '${_Paths.CATEGORY}/:id',
      page: () => CategoryView(),
      binding: CategoryBinding(),
    ),
    GetPage(
      name: '${_Paths.CATEGORY}/:id/:page',
      page: () => CategoryView(),
      binding: CategoryBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE,
      page: () => ProfileView(),
      binding: ProfileBinding(),
    ),
    GetPage(
      name: _Paths.SIGN_IN,
      page: () => SignInView(),
      binding: SignInBinding(),
    ),
    GetPage(
      name: _Paths.SIGN_UP,
      page: () => const SignUpView(),
      binding: SignUpBinding(),
    ),
    GetPage(
      name: _Paths.PASSWORD_INPUT,
      page: () => const PasswordInputView(),
      binding: PasswordInputBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH,
      page: () => const SplashView(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: _Paths.HOME,
      page: () => HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.EF_MENU,
      page: () => EfMenuView(),
      binding: EfMenuBinding(),
    ),
    GetPage(
      name: _Paths.MESSAGE,
      page: () => const MessageView(),
      binding: MessageBinding(),
    ),
    GetPage(
      name: _Paths.SERVICE,
      page: () => const ServiceView(),
      binding: ServiceBinding(),
    ),
    GetPage(
      name: _Paths.FAVORITE,
      page: () => const FavoriteView(),
      binding: FavoriteBinding(),
    ),
    GetPage(
      name: _Paths.ARRIVAL,
      page: () => const ArrivalView(),
      binding: ArrivalBinding(),
    ),
    GetPage(
      name: _Paths.ADDRESS,
      page: () => const AddressView(),
      binding: AddressBinding(),
    ),
    GetPage(
      name: _Paths.ADDRESS_EDITOR,
      page: () => const AddressEditorView(),
      binding: AddressEditorBinding(),
    ),
    GetPage(
      name: _Paths.ADDRESS_PICKER,
      page: () => const AddressPickerView(),
      binding: AddressPickerBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_EDITOR,
      page: () => const ProfileEditorView(),
      binding: ProfileEditorBinding(),
    ),
    GetPage(
      name: _Paths.PASSWORD_EDITOR,
      page: () => const PasswordEditorView(),
      binding: PasswordEditorBinding(),
    ),
    GetPage(
      name: _Paths.SETTINGS,
      page: () => const SettingsView(),
      binding: SettingsBinding(),
    ),
    GetPage(
      name: _Paths.ORDERS,
      page: () => const OrdersView(),
      binding: OrdersBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_DETAIL,
      page: () => const OrderDetailView(),
      binding: OrderDetailBinding(),
    ),
    GetPage(
      name: _Paths.SHIPPING_DETAIL,
      page: () => const ShippingDetailView(),
      binding: ShippingDetailBinding(),
    ),
    GetPage(
      name: _Paths.CUSTOMER_SERVICE,
      page: () => const CustomerServiceView(),
      binding: CustomerServiceBinding(),
    ),
    GetPage(
      name: _Paths.SERVICE_DETAIL,
      page: () => const ServiceDetailView(),
      binding: ServiceDetailBinding(),
    ),
    GetPage(
      name: _Paths.MESSAGE_DETAIL,
      page: () => const MessageDetailView(),
      binding: MessageDetailBinding(),
    ),
    GetPage(
      name: _Paths.LEAVE_MESSAGE,
      page: () => const LeaveMessageView(),
      binding: LeaveMessageBinding(),
    ),
    GetPage(
      name: _Paths.MESSAGE_HISTORY,
      page: () => const MessageHistoryView(),
      binding: MessageHistoryBinding(),
    ),
    GetPage(
      name: _Paths.LEAVE_COMMENT,
      page: () => const LeaveCommentView(),
      binding: LeaveCommentBinding(),
    ),
    GetPage(
      name: _Paths.ACTIVITY,
      page: () => const ActivityView(),
      binding: ActivityBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFICATION,
      page: () => const NotificationView(),
      binding: NotificationBinding(),
    ),
    GetPage(
      name: _Paths.LOGISTIC,
      page: () => const LogisticView(),
      binding: LogisticBinding(),
    ),
    GetPage(
      name: _Paths.FAQ,
      page: () => const FaqView(),
      binding: FaqBinding(),
    ),
    GetPage(
      name: _Paths.FAQ_DETAIL,
      page: () => const FaqDetailView(),
      binding: FaqDetailBinding(),
    ),
    GetPage(
      name: _Paths.FAQ_CHILDREN,
      page: () => const FaqChildrenView(),
      binding: FaqChildrenBinding(),
    ),
    GetPage(
      name: _Paths.DELETE_ACCOUNT,
      page: () => const DeleteAccountView(),
      binding: DeleteAccountBinding(),
    ),
    GetPage(
      name: _Paths.SEARCH_SUGGESTION,
      page: () => SearchSuggestionView(),
      binding: SearchSuggestionBinding(),
    ),
    GetPage(
      name: _Paths.SEARCH_RESULT,
      page: () => const SearchResultView(),
      binding: SearchResultBinding(),
    ),
    GetPage(
      name: _Paths.REFUND,
      page: () => const RefundView(),
      binding: RefundBinding(),
    ),
    GetPage(
      name: _Paths.BANK,
      page: () => const BankView(),
      binding: BankBinding(),
    ),
    GetPage(
      name: _Paths.BANK_MANAGER,
      page: () => const BankManagerView(),
      binding: BankManagerBinding(),
    ),
    GetPage(
      name: _Paths.BANK_EDITOR,
      page: () => const BankEditorView(),
      binding: BankEditorBinding(),
    ),
    GetPage(
      name: _Paths.REFUND_SUCCESS,
      page: () => const RefundSuccessView(),
      binding: RefundSuccessBinding(),
    ),
    GetPage(
      name: _Paths.REFUND_DETAIL,
      page: () => const RefundDetailView(),
      binding: RefundDetailBinding(),
    ),
    GetPage(
      name: _Paths.INVOICE,
      page: () => const InvoiceView(),
      binding: InvoiceBinding(),
    ),
    GetPage(
      name: _Paths.COUPON,
      page: () => const CouponView(),
      binding: CouponBinding(),
    ),
    GetPage(
      name: _Paths.MEMBER_VOUCHER_LIST,
      page: () => const CouponView(),
      binding: CouponBinding(),
    ),
    GetPage(
      name: _Paths.PRODUCT,
      page: () => ProductView(),
      binding: ProductBinding(),
      preventDuplicates: false,
    ),
    GetPage(
      name: '${_Paths.PRODUCT}/:id',
      page: () => ProductView(),
      binding: ProductBinding(),
      preventDuplicates: false,
    ),
    GetPage(
      name: '${_Paths.SHOW_PRODUCT}/:id',
      page: () => ProductView(),
      binding: ProductBinding(),
      preventDuplicates: false,
    ),
    GetPage(
      name: _Paths.CATEGORIES,
      page: () => const CategoriesView(),
      binding: CategoriesBinding(),
    ),
    GetPage(
      name: _Paths.PROMOTION,
      page: () => CategoryView(),
      binding: CategoryBinding(),
    ),
    GetPage(
      name: '${_Paths.PROMOTION}/:id',
      page: () => CategoryView(),
      binding: CategoryBinding(),
    ),
    GetPage(
      name: '${_Paths.PROMOTION}/:id/:page',
      page: () => CategoryView(),
      binding: CategoryBinding(),
    ),
    GetPage(
      name: _Paths.EF_GRID,
      page: () => EfGridView(),
      binding: EfGridBinding(),
    ),
    GetPage(
      name: _Paths.CATEGORIES_PAGE,
      page: () => CategoriesPageView(),
      binding: CategoriesPageBinding(),
    ),
    GetPage(
      name: _Paths.EF_WEB,
      page: () => EfWebView(),
      binding: EfWebBinding(),
    ),
    GetPage(
      name: _Paths.CONTENT,
      page: () => EfWebView(),
      binding: EfWebBinding(),
    ),
    GetPage(
      name: '${_Paths.CONTENT}/:id/:display',
      page: () => EfWebView(),
      binding: EfWebBinding(),
    ),
    GetPage(
      name: '${_Paths.CONTENT}/:display',
      page: () => EfWebView(),
      binding: EfWebBinding(),
    ),
    GetPage(
      name: _Paths.MY_BONUS,
      page: () => EfWebView(),
      binding: EfWebBinding(),
    ),
    GetPage(
      name: _Paths.COMMENT,
      page: () => CommentView(),
      binding: CommentBinding(),
    ),
    GetPage(
      name: _Paths.COLOR_PICKER,
      page: () => ColorPickerView(),
      binding: ColorPickerBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_SUCCESS,
      page: () => OrderSuccessView(),
      binding: OrderSuccessBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_FAIL,
      page: () => OrderFailView(),
      binding: OrderFailBinding(),
    ),
    GetPage(
      name: _Paths.SALE,
      page: () => const SaleView(),
      binding: SaleBinding(),
    ),
    GetPage(
      name: _Paths.CART_WEB,
      page: () => CartWebView(),
      binding: CartWebBinding(),
    ),
    // GetPage(
    //   name: _Paths.CART_FINISH,
    //   page: () => CartWebView(),
    //   binding: CartWebBinding(),
    // ),
    GetPage(
      name: _Paths.CHAT,
      page: () => ChatView(),
      binding: ChatBinding(),
    ),
    GetPage(
      name: _Paths.DEVTOOL,
      page: () => const DevtoolView(),
      binding: DevtoolBinding(),
    ),
    GetPage(
      name: _Paths.SIGN_UP_MID,
      page: () => const SignUpMidView(),
      binding: SignUpMidBinding(),
    ),
    GetPage(
      name: _Paths.SIGN_UP_UID,
      page: () => const SignUpUidView(),
      binding: SignUpUidBinding(),
    ),
    GetPage(
      name: _Paths.EF_ANDROID_WEB,
      page: () => EfAndroidWebView(),
      binding: EfAndroidWebBinding(),
    ),
    GetPage(
      name: _Paths.EF_IOS_WEB,
      page: () => EfIosWebView(),
      binding: EfIosWebBinding(),
    ),
    GetPage(
      name: _Paths.SHOW_EMPTY_CART,
      page: () => ShowEmptyCartView(),
      binding: ShowEmptyCartBinding(),
    ),
    GetPage(
      name: _Paths.LOADING,
      page: () => const LoadingView(),
      binding: LoadingBinding(),
      transitionDuration: const Duration(milliseconds: 0),
    ),
    GetPage(
      name: _Paths.NOT_FOUND,
      page: () => const NotFoundView(),
      binding: NotFoundBinding(),
    ),
  ];
}
