part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const INDEX = _Paths.INDEX;
  static const CATEGORY = _Paths.CATEGORY;
  static const CART = _Paths.CART;
  static const PROFILE = _Paths.PROFILE;
  static const SIGN_IN = _Paths.SIGN_IN;
  static const SIGN_UP = _Paths.SIGN_UP;
  static const PASSWORD_INPUT = _Paths.PASSWORD_INPUT;
  static const SPLASH = _Paths.SPLASH;
  static const HOME = _Paths.HOME;
  static const EF_MENU = _Paths.EF_MENU;
  static const MESSAGE = _Paths.MESSAGE;
  static const SERVICE = _Paths.SERVICE;
  static const FAVORITE = _Paths.FAVORITE;
  static const ARRIVAL = _Paths.ARRIVAL;
  static const ADDRESS = _Paths.ADDRESS;
  static const ADDRESS_EDIT = _Paths.ADDRESS_EDITOR;
  static const ADDRESS_PICKER = _Paths.ADDRESS_PICKER;
  static const PROFILE_EDITOR = _Paths.PROFILE_EDITOR;
  static const PASSWORD_EDITOR = _Paths.PASSWORD_EDITOR;
  static const SETTINGS = _Paths.SETTINGS;
  static const ORDERS = _Paths.ORDERS;
  static const ORDER_DETAIL = _Paths.ORDER_DETAIL;
  static const SHIPPING_DETAIL = _Paths.SHIPPING_DETAIL;
  static const CUSTOMER_SERVICE = _Paths.CUSTOMER_SERVICE;
  static const SERVICE_DETAIL = _Paths.SERVICE_DETAIL;
  static const MESSAGE_DETAIL = _Paths.MESSAGE_DETAIL;
  static const LEAVE_MESSAGE = _Paths.LEAVE_MESSAGE;
  static const MESSAGE_HISTORY = _Paths.MESSAGE_HISTORY;
  static const LEAVE_COMMENT = _Paths.LEAVE_COMMENT;
  static const ACTIVITY = _Paths.ACTIVITY;
  static const NOTIFICATION = _Paths.NOTIFICATION;
  static const LOGISTIC = _Paths.LOGISTIC;
  static const FAQ = _Paths.FAQ;
  static const FAQ_DETAIL = _Paths.FAQ_DETAIL;
  static const FAQ_CHILDREN = _Paths.FAQ_CHILDREN;
  static const DELETE_ACCOUNT = _Paths.DELETE_ACCOUNT;
  static const SEARCH_SUGGESTION = _Paths.SEARCH_SUGGESTION;
  static const SEARCH_RESULT = _Paths.SEARCH_RESULT;
  static const REFUND = _Paths.REFUND;
  static const BANK = _Paths.BANK;
  static const BANK_MANAGER = _Paths.BANK_MANAGER;
  static const BANK_EDITOR = _Paths.BANK_EDITOR;
  static const REFUND_SUCCESS = _Paths.REFUND_SUCCESS;
  static const REFUND_DETAIL = _Paths.REFUND_DETAIL;
  static const INVOICE = _Paths.INVOICE;
  static const COUPON = _Paths.COUPON;
  static const MEMBER_VOUCHER_LIST = _Paths.MEMBER_VOUCHER_LIST;
  static const PRODUCT = _Paths.PRODUCT;
  static const SHOW_PRODUCT = _Paths.SHOW_PRODUCT;
  static const CONTENT = _Paths.CONTENT;
  static const CATEGORIES = _Paths.CATEGORIES;
  static const PROMOTION = _Paths.PROMOTION;
  static const EF_GRID = _Paths.EF_GRID;
  static const CATEGORIES_PAGE = _Paths.CATEGORIES_PAGE;
  static const EF_WEB = _Paths.EF_WEB;
  static const COMMENT = _Paths.COMMENT;
  static const COLOR_PICKER = _Paths.COLOR_PICKER;
  static const MY_BONUS = _Paths.MY_BONUS;
  static const CATEGORY_TOP = _Paths.CATEGORY_TOP;
  static const SEARCH = _Paths.SEARCH;
  static const ORDER_SUCCESS = _Paths.ORDER_SUCCESS;
  static const ORDER_FAIL = _Paths.ORDER_FAIL;
  static const SALE = _Paths.SALE;
  static const CART_WEB = _Paths.CART_WEB;
  static const CHAT = _Paths.CHAT;
  static const DEVTOOL = _Paths.DEVTOOL;
  static const SIGN_UP_MID = _Paths.SIGN_UP_MID;
  static const SIGN_UP_UID = _Paths.SIGN_UP_UID;
  static const EF_ANDROID_WEB = _Paths.EF_ANDROID_WEB;
  static const CART_FINISH = _Paths.CART_FINISH;
  static const APP_CART_FINISH = _Paths.APP_CART_FINISH;
  static const EF_IOS_WEB = _Paths.EF_IOS_WEB;
  static const EMPTY = _Paths.EMPTY;
  static const SHOW_EMPTY_CART = _Paths.SHOW_EMPTY_CART;
  static const LOADING = _Paths.LOADING;
  static const NOT_FOUND = _Paths.NOT_FOUND;
}

abstract class _Paths {
  _Paths._();
  static const INDEX = '/index';
  static const CATEGORY = '/category';
  static const CART = '/cart';
  static const PROFILE = '/member_center';
  static const SIGN_IN = '/sign-in';
  static const SIGN_UP = '/sign-up';
  static const PASSWORD_INPUT = '/password-input';
  static const SPLASH = '/splash';
  static const HOME = '/home';
  static const EF_MENU = '/ef-menu';
  static const MESSAGE = '/message';
  static const SERVICE = '/service';
  static const FAVORITE = '/my_favorite';
  static const ARRIVAL = '/arrival';
  static const ADDRESS = '/address';
  static const ADDRESS_EDITOR = '/address-editor';
  static const ADDRESS_PICKER = '/address-picker';
  static const PROFILE_EDITOR = '/profile-editor';
  static const PASSWORD_EDITOR = '/password-editor';
  static const SETTINGS = '/settings';
  static const ORDERS = '/orders';
  static const ORDER_DETAIL = '/order-detail';
  static const SHIPPING_DETAIL = '/shipping-detail';
  static const CUSTOMER_SERVICE = '/customer-service';
  static const SERVICE_DETAIL = '/service-detail';
  static const MESSAGE_DETAIL = '/message-detail';
  static const LEAVE_MESSAGE = '/leave-message';
  static const MESSAGE_HISTORY = '/message-history';
  static const LEAVE_COMMENT = '/leave-comment';
  static const ACTIVITY = '/activity';
  static const NOTIFICATION = '/notification';
  static const LOGISTIC = '/logistic';
  static const FAQ = '/faq';
  static const FAQ_DETAIL = '/faq-detail';
  static const FAQ_CHILDREN = '/faq-children';
  static const DELETE_ACCOUNT = '/delete-account';
  static const SEARCH_SUGGESTION = '/search-suggestion';
  static const SEARCH_RESULT = '/search-result';
  static const REFUND = '/refund';
  static const BANK = '/bank';
  static const BANK_MANAGER = '/bank-manager';
  static const BANK_EDITOR = '/bank-editor';
  static const REFUND_SUCCESS = '/refund-success';
  static const REFUND_DETAIL = '/refund-detail';
  static const INVOICE = '/invoice';
  static const COUPON = '/member_push_voucher';
  static const MEMBER_VOUCHER_LIST = '/member_voucher_list';
  static const PRODUCT = '/product';
  static const SHOW_PRODUCT = '/showProduct';
  static const CONTENT = '/content';
  static const CATEGORIES = '/categories';
  static const PROMOTION = '/promotion';
  static const EF_GRID = '/ef-grid';
  static const CATEGORIES_PAGE = '/categories-page';
  static const EF_WEB = '/web_view';
  static const COMMENT = '/comment';
  static const COLOR_PICKER = '/color-picker';
  static const MY_BONUS = '/my_bonus';
  static const CATEGORY_TOP = '/category_top';
  static const SEARCH = '/search';
  static const ORDER_SUCCESS = '/order-success';
  static const ORDER_FAIL = '/order-fail';
  static const SALE = '/sale';
  static const CART_WEB = '/cart-web';
  static const CHAT = '/chat';
  static const DEVTOOL = '/devtool';
  static const SIGN_UP_MID = '/sign-up-mid';
  static const SIGN_UP_UID = '/sign-up-uid';
  static const EF_ANDROID_WEB = '/ef-android-web';
  static const CART_FINISH = '/CartFinish';
  static const APP_CART_FINISH = '/app/CartFinish';
  static const EF_IOS_WEB = '/ef-ios-web';
  static const EMPTY = '/empty';
  static const SHOW_EMPTY_CART = '/showEmptyCart';
  static const LOADING = '/loading';
  static const NOT_FOUND = '/not-found';
}
