import 'dart:developer';

import 'package:efshop/app/models/url.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/firebase_options.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

class NotificationProvider {
  /// 初始化套件
  final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  /// talker
  final Talker talker;

  /// 第幾則通知
  var id = 0;

  NotificationProvider({
    required this.talker,
  });

  // 初始化，執行一次即可
  Future<void> initialize() async {
    // 初始化在Android上的通知設定
    const initializationSettingsAndroid =
        AndroidInitializationSettings('ic_notification');

    // 初始化在iOS上的通知設定
    final initializationSettingsDarwin = DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
      onDidReceiveLocalNotification:
          (int id, String? title, String? body, String? payload) async {
        // 收到通知要做的事
        talker.log(
            'notification($id) title: $title, body: $body, payload: $payload');
      },
    );

    // 設定組合
    final initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsDarwin,
    );

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse:
          (NotificationResponse notificationResponse) async {
        // 收到通知要做的事
        // 點擊通知後的動作
        // notification(0) action tapped: null with payload: {"partner_id":"home","action":"category","id":"457","type":"activities"}
        talker.log('notification(${notificationResponse.id}) action tapped: '
            '${notificationResponse.actionId} with'
            ' payload: ${notificationResponse.payload}');
        // final detail = await flutterLocalNotificationsPlugin
        //     .getNotificationAppLaunchDetails();
        // detail.notificationResponse is equal to notificationResponse
        // log('detail: $detail');
        await _onSelectNotification(notificationResponse.payload);
      },
      // 這種情況通常是指，當應用程式在背景運行，
      // 用戶點擊了一個通知或通知中的動作，但這個動作並不需要打開應用程式或者改變用戶介面。
      // 傳遞給這個回調函數的函數需要使用 @pragma('vm:entry-point') 注解，
      // 以確保它們不會被 Dart 編譯器刪除。
      //
      // 直接在通知上處理的動作。如 Android 上的直接回覆，或是 iOS 上的直接回覆
      // 在 efshop 不會用到，所以這邊不做處理
      onDidReceiveBackgroundNotificationResponse: notificationTapBackground,
    );
  }

  // Future<void> cancelAll() async {
  //   await flutterLocalNotificationsPlugin.cancelAll();
  // }

  ///
  /// 點擊通知後執行
  ///
  Future<void> _onSelectNotification(String? payload) async {
    // TODO: for testing
    // Exception: _onSelectNotification: {"partner_id":"home","action":"category","id":"457","type":"activities"}
    // FirebaseCrashlytics.instance.recordFlutterError(
    //   FlutterErrorDetails(
    //     exception: Exception("_onSelectNotification: $payload"),
    //   ),
    //   fatal: true,
    // );
    if (payload != null && payload.isNotEmpty) {
      // notification payload: {"partner_id":"","action":"category","id":"457","type":"activities"}
      talker.info('notification payload: $payload');
      try {
        final url = Url.fromRawJson(payload); // 檢查是否為 Url 格式 (這邊是自定義的 Url 類別
        final uri = url.uri;
        if (Get.canLaunchUrl(uri)) {
          Get.popAll();
          await Get.launchUrl(uri);
        }
      } on Exception catch (e) {
        talker.error('notification payload error: $e');
      }
    }
  }

  ///
  /// 跳出通知
  /// usage: await NotificationProvider().showNotification();
  ///
  Future<void> showNotification({
    String title = '標題',
    String body = '內容',
    String payload = '要帶回程式的資料(如果有做點按後回到程式的功能)',
  }) async {
    const androidNotificationDetails = AndroidNotificationDetails(
      '你的通道id',
      '你的通道名稱',
      channelDescription: '都可以啦…讓使用者看這是什麼功能的通知',
      importance: Importance.max,
      priority: Priority.high,
      ticker: 'ticker',
    );
    const notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
    );
    await flutterLocalNotificationsPlugin.show(
      id++,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }
}

///
/// 注意，這個方法不能是class內的方法，要是靜態，或宣告在外層的方法
///
@pragma('vm:entry-point')
Future<void> notificationTapBackground(
    NotificationResponse notificationResponse) async {
  try {
    if (Firebase.apps.isEmpty) {
      const sandbox = false;
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.getCurrentPlatform(sandbox),
      );
    }
    // ignore: avoid_print
    FirebaseCrashlytics.instance
        .log('ntb notification(${notificationResponse.id}) action tapped: '
            '${notificationResponse.actionId} with'
            ' payload: ${notificationResponse.payload}');
    if (notificationResponse.input?.isNotEmpty ?? false) {
      // 確認有帶值進來時，程式要做的動作
      // ignore: avoid_print
      FirebaseCrashlytics.instance.log(
          'notification action tapped with input: ${notificationResponse.input}');
    }
  } catch (e, s) {
    FirebaseCrashlytics.instance.recordError(e.toString(), s, fatal: true);
  }
}
