import 'package:get_storage/get_storage.dart';
import 'package:hive/hive.dart' as h;
import 'package:objectbox/objectbox.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:path/path.dart' as p;

class BoxProvider {
  var _namespace = '';
  set namespace(String value) => _namespace = value;
  late Store _store; // store instance
  Store get store => _store;
  var _domain = '';
  set domain(String value) => _domain = value;

  final h.Box defaultBox;

  BoxProvider(this.defaultBox);

  // Future<void> init() async {
  //   if (_store.isClosed()) {
  //     _store = await _createStore();
  //   }
  // }

  // void close() {
  //   _store.close();
  // }

  // Future<Store> _createStore() async {
  //   final docsDir = await getApplicationDocumentsDirectory();
  //   final path = p.join(docsDir.path, _domain);
  //   // logger.d('[BoxProvider] path: ' + path);
  //   if (Store.isOpen(path)) {
  //     // applicable when store is from other isolate
  //     return Store.attach(getObjectBoxModel(), path);
  //   }
  //   return openStore(directory: path);
  // }

  Future<h.Box<E>> getHiveBox<E>(String name) async {
    final fullName = _getFullName(name);
    if (h.Hive.isBoxOpen(fullName)) {
      return h.Hive.box<E>(fullName);
    }
    // logger.d('[BoxProvider] getHiveBox($fullName)');
    return h.Hive.openBox(fullName);
  }

  Future<h.LazyBox<E>> getHiveLazyBox<E>(String name) async {
    final fullName = _getFullName(name);
    // logger.d('[BoxProvider] getHiveLazyBox($fullName)');
    if (h.Hive.isBoxOpen(fullName)) {
      return h.Hive.lazyBox<E>(fullName);
    }
    return h.Hive.openLazyBox<E>(fullName);
  }

  // Future<GetStorage> getGsBox(String name) async {
  //   final fullName = '$name.$_domain';
  //   await GetStorage(fullName).initStorage;
  //   // logger.d('[BoxProvider] getGsBox($fullName)');
  //   return GetStorage(fullName);
  // }

  Future<bool> initGsBox(String name) {
    final fullName = _getFullName(name);
    return GetStorage.init(fullName);
  }

  GetStorage getGsBox(String name, {bool withNamespace = true}) {
    final fullName = _getFullName(name, withNamespace);
    return GetStorage(fullName);
  }

  String _getFullName(String name, [bool withNamespace = true]) {
    Iterable<String> children([bool withNamespace = true]) sync* {
      yield name;
      if (withNamespace && _namespace.isNotEmpty) {
        yield _namespace;
      }
      yield _domain;
    }

    return children(withNamespace).join('.');
  }
}
