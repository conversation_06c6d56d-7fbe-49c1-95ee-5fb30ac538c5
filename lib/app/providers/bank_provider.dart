import 'package:efshop/enums.dart';
import 'package:get/get.dart';

import '../models/members_refund_post_req.dart';
import '../models/members_refund_res.dart';
import '../models/message_res.dart';
import 'box_provider.dart';
import 'wabow_provider.dart';

class BankProvider {
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  final _defaultBankId = ''.obs;
  String get defaultBankId => _defaultBankId.value;
  Stream<String> get defaultBankIdStream => _defaultBankId.stream;

  BankProvider({
    required this.wabowProvider,
  });

  // TODO: use iterable map
  Iterable<MembersRefundRes> getBanksFromLocalStorage() sync* {
    final box = boxProvider.getGsBox(Boxes.bank.name);
    for (dynamic item in box.getValues()) {
      yield MembersRefundRes.fromJson(item);
    }
  }

  Future<Iterable<MembersRefundRes>> getBanks() async {
    final res = await wabowProvider.getMembersRefund();
    // save to box
    final box = boxProvider.getGsBox(Boxes.bank.name);
    box.erase();
    for (final item in res) {
      box.write(item.id ?? '', item.toJson());
      if (item.isDefault == true) {
        _defaultBankId.value = item.id ?? '';
      }
    }
    return res;
  }

  Future<MessageRes> create(MembersRefundPostReq data) async {
    // 如果是空銀行列表，設定預設銀行
    final banks = getBanksFromLocalStorage();
    if (banks.isEmpty) {
      data.isDefault = true;
    }
    final res = await wabowProvider.postMembersRefund(data);
    if (res.status == true) {
      await getBanks();
    }
    return res;
  }

  Future<MessageRes> update(String id, MembersRefundPostReq data) async {
    // 變更預設銀行
    if (data.isDefault == true) {
      _defaultBankId.value = id;
    }
    final res = await wabowProvider.patchMembersRefund(id, data);
    if (res.status == true) {
      await getBanks();
    }
    return res;
  }

  Future<MessageRes> delete(String id) async {
    // 如果刪除的是預設銀行，清空預設銀行
    if (id == defaultBankId) {
      _defaultBankId.value = '';
    }
    final res = await wabowProvider.deleteMembersRefund(id);
    if (res.status == true) {
      await getBanks();
    }
    return res;
  }

  MembersRefundRes getBank(String id) {
    final box = boxProvider.getGsBox(Boxes.bank.name);
    final jsonObject = box.read(id);
    if (jsonObject != null) {
      return MembersRefundRes.fromJson(jsonObject);
    }
    return MembersRefundRes();
  }
}
