import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/basic.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/foundation.dart';

import '../models/city.dart';
import '../models/members_address_res.dart';
import '../models/members_addresses_post_req.dart';
import '../models/store_detail.dart';

class AddressProvider {
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;

  AddressProvider({
    required this.wabowProvider,
  });

  Map<String, String> getCityMap() =>
      Map<String, String>.fromEntries(getCities());

  // TODO: use iterable map
  Iterable<MapEntry<String, String>> getCities() sync* {
    final box = boxProvider.getGsBox(Boxes.city.name);
    for (final key in box.getKeys()) {
      final value = box.read('$key');
      yield MapEntry('$key', '$value');
    }
  }

  City getCity(String cityName) {
    final box = boxProvider.getGsBox(Boxes.town.name);
    if (box.hasData(cityName)) {
      final json = box.read(cityName);
      return City.fromJson(json);
    }
    return City();
  }

  Iterable<Store> getTowns(String cityName) {
    return getCity(cityName).data ?? [];
  }

  ///
  /// 取得縣市鄉鎮市區
  ///
  MapEntry getCityTown(String zipcode) {
    final box = boxProvider.getGsBox(Boxes.town.name);
    for (final key in box.getKeys()) {
      final json = box.read('$key');
      final city = City.fromJson(json);
      for (final town in city.data ?? <Store>[]) {
        if (town.code == zipcode) {
          return MapEntry(city.name, town.name);
        }
      }
    }
    return const MapEntry('', '');
  }

  ///
  /// 取得郵遞區號
  ///
  String getZipCode(String cityName, String townName) {
    return getCity(cityName).getZipCode(townName);
  }

  ///
  /// 取得門市
  ///
  StoreDetail getStoreDetail(String roadName, Predicate<StoreDetail> tester) {
    final box = boxProvider.getGsBox(Boxes.road.name);
    if (box.hasData(roadName)) {
      final it = box.read(roadName);
      return Iterable.castFrom(it)
          .map((e) => StoreDetail.fromJson(e))
          .firstWhere(tester, orElse: () => StoreDetail());
    }
    return StoreDetail();
  }

  ///
  /// 取得遠端地址列表
  ///
  Future<void> fetchAddress() async {
    final it = await wabowProvider.getMembersAddresses();
    final box = boxProvider.getGsBox(Boxes.address.name);
    box.erase();
    for (final item in it) {
      box.write(item.id ?? '0', item.toJson());
    }
  }

  ///
  /// 取得地址列表
  ///
  Iterable<MembersAddressRes> getAddressList(
      [Predicate<MembersAddressRes>? tester]) sync* {
    AsyncCallback;
    final box = boxProvider.getGsBox(Boxes.address.name);
    for (final item in box.getValues()) {
      final res = MembersAddressRes.fromJson(item as Map<String, dynamic>);
      if (tester == null) {
        yield res;
      } else {
        if (tester(res) == true) {
          yield res;
        }
      }
    }
  }

  ///
  /// 設定預設地址
  ///
  Future<MessageRes> makeDefaultAddress(
      AddressType type, MembersAddressRes data) {
    final draft = MembersAddressesPostReq(isDefault: true);
    return wabowProvider.patchMembersAddresses(type, data.id ?? '', draft);
  }

  ///
  /// 取得 7-11 的縣市
  ///
  Future<void> fetchSevenElevenCities() async {
    final map = await wabowProvider.getStoreSevenElevenCities();
    final box = boxProvider.getGsBox(Boxes.city.name);
    box.erase();
    for (final e in map.entries) {
      box.write(e.key, e.value);
    }
  }

  ///
  /// 取得 7-11 的鄉鎮市區
  ///
  Future<void> fetchSevenElevenTowns() async {
    final list = await wabowProvider.getStoreSevenElevenTowns();
    final box = boxProvider.getGsBox(Boxes.town.name);
    box.erase();
    for (final city in list) {
      box.write(city.name ?? '', city.toJson());
    }
  }

  ///
  /// 取得包含 7-11 的路名
  ///
  Future<void> fetchSevenElevenRoads(String zip) async {
    final map = await wabowProvider.getStoreSevenElevenWithZip(zip);
    final box = boxProvider.getGsBox(Boxes.road.name);
    box.erase();
    for (final entity in map.entries) {
      final storeList = Iterable.castFrom(entity.value)
          .map((e) => e.toJson())
          .toList(growable: false);
      box.write(entity.key, storeList);
    }
  }

  ///
  /// 取得包含全家的縣市
  ///
  Future<void> fetchFamilyMartCities() async {
    final map = await wabowProvider.getStoreFamilyMartCities();
    final box = boxProvider.getGsBox(Boxes.city.name);
    box.erase();
    for (final e in map.entries) {
      box.write(e.key, e.value);
    }
  }

  ///
  /// 取得包含全家的鄉鎮市區
  ///
  Future<void> fetchFamilyMartTowns() async {
    final list = await wabowProvider.getStoreFamilyMartTowns();
    final box = boxProvider.getGsBox(Boxes.town.name);
    box.erase();
    for (final city in list) {
      box.write(city.name ?? '', city.toJson());
    }
  }

  ///
  /// 取得包含全家的路名
  ///
  Future<void> fetchFamilyMartRoads(String zip) async {
    final map = await wabowProvider.getStoreFamilyMartWithZip(zip);
    final box = boxProvider.getGsBox(Boxes.road.name);
    box.erase();
    for (final entity in map.entries) {
      final storeList = Iterable.castFrom(entity.value)
          .map((e) => e.toJson())
          .toList(growable: false);
      box.write(entity.key, storeList);
    }
  }
}
