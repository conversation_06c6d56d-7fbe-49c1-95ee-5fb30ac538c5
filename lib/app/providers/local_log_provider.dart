import 'dart:convert';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:uuid/uuid.dart';

import '../../enums.dart';
import '../models/log_entry.dart';
import 'box_provider.dart';

/// 本地日誌提供者
/// 負責將日誌記錄到本地存儲，並提供查詢和管理功能
class LocalLogProvider {
  final BoxProvider boxProvider;
  final Logger logger;
  final Talker talker;
  final Uuid _uuid = const Uuid();

  // 日誌存儲的最大條數
  static const int maxLogEntries = 1000;
  
  // 日誌清理的批次大小
  static const int cleanupBatchSize = 100;

  LocalLogProvider({
    required this.boxProvider,
    required this.logger,
    required this.talker,
  });

  /// 獲取日誌存儲盒子
  GetStorage get _logBox => boxProvider.getGsBox(Boxes.log.name);

  /// 記錄日誌到本地
  Future<void> logToLocal({
    required String level,
    required String message,
    String? stackTrace,
    Map<String, dynamic>? extra,
  }) async {
    try {
      final logEntry = LogEntry(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        level: level,
        message: message,
        stackTrace: stackTrace,
        extra: extra,
      );

      await _saveLogEntry(logEntry);
      await _cleanupOldLogs();
    } catch (e, stackTrace) {
      // 避免在日誌記錄過程中產生無限循環
      talker.error('Failed to log to local storage: $e', stackTrace);
    }
  }

  /// 保存日誌條目
  Future<void> _saveLogEntry(LogEntry logEntry) async {
    final key = '${logEntry.timestamp.millisecondsSinceEpoch}_${logEntry.id}';
    _logBox.write(key, logEntry.toRawJson());
  }

  /// 獲取所有本地日誌
  List<LogEntry> getAllLogs() {
    try {
      final keys = _logBox.getKeys().cast<String>();
      final logs = <LogEntry>[];

      for (final key in keys) {
        try {
          final jsonString = _logBox.read(key);
          if (jsonString != null) {
            final logEntry = LogEntry.fromRawJson(jsonString);
            logs.add(logEntry);
          }
        } catch (e) {
          // 跳過無效的日誌條目
          talker.warning('Invalid log entry for key: $key, error: $e');
        }
      }

      // 按時間戳排序（最新的在前）
      logs.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return logs;
    } catch (e, stackTrace) {
      talker.error('Failed to get all logs: $e', stackTrace);
      return [];
    }
  }

  /// 獲取未同步的日誌
  List<LogEntry> getUnsyncedLogs() {
    return getAllLogs().where((log) => !log.synced).toList();
  }

  /// 獲取指定時間範圍內的日誌
  List<LogEntry> getLogsByDateRange(DateTime start, DateTime end) {
    return getAllLogs()
        .where((log) => 
            log.timestamp.isAfter(start) && log.timestamp.isBefore(end))
        .toList();
  }

  /// 獲取指定級別的日誌
  List<LogEntry> getLogsByLevel(String level) {
    return getAllLogs().where((log) => log.level == level).toList();
  }

  /// 標記日誌為已同步
  Future<void> markLogAsSynced(String logId) async {
    try {
      final logs = getAllLogs();
      final logIndex = logs.indexWhere((log) => log.id == logId);
      
      if (logIndex != -1) {
        final log = logs[logIndex];
        final updatedLog = log.copyWith(synced: true);
        
        // 找到對應的存儲鍵
        final keys = _logBox.getKeys().cast<String>();
        for (final key in keys) {
          final jsonString = _logBox.read(key);
          if (jsonString != null) {
            final existingLog = LogEntry.fromRawJson(jsonString);
            if (existingLog.id == logId) {
              _logBox.write(key, updatedLog.toRawJson());
              break;
            }
          }
        }
      }
    } catch (e, stackTrace) {
      talker.error('Failed to mark log as synced: $e', stackTrace);
    }
  }

  /// 批量標記日誌為已同步
  Future<void> markLogsAsSynced(List<String> logIds) async {
    for (final logId in logIds) {
      await markLogAsSynced(logId);
    }
  }

  /// 清理舊日誌
  Future<void> _cleanupOldLogs() async {
    try {
      final keys = _logBox.getKeys().cast<String>();
      
      if (keys.length > maxLogEntries) {
        // 按鍵名排序（包含時間戳）
        final sortedKeys = keys.toList()..sort();
        
        // 刪除最舊的日誌
        final keysToDelete = sortedKeys.take(keys.length - maxLogEntries + cleanupBatchSize);
        for (final key in keysToDelete) {
          _logBox.remove(key);
        }
        
        talker.info('Cleaned up ${keysToDelete.length} old log entries');
      }
    } catch (e, stackTrace) {
      talker.error('Failed to cleanup old logs: $e', stackTrace);
    }
  }

  /// 清理已同步的日誌
  Future<void> cleanupSyncedLogs({int? olderThanDays}) async {
    try {
      final cutoffDate = olderThanDays != null 
          ? DateTime.now().subtract(Duration(days: olderThanDays))
          : null;
      
      final keys = _logBox.getKeys().cast<String>();
      final keysToDelete = <String>[];

      for (final key in keys) {
        try {
          final jsonString = _logBox.read(key);
          if (jsonString != null) {
            final logEntry = LogEntry.fromRawJson(jsonString);
            
            // 刪除已同步且符合時間條件的日誌
            if (logEntry.synced && 
                (cutoffDate == null || logEntry.timestamp.isBefore(cutoffDate))) {
              keysToDelete.add(key);
            }
          }
        } catch (e) {
          // 刪除無效的日誌條目
          keysToDelete.add(key);
        }
      }

      for (final key in keysToDelete) {
        _logBox.remove(key);
      }

      if (keysToDelete.isNotEmpty) {
        talker.info('Cleaned up ${keysToDelete.length} synced log entries');
      }
    } catch (e, stackTrace) {
      talker.error('Failed to cleanup synced logs: $e', stackTrace);
    }
  }

  /// 獲取日誌統計信息
  Map<String, dynamic> getLogStats() {
    try {
      final logs = getAllLogs();
      final unsyncedLogs = logs.where((log) => !log.synced).toList();
      
      final levelCounts = <String, int>{};
      for (final log in logs) {
        levelCounts[log.level] = (levelCounts[log.level] ?? 0) + 1;
      }

      return {
        'total': logs.length,
        'unsynced': unsyncedLogs.length,
        'synced': logs.length - unsyncedLogs.length,
        'levelCounts': levelCounts,
        'oldestLog': logs.isNotEmpty ? logs.last.timestamp.toIso8601String() : null,
        'newestLog': logs.isNotEmpty ? logs.first.timestamp.toIso8601String() : null,
      };
    } catch (e, stackTrace) {
      talker.error('Failed to get log stats: $e', stackTrace);
      return {
        'total': 0,
        'unsynced': 0,
        'synced': 0,
        'levelCounts': <String, int>{},
        'oldestLog': null,
        'newestLog': null,
      };
    }
  }

  /// 清空所有日誌
  Future<void> clearAllLogs() async {
    try {
      final keys = _logBox.getKeys().cast<String>();
      for (final key in keys) {
        _logBox.remove(key);
      }
      talker.info('Cleared all local logs');
    } catch (e, stackTrace) {
      talker.error('Failed to clear all logs: $e', stackTrace);
    }
  }
}
