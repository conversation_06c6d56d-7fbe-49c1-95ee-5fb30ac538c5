import 'dart:convert';
import 'dart:developer';

import 'package:flutter/services.dart';

class LoveCodeProvider {
  final dict = <String, String>{};

  static Future<LoveCodeProvider> init() async {
    final lcp = LoveCodeProvider();
    try {
      final csvString = await rootBundle.loadString('docs/xca.csv');
      // read line
      final lines = LineSplitter.split(csvString).toList();
      // grab love code
      for (var line in lines) {
        final elements = line.split(',');
        final key = elements.elementAt(2);
        if (num.tryParse(key) != null) {
          final val = elements.elementAt(3);
          lcp.dict[key] = val;
        }
      }
    } catch (e) {
      log('error', error: e);
    }
    return lcp;
  }
}
