import 'package:device_info_plus/device_info_plus.dart';
import 'package:efshop/app/models/login_res.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../../enums.dart';
import '../models/cart_items_res.dart';
import '../models/configs_res.dart';
import '../models/members_messages_unread_res.dart';
import '../models/members_profile_res.dart';
import '../models/product_detail.dart';
import 'box_provider.dart';

class PrefProvider {
  static const _sandboxHost = 'api.efshop.tt.wabow.com';
  static const _productionHost = 'api.efshop.com.tw';

  final Logger logger;
  final DeviceInfoPlugin deviceInfo;
  final PackageInfo packageInfo;
  final BoxProvider boxProvider;
  final Talker talker;

  PrefProvider({
    required this.logger,
    required this.deviceInfo,
    required this.packageInfo,
    required this.boxProvider,
    required this.talker,
  });

  Future<void> printInfo() async {
    if (GetPlatform.isAndroid) {
      await _printAndroidInfo();
    } else if (GetPlatform.isIOS) {
      await _printIosInfo();
    } else if (GetPlatform.isWeb) {
      await _printWebInfo();
    }
    // appName: Efshop
    logger.d('appName: ${packageInfo.appName}');
    // packageName: com.chendermei.efshop
    logger.d('packageName: ${packageInfo.packageName}');
    // version: 1.0.0
    logger.d('version: ${packageInfo.version}');
    // buildNumber: 1
    logger.d('buildNumber: ${packageInfo.buildNumber}');
  }

  Future<void> _printAndroidInfo() async {
    final androidInfo = await deviceInfo.androidInfo;
    logger.d('Running on $androidInfo'); // e.g. "Moto G (4)"
  }

  // {data: {systemName: iOS, isPhysicalDevice: false, utsname: {release: 22.3.0, sysname: Darwin, nodename: phtdeMBP, machine: iPhone14,6, version: Darwin Kernel Version 22.3.0: Mon Jan 30 20:38:37 PST 2023; root:xnu-8792.81.3~2/RELEASE_ARM64_T6000}, model: iPhone, localizedModel: iPhone, systemVersion: 16.2, name: iPhone SE (3rd generation), identifierForVendor: F906D874-7844-42FA-BE28-E956644A01BA}}
  Future<void> _printIosInfo() async {
    final iosInfo = await deviceInfo.iosInfo;
    logger.d('Running on $iosInfo'); // e.g. "iPod7,1"
  }

  Future<void> _printWebInfo() async {
    final webBrowserInfo = await deviceInfo.webBrowserInfo;
    logger.d(
        'Running on $webBrowserInfo'); // e.g. "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:61.0) Gecko/20100101 Firefox/61.0"
  }

  bool get isProduction {
    final packageName = packageInfo.packageName;
    if (GetPlatform.isAndroid) {
      return Constants.androidBundleId == packageName;
    }
    if (GetPlatform.isIOS) {
      return Constants.iosBundleId == packageName;
    }
    return false;
  }

  bool get isSandBox => !isProduction;

  String get host => isSandBox ? _sandboxHost : _productionHost;
  String get appierKey =>
      isSandBox ? Constants.appierSandbox : Constants.appierProduction;

  // Future<void> removeLoginRes() {
  //   return boxProvider.defaultBox.delete(Keys.loginRes);
  // }

  // Future<void> setLoginRes(LoginRes value) {
  //   return boxProvider.defaultBox.put(Keys.loginRes, value.toRawJson());
  // }

  set fcmToken(String value) {
    boxProvider.defaultBox.put(Keys.fcmToken, value);
  }

  String get fcmToken {
    return boxProvider.defaultBox.get(Keys.fcmToken) ?? '';
  }

  // set and get apns token
  set apnsToken(String value) {
    boxProvider.defaultBox.put(Keys.apnsToken, value);
  }

  String get apnsToken {
    return boxProvider.defaultBox.get(Keys.apnsToken) ?? '';
  }

  String get deviceId => GetPlatform.isIOS ? apnsToken : fcmToken;

  set appierAuthCode(String value) {
    boxProvider.defaultBox.put(Keys.appierAuthCode, value);
  }

  String get appierAuthCode {
    return boxProvider.defaultBox.get(Keys.appierAuthCode) ?? '';
  }

  set configs(ConfigsRes value) {
    boxProvider.defaultBox.put(Keys.configs, value.toRawJson());
  }

  ConfigsRes get configs {
    if (boxProvider.defaultBox.containsKey(Keys.configs)) {
      final jsonString = boxProvider.defaultBox.get(Keys.configs) ?? '{}';
      if (jsonString != null) {
        return ConfigsRes.fromRawJson(jsonString);
      }
    }
    return ConfigsRes();
  }

  set profile(MembersProfileRes value) {
    final box = boxProvider.getGsBox(Boxes.setting.name);
    box.write(Keys.profile, value.toJson());
  }

  MembersProfileRes get profile {
    final box = boxProvider.getGsBox(Boxes.setting.name);
    if (box.hasData(Keys.profile)) {
      final json = box.read(Keys.profile);
      return MembersProfileRes.fromJson(json);
    }
    return MembersProfileRes();
  }

  // 最後版本號
  String get appVersionRequires => configs.appVersionRequires;

  // 是否有新版本
  bool get hasNewerVersion {
    return configs.hasNewerVersion(packageInfo.version);
  }

  set loginRes(LoginRes? value) {
    if (value != null) {
      boxProvider.defaultBox.put(Keys.loginRes, value.toRawJson());
    } else {
      // boxProvider.defaultBox.delete(Keys.loginRes);
      boxProvider.defaultBox.put(Keys.loginRes, '{}');
    }
  }

  LoginRes? get loginRes {
    if (boxProvider.defaultBox.containsKey(Keys.loginRes)) {
      final jsonString = boxProvider.defaultBox.get(Keys.loginRes) ?? '{}';
      if (jsonString != null) {
        return LoginRes.fromRawJson(jsonString);
      }
    }
    return null;
  }

  bool get isLogin => !isNotLogin;

  bool get isNotLogin {
    final res = loginRes;
    return res == null || res.isExpired;
  }

  String get token {
    if (loginRes != null) {
      return loginRes!.token ?? '';
    }
    return '';
  }

  Stream<String> get tokenStream {
    return boxProvider.defaultBox
        .watch(key: Keys.loginRes)
        .map((event) => event.value)
        .map((jsonString) {
      if (jsonString != null) {
        try {
          final loginRes = LoginRes.fromRawJson(jsonString);
          return loginRes.token ?? '';
        } catch (e) {
          logger.e(e.toString());
        }
      }
      return '';
    });
  }

  // cart items
  final _cartItems = CartItemsRes().obs;
  CartItemsRes get cartItems => _cartItems.value;
  set cartItems(CartItemsRes value) => _cartItems.value = value;
  void refreshCartItems() => _cartItems.refresh();
  bool containsCartItems(String number) {
    if (number.isEmpty) {
      return false;
    }
    final items = cartItems.items ?? [];
    if (isLogin) {
      return items.any((element) => number == element.productNumber);
    }
    // local storage
    try {
      final box = boxProvider.getGsBox(Boxes.cart.name, withNamespace: false);
      final keys = box.getKeys<Iterable>();
      final products = boxProvider.getGsBox(Boxes.productDetail.name);
      for (final key in keys) {
        try {
          final json = products.read(key);
          // 檢查 json 是否為 null 或空
          if (json == null) {
            talker.warning('Product detail not found for key: $key');
            continue;
          }
          
          // 確保 json 是正確的 Map 類型
          Map<String, dynamic> jsonMap;
          if (json is Map<String, dynamic>) {
            jsonMap = json;
          } else if (json is Map) {
            jsonMap = Map<String, dynamic>.from(json);
          } else {
            talker.warning('Invalid json format for key: $key, type: ${json.runtimeType}');
            continue;
          }
          
          final product = ProductDetail.fromJson(jsonMap);
          if (number == product.number) {
            return true;
          }
        } catch (e, s) {
          // 記錄個別產品解析錯誤，但不中斷整個流程
          talker.error('Error parsing product for key: $key', e, s);
          continue;
        }
      }
    } catch (e, s) {
      // 記錄整體錯誤
      talker.error('Error in containsCartItems for number: $number', e, s);
    }
    return false;
  }

  num itemCountWithPromotionId(String id) {
    final items = cartItems.items ?? [];
    if (isLogin) {
      return items.fold<num>(0, (previousValue, element) {
        if ('${element.promotionId}' == id) {
          return previousValue + (element.cartQuantity ?? 0);
        }
        return previousValue;
      });
    }
    // local storage
    num count = 0;
    try {
      final products = boxProvider.getGsBox(Boxes.productDetail.name);
      final box = boxProvider.getGsBox(Boxes.cart.name, withNamespace: false);
      final map = Map.fromIterables(box.getKeys(), box.getValues());
      for (final entry in map.entries) {
        try {
          final json = products.read('${entry.key}');
          // 檢查 json 是否為 null 或空
          if (json == null) {
            talker.warning('Product detail not found for key: ${entry.key}');
            continue;
          }
          
          // 確保 json 是正確的 Map 類型
          Map<String, dynamic> jsonMap;
          if (json is Map<String, dynamic>) {
            jsonMap = json;
          } else if (json is Map) {
            jsonMap = Map<String, dynamic>.from(json);
          } else {
            talker.warning('Invalid json format for key: ${entry.key}, type: ${json.runtimeType}');
            continue;
          }
          
          final product = ProductDetail.fromJson(jsonMap);
          if ('${product.promotionId}' == id) {
            final quantity = entry.value as num;
            count += quantity;
          }
        } catch (e, s) {
          // 記錄個別產品解析錯誤，但不中斷整個流程
          talker.error('Error parsing product for key: ${entry.key}', e, s);
          continue;
        }
      }
    } catch (e, s) {
      // 記錄整體錯誤
      talker.error('Error in itemCountWithPromotionId for id: $id', e, s);
    }
    return count;
  }

  final _cartQuantity = 0.obs;
  int get cartQuantity => _cartQuantity.value;
  set cartQuantity(int value) => _cartQuantity.value = value;
  Stream<num> get cartQuantityStream => _cartQuantity.stream;

  final _unreadRes = MembersMessagesUnreadRes().obs;
  MembersMessagesUnreadRes get unreadRes => _unreadRes.value;
  set unreadRes(MembersMessagesUnreadRes value) => _unreadRes.value = value;

  final _devtool = false.obs;
  bool get devtool => _devtool.value;
  // set devtool(bool value) => _devtool.value = value;
  DateTime? _lastClickTime;
  int _clickCount = 0;

  ///
  /// 5秒內點擊5次，顯示開發者選單
  ///
  void showDeveloperMenu() {
    final now = DateTime.now();
    if (_lastClickTime == null) {
      _lastClickTime = now;
      return;
    }
    final diff = now.difference(_lastClickTime!);
    if (diff.inSeconds < 5) {
      _clickCount++;
      if (_clickCount >= 5) {
        _clickCount = 0;
        _lastClickTime = null;
        _devtool.value = true;
      }
    } else {
      _clickCount = 0;
      _lastClickTime = now;
    }
  }

  ///
  /// avatar
  ///
  set avatar(String value) {
    final box = boxProvider.getGsBox(Boxes.setting.name);
    box.write(Keys.avatar, value);
  }

  String get avatar {
    final box = boxProvider.getGsBox(Boxes.setting.name);
    return box.read(Keys.avatar) ?? '';
  }

  // 訂單數量
  final tabs = <OrderStatus, num>{
    OrderStatus.padding: -1,
    OrderStatus.shipped: -1,
    OrderStatus.signed: -1,
    OrderStatus.refund: -1,
    OrderStatus.rated: -1,
    OrderStatus.all: -1,
  }.obs;

  Iterable<String> get cookies {
    final box = boxProvider.getGsBox(Boxes.setting.name, withNamespace: false);
    final ls = box.read(Keys.cookies);
    return ls is List ? List.from(ls) : [];
  }

  set cookies(Iterable<String> value) {
    final box = boxProvider.getGsBox(Boxes.setting.name, withNamespace: false);
    box.write(Keys.cookies, value.toList());
  }

  ///
  /// 歡迎訊息最後觸發日期
  ///
  set welcomeMessageLastTriggered(String value) {
    final box = boxProvider.getGsBox(Boxes.setting.name);
    box.write(Keys.welcomeMessageLastTriggered, value);
  }

  String get welcomeMessageLastTriggered {
    final box = boxProvider.getGsBox(Boxes.setting.name);
    return box.read(Keys.welcomeMessageLastTriggered) ?? '';
  }

  ///
  /// 檢查是否應該觸發歡迎訊息（每天只觸發一次）
  ///
  bool shouldTriggerWelcomeMessage() {
    final lastTriggered = welcomeMessageLastTriggered;
    final today = DateTime.now().toIso8601String().substring(0, 10); // YYYY-MM-DD
    
    return lastTriggered.isEmpty || lastTriggered != today;
  }

  ///
  /// 設定歡迎訊息已觸發
  ///
  void setWelcomeMessageTriggered() {
    final today = DateTime.now().toIso8601String().substring(0, 10); // YYYY-MM-DD
    welcomeMessageLastTriggered = today;
  }
}
