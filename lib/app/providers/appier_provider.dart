import 'package:appier_flutter/appier_flutter.dart';
import 'package:efshop/extension.dart';

import '../../constants.dart';
import '../../keys.dart';
import '../models/appier_auth_code_req.dart';
import '../models/appier_cart_viewed_res.dart';
import '../models/appier_category_viewed_req.dart';
import '../models/appier_checkout_completed_req.dart';
import '../models/appier_payment_info_added_req.dart';
import '../models/appier_product_purchased_req.dart';
import '../models/appier_product_removed_from_cart_res.dart';
import '../models/appier_product_viewed_req.dart';
import '../models/appier_registration_completed_req.dart';
import '../models/appier_searched_req.dart';
import '../models/appier_type_viewed_req.dart';
import '../models/appier_user_login_req.dart';
import '../models/product_added_to_cart_req.dart';
import 'api_provider.dart';

class AppierProvider {
  final ApiProvider apiProvider;

  AppierProvider({required this.apiProvider});

  ///
  /// 完成註冊
  ///
  Future<void> registrationCompleted(AppierRegistrationCompletedReq req) async {
    await AppierFlutter.logEvent(
      'registration_completed',
      parameters: req.toJson(),
      // vtsCurr: 'NTD',
    );
  }

  ///
  /// 登入
  ///
  Future<void> userLogin(AppierUserLoginReq req) async {
    await AppierFlutter.logEvent(
      'user_login',
      parameters: req.toJson(),
      // parameters: <String, dynamic>{
      //   'param1': 2,
      //   'param2': null,
      //   'param3': 'appier',
      // },
      // vts: 100,
      // vtsCurr: 'NTD',
    );
  }

  ///
  /// 付款資訊
  ///
  Future<void> paymentInfoAdded(
      AppierPaymentInfoAddedReq req, num valueToSum) async {
    await AppierFlutter.logEvent(
      'payment_info_added',
      parameters: req.toJson(),
      vts: valueToSum.toDouble(),
      vtsCurr: 'NTD',
    );
  }

  ///
  /// 瀏覽大類別
  ///
  Future<void> categoryViewed(AppierCategoryViewedReq req) async {
    await AppierFlutter.logEvent(
      'category_viewed',
      parameters: req.toJson(),
    );
  }

  ///
  /// 瀏覽子類別
  ///
  Future<void> typeViewed(AppierTypeViewedReq req) async {
    await AppierFlutter.logEvent(
      'type_viewed',
      parameters: req.toJson(),
    );
  }

  ///
  /// 搜尋
  ///
  Future<void> searched(AppierSearchedReq req) async {
    await AppierFlutter.logEvent(
      'searched',
      parameters: req.toJson(),
    );
  }

  ///
  /// 瀏覽產品頁
  ///
  Future<void> productViewed(AppierProductViewedReq req) async {
    await AppierFlutter.logEvent(
      'product_viewed',
      parameters: req.toJson(),
      vts: (req.productPrice ?? 0).toDouble(),
      vtsCurr: 'NTD',
    );
  }

  ///
  /// 把產品加入購物車
  ///
  Future<void> productAddedToCart(ProductAddedToCartReq req) async {
    await AppierFlutter.logEvent(
      'product_added_to_cart',
      parameters: req.toJson(),
      vts: (req.productPrice ?? 0).toDouble(),
      vtsCurr: 'NTD',
    );
  }

  ///
  /// 完成產品購買
  ///
  Future<void> productPurchased(AppierProductPurchasedReq req) async {
    await AppierFlutter.logEvent(
      'product_purchased',
      parameters: req.toJson(),
      vts: (req.productPrice ?? 0).toDouble(),
      vtsCurr: 'NTD',
    );
  }

  ///
  /// 完成結帳
  ///
  Future<void> checkoutCompleted(AppierCheckoutCompletedReq req) async {
    await AppierFlutter.logEvent(
      'checkout_completed',
      parameters: req.toJson(),
      vts: (req.orderAmount ?? 0).toDouble(),
      vtsCurr: 'NTD',
    );
  }

  ///
  /// 產品移出購物車(無法偵測產品移出購物車)
  ///
  Future<void> productRemovedFromCart(
      AppierProductRemovedFromCartRes req) async {
    await AppierFlutter.logEvent(
      'product_removed_from_cart',
      parameters: req.toJson(),
    );
  }

  ///
  /// 檢查/瀏覽購物車
  ///
  Future<void> cartViewed(AppierCartViewedRes req) async {
    await AppierFlutter.logEvent(
      'cart_viewed',
      parameters: req.toJson(),
    );
  }

  ///
  /// 換 code
  ///
  Future<String> getAuthCode(AppierAuthCodeReq req) async {
    final data = req.toJson();
    data.removeNull();
    final httpClient = apiProvider.httpClient;
    httpClient.options.headers.remove('Authorization');
    httpClient.options.headers['x-api-token'] = Constants.appierApiToken;
    final res = await apiProvider.httpClient.postUri<Map<String, dynamic>>(
      Constants.uriBotBonnieApi,
      data: data,
    );
    return res.data?[Keys.code] ?? '';
  }
}
