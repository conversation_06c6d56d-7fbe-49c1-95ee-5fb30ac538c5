import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:logger/logger.dart';

import '../../enums.dart';

class MessageProvider {
  final WabowProvider wabowProvider;
  Logger get logger => wabowProvider.logger;

  MessageProvider({
    required this.wabowProvider,
  });

  Future<void> mark<PERSON><PERSON>ead(MessageType messageType) async {
    try {
      await wabowProvider.putMembersMessagesRead(messageType);
      await wabowProvider.getMembersMessagesUnread();
    } catch (e) {
      logger.e(e);
    }
  }
}
