import 'dart:convert';

import 'thumbnail.dart';

class MembersPopupRes {
  int? voucherId;
  String? name;
  String? popupTitle;
  String? popupButtonTitle;
  String? popupButtonPath;
  Thumbnail? thumbnail;

  MembersPopupRes({
    this.voucherId,
    this.name,
    this.popupTitle,
    this.popupButtonTitle,
    this.popupButtonPath,
    this.thumbnail,
  });

  MembersPopupRes copyWith({
    int? voucherId,
    String? name,
    String? popupTitle,
    String? popupButtonTitle,
    String? popupButtonPath,
    Thumbnail? thumbnail,
  }) =>
      MembersPopupRes(
        voucherId: voucherId ?? this.voucherId,
        name: name ?? this.name,
        popupTitle: popupTitle ?? this.popupTitle,
        popupButtonTitle: popupButtonTitle ?? this.popupButtonTitle,
        popupButtonPath: popupButtonPath ?? this.popupButtonPath,
        thumbnail: thumbnail ?? this.thumbnail,
      );

  factory MembersPopupRes.fromRawJson(String str) =>
      MembersPopupRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersPopupRes.fromJson(Map<String, dynamic> json) =>
      MembersPopupRes(
        voucherId: json["voucher_id"],
        name: json["name"],
        popupTitle: json["popup_title"],
        popupButtonTitle: json["popup_button_title"],
        popupButtonPath: json["popup_button_path"],
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
      );

  Map<String, dynamic> toJson() => {
        "voucher_id": voucherId,
        "name": name,
        "popup_title": popupTitle,
        "popup_button_title": popupButtonTitle,
        "popup_button_path": popupButtonPath,
        "thumbnail": thumbnail?.toJson(),
      };
}
