// To parse this JSON data, do
//
//     final membersOrdersIdRefundRes = membersOrdersIdRefundResFromJson(jsonString);

import 'dart:convert';

import 'product.dart';

class MembersOrdersRefundRes {
  String? receiverName;
  String? comment;
  String? shippingMethod;
  String? shippingNumber;
  String? orderNumber;
  String? type;
  String? bankBranch;
  String? bankCode;
  String? accountName;
  String? accountNumber;
  int? total;
  int? shippingFee;
  int? otherMoney;
  String? status;
  String? createDatetime;
  List<Product>? products;

  MembersOrdersRefundRes({
    this.receiverName,
    this.comment,
    this.shippingMethod,
    this.shippingNumber,
    this.orderNumber,
    this.type,
    this.bankBranch,
    this.bankCode,
    this.accountName,
    this.accountNumber,
    this.total,
    this.shippingFee,
    this.otherMoney,
    this.status,
    this.createDatetime,
    this.products,
  });

  MembersOrdersRefundRes copyWith({
    String? receiverName,
    String? comment,
    String? shippingMethod,
    String? shippingNumber,
    String? orderNumber,
    String? type,
    String? bankBranch,
    String? bankCode,
    String? accountName,
    String? accountNumber,
    int? total,
    int? shippingFee,
    int? otherMoney,
    String? status,
    String? createDatetime,
    List<Product>? products,
  }) =>
      MembersOrdersRefundRes(
        receiverName: receiverName ?? this.receiverName,
        comment: comment ?? this.comment,
        shippingMethod: shippingMethod ?? this.shippingMethod,
        shippingNumber: shippingNumber ?? this.shippingNumber,
        orderNumber: orderNumber ?? this.orderNumber,
        type: type ?? this.type,
        bankBranch: bankBranch ?? this.bankBranch,
        bankCode: bankCode ?? this.bankCode,
        accountName: accountName ?? this.accountName,
        accountNumber: accountNumber ?? this.accountNumber,
        total: total ?? this.total,
        shippingFee: shippingFee ?? this.shippingFee,
        otherMoney: otherMoney ?? this.otherMoney,
        status: status ?? this.status,
        createDatetime: createDatetime ?? this.createDatetime,
        products: products ?? this.products,
      );

  factory MembersOrdersRefundRes.fromRawJson(String str) =>
      MembersOrdersRefundRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersOrdersRefundRes.fromJson(Map<String, dynamic> json) =>
      MembersOrdersRefundRes(
        receiverName: json["receiver_name"],
        comment: json["comment"],
        shippingMethod: json["shipping_method"],
        shippingNumber: json["shipping_number"],
        orderNumber: json["order_number"],
        type: json["type"],
        bankBranch: json["bank_branch"],
        bankCode: json["bank_code"],
        accountName: json["account_name"],
        accountNumber: json["account_number"],
        total: json["total"],
        shippingFee: json["shipping_fee"],
        otherMoney: json["other_money"],
        status: json["status"],
        createDatetime: json["create_datetime"],
        products: json["products"] == null
            ? []
            : List<Product>.from(
                json["products"]!.map((x) => Product.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "receiver_name": receiverName,
        "comment": comment,
        "shipping_method": shippingMethod,
        "shipping_number": shippingNumber,
        "order_number": orderNumber,
        "type": type,
        "bank_branch": bankBranch,
        "bank_code": bankCode,
        "account_name": accountName,
        "account_number": accountNumber,
        "total": total,
        "shipping_fee": shippingFee,
        "other_money": otherMoney,
        "status": status,
        "create_datetime": createDatetime,
        "products": products == null
            ? []
            : List<dynamic>.from(products!.map((x) => x.toJson())),
      };
}
