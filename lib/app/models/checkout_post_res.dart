// To parse this JSON data, do
//
//     final checkoutPostRes = checkoutPostResFromJson(jsonString);

import 'dart:convert';

class CheckoutPostRes {
  String? flag;
  String? action;
  String? url;
  String? method;
  Data? data;

  CheckoutPostRes({
    this.flag,
    this.action,
    this.url,
    this.method,
    this.data,
  });

  CheckoutPostRes copyWith({
    String? flag,
    String? action,
    String? url,
    String? method,
    Data? data,
  }) =>
      CheckoutPostRes(
        flag: flag ?? this.flag,
        action: action ?? this.action,
        url: url ?? this.url,
        method: method ?? this.method,
        data: data ?? this.data,
      );

  factory CheckoutPostRes.fromRawJson(String str) =>
      CheckoutPostRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CheckoutPostRes.fromJson(Map<String, dynamic> json) =>
      CheckoutPostRes(
        flag: json["flag"],
        action: json["action"],
        url: json["url"],
        method: json["method"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "flag": flag,
        "action": action,
        "url": url,
        "method": method,
        "data": data?.toJson(),
      };
}

class Data {
  int? price;
  String? orderId;
  String? number;

  Data({
    this.price,
    this.orderId,
    this.number,
  });

  Data copyWith({
    int? price,
    String? orderId,
    String? number,
  }) =>
      Data(
        price: price ?? this.price,
        orderId: orderId ?? this.orderId,
        number: number ?? this.number,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        price: json["price"],
        orderId: json["order_id"],
        number: json["number"],
      );

  Map<String, dynamic> toJson() => {
        "price": price,
        "order_id": orderId,
        "number": number,
      };
}
