import 'dart:convert';

class AppierSearchedReq {
  String? searchedKeyword;

  AppierSearchedReq({
    this.searchedKeyword,
  });

  AppierSearchedReq copyWith({
    String? searchedKeyword,
  }) =>
      AppierSearchedReq(
        searchedKeyword: searchedKeyword ?? this.searchedKeyword,
      );

  factory AppierSearchedReq.fromRawJson(String str) =>
      AppierSearchedReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppierSearchedReq.fromJson(Map<String, dynamic> json) =>
      AppierSearchedReq(
        searchedKeyword: json["searched_keyword"],
      );

  Map<String, dynamic> toJson() => {
        "searched_keyword": searchedKeyword,
      };
}
