import 'dart:convert';

class AppierCartViewedRes {
  num? cartAmount;
  num? numberOfProducts;

  AppierCartViewedRes({
    this.cartAmount,
    this.numberOfProducts,
  });

  AppierCartViewedRes copyWith({
    num? cartAmount,
    num? numberOfProducts,
  }) =>
      AppierCartViewedRes(
        cartAmount: cartAmount ?? this.cartAmount,
        numberOfProducts: numberOfProducts ?? this.numberOfProducts,
      );

  factory AppierCartViewedRes.fromRawJson(String str) =>
      AppierCartViewedRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppierCartViewedRes.fromJson(Map<String, dynamic> json) =>
      AppierCartViewedRes(
        cartAmount: json["cart_amount"],
        numberOfProducts: json["number_of_products"],
      );

  Map<String, dynamic> toJson() => {
        "cart_amount": cartAmount,
        "number_of_products": numberOfProducts,
      };
}
