import 'dart:convert';

import 'thumbnail.dart';

class FavoriteData {
  Thumbnail? thumbnail;
  String? name;
  String? color;
  int? price;
  int? promotionPrice;
  String? size;
  String? quantity;
  String? available;
  String? id;

  FavoriteData({
    this.thumbnail,
    this.name,
    this.color,
    this.price,
    this.promotionPrice,
    this.size,
    this.quantity,
    this.available,
    this.id,
  });

  FavoriteData copyWith({
    Thumbnail? thumbnail,
    String? name,
    String? color,
    int? price,
    int? promotionPrice,
    String? size,
    String? quantity,
    String? available,
    String? id,
  }) =>
      FavoriteData(
        thumbnail: thumbnail ?? this.thumbnail,
        name: name ?? this.name,
        color: color ?? this.color,
        price: price ?? this.price,
        promotionPrice: promotionPrice ?? this.promotionPrice,
        size: size ?? this.size,
        quantity: quantity ?? this.quantity,
        available: available ?? this.available,
        id: id ?? this.id,
      );

  factory FavoriteData.fromRawJson(String str) =>
      FavoriteData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FavoriteData.fromJson(Map<String, dynamic> json) => FavoriteData(
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        name: json["name"],
        color: json["color"],
        price: json["price"],
        promotionPrice: json["promotion_price"],
        size: json["size"],
        quantity: '${json["quantity"]}',
        available: json["available"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "thumbnail": thumbnail?.toJson(),
        "name": name,
        "color": color,
        "price": price,
        "promotion_price": promotionPrice,
        "size": size,
        "quantity": quantity,
        "available": available,
      };
}
