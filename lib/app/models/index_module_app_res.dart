import 'dart:convert';

import 'index_module_app.dart';

class IndexModuleAppRes {
  String? name;
  String? type;
  String? sort;
  dynamic data;
  List<IndexModuleApp>? list;
  Map<String, IndexModuleApp>? map;

  IndexModuleAppRes({
    this.name,
    this.type,
    this.sort,
    this.data,
    this.list,
    this.map,
  });

  IndexModuleAppRes copyWith({
    String? name,
    String? type,
    String? sort,
    dynamic data,
  }) =>
      IndexModuleAppRes(
        name: name ?? this.name,
        type: type ?? this.type,
        sort: sort ?? this.sort,
        data: data ?? this.data,
      );

  factory IndexModuleAppRes.fromRawJson(String str) =>
      IndexModuleAppRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory IndexModuleAppRes.fromJson(Map<String, dynamic> json) =>
      IndexModuleAppRes(
        name: json["name"],
        type: json["type"],
        sort: json["sort"],
        data: json["data"],
        // list: json["data"] == null
        //     ? []
        //     : List<IndexModuleApp>.from(
        //         json["data"].map((x) => IndexModuleApp.fromJson(x))),
        // map: json["data"] == null
        //     ? {}
        //     : Map.from(json["data"]).map((k, v) =>
        //         MapEntry<String, IndexModuleApp>(
        //             k, IndexModuleApp.fromJson(v))),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "type": type,
        "sort": sort,
        "data": data,
      };
}
