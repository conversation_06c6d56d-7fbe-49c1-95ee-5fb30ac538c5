// To parse this JSON data, do
//
//     final membersRefundPostReq = membersRefundPostReqFromJson(jsonString);

import 'dart:convert';

class MembersRefundPostReq {
  bool? isDefault;
  String? bankBranch;
  String? bankCode;
  String? accountName;
  String? accountNumber;

  MembersRefundPostReq({
    this.isDefault,
    this.bankBranch,
    this.bankCode,
    this.accountName,
    this.accountNumber,
  });

  MembersRefundPostReq copyWith({
    bool? isDefault,
    String? bankBranch,
    String? bankCode,
    String? accountName,
    String? accountNumber,
  }) =>
      MembersRefundPostReq(
        isDefault: isDefault ?? this.isDefault,
        bankBranch: bankBranch ?? this.bankBranch,
        bankCode: bankCode ?? this.bankCode,
        accountName: accountName ?? this.accountName,
        accountNumber: accountNumber ?? this.accountNumber,
      );

  factory MembersRefundPostReq.fromRawJson(String str) =>
      MembersRefundPostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersRefundPostReq.fromJson(Map<String, dynamic> json) =>
      MembersRefundPostReq(
        isDefault: json["is_default"],
        bankBranch: json["bank_branch"],
        bankCode: json["bank_code"],
        accountName: json["account_name"],
        accountNumber: json["account_number"],
      );

  Map<String, dynamic> toJson() => {
        "is_default": isDefault,
        "bank_branch": bankBranch,
        "bank_code": bankCode,
        "account_name": accountName,
        "account_number": accountNumber,
      };
}
