// To parse this JSON data, do
//
//     final membersMessagesActivity = membersMessagesActivityFromJson(jsonString);

import 'dart:convert';

import 'thumbnail.dart';
import 'url.dart';

class MembersMessagesActivity {
  String? title;
  Url? url;
  Thumbnail? thumbnail;
  String? createDatetime;

  MembersMessagesActivity({
    this.title,
    this.url,
    this.thumbnail,
    this.createDatetime,
  });

  MembersMessagesActivity copyWith({
    String? title,
    Url? url,
    Thumbnail? thumbnail,
    String? createDatetime,
  }) =>
      MembersMessagesActivity(
        title: title ?? this.title,
        url: url ?? this.url,
        thumbnail: thumbnail ?? this.thumbnail,
        createDatetime: createDatetime ?? this.createDatetime,
      );

  factory MembersMessagesActivity.fromRawJson(String str) =>
      MembersMessagesActivity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMessagesActivity.fromJson(Map<String, dynamic> json) =>
      MembersMessagesActivity(
        title: json["title"],
        url: json["url"] is Map ? Url.fromJson(json["url"]) : null,
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        createDatetime: json["create_datetime"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "url": url?.toJson(),
        "thumbnail": thumbnail?.toJson(),
        "create_datetime": createDatetime,
      };
}
