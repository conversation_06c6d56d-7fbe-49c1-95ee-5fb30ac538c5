import 'dart:convert';

class AppierCategoryViewedReq {
  String? categoryName;

  AppierCategoryViewedReq({
    this.categoryName,
  });

  AppierCategoryViewedReq copyWith({
    String? categoryName,
  }) =>
      AppierCategoryViewedReq(
        categoryName: categoryName ?? this.categoryName,
      );

  factory AppierCategoryViewedReq.fromRawJson(String str) =>
      AppierCategoryViewedReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppierCategoryViewedReq.fromJson(Map<String, dynamic> json) =>
      AppierCategoryViewedReq(
        categoryName: json["category_name"],
      );

  Map<String, dynamic> toJson() => {
        "category_name": categoryName,
      };
}
