// To parse this JSON data, do
//
//     final city = cityFromJson(jsonString);

import 'dart:convert';

class City {
  String? name;
  List<Store>? data;

  City({
    this.name,
    this.data,
  });

  City copyWith({
    String? name,
    List<Store>? data,
  }) =>
      City(
        name: name ?? this.name,
        data: data ?? this.data,
      );

  factory City.fromRawJson(String str) => City.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory City.fromJson(Map<String, dynamic> json) => City(
        name: json["name"],
        data: json["data"] == null
            ? []
            : List<Store>.from(json["data"]!.map((x) => Store.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Store {
  String? code;
  String? name;

  Store({
    this.code,
    this.name,
  });

  Store copyWith({
    String? code,
    String? name,
  }) =>
      Store(
        code: code ?? this.code,
        name: name ?? this.name,
      );

  factory Store.fromRawJson(String str) => Store.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Store.fromJson(Map<String, dynamic> json) => Store(
        code: json["code"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "name": name,
      };
}
