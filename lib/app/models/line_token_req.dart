// To parse this JSON data, do
//
//     final lineTokenReq = lineTokenReqFromJson(jsonString);

import 'dart:convert';

class LineTokenReq {
  String? grantType;
  String? code;
  String? redirectUri;
  String? clientId;
  String? clientSecret;

  LineTokenReq({
    this.grantType,
    this.code,
    this.redirectUri,
    this.clientId,
    this.clientSecret,
  });

  LineTokenReq copyWith({
    String? grantType,
    String? code,
    String? redirectUri,
    String? clientId,
    String? clientSecret,
  }) =>
      LineTokenReq(
        grantType: grantType ?? this.grantType,
        code: code ?? this.code,
        redirectUri: redirectUri ?? this.redirectUri,
        clientId: clientId ?? this.clientId,
        clientSecret: clientSecret ?? this.clientSecret,
      );

  factory LineTokenReq.fromRawJson(String str) =>
      LineTokenReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LineTokenReq.fromJson(Map<String, dynamic> json) => LineTokenReq(
        grantType: json["grant_type"],
        code: json["code"],
        redirectUri: json["redirect_uri"],
        clientId: json["client_id"],
        clientSecret: json["client_secret"],
      );

  Map<String, dynamic> toJson() => {
        "grant_type": grantType,
        "code": code,
        "redirect_uri": redirectUri,
        "client_id": clientId,
        "client_secret": clientSecret,
      };
}
