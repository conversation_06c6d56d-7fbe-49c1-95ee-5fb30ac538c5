// To parse this JSON data, do
//
//     final checkoutRes = checkoutResFromJson(jsonString);

import 'dart:convert';

class CheckoutRes {
  String? checkoutResDefault;
  Payment? payment;

  CheckoutRes({
    this.checkoutResDefault,
    this.payment,
  });

  CheckoutRes copyWith({
    String? checkoutResDefault,
    Payment? payment,
  }) =>
      CheckoutRes(
        checkoutResDefault: checkoutResDefault ?? this.checkoutResDefault,
        payment: payment ?? this.payment,
      );

  factory CheckoutRes.fromRawJson(String str) =>
      CheckoutRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CheckoutRes.fromJson(Map<String, dynamic> json) => CheckoutRes(
        checkoutResDefault: json["default"],
        payment:
            json["payment"] == null ? null : Payment.fromJson(json["payment"]),
      );

  Map<String, dynamic> toJson() => {
        "default": checkoutResDefault,
        "payment": payment?.toJson(),
      };
}

class Payment {
  PaymentDetail? the7Eshop;
  PaymentDetail? fmeshop;
  PaymentDetail? cod;
  PaymentDetail? chinatrust;
  PaymentDetail? hitrust;
  PaymentDetail? linePay;

  Payment({
    this.the7Eshop,
    this.fmeshop,
    this.cod,
    this.chinatrust,
    this.hitrust,
    this.linePay,
  });

  Payment copyWith({
    PaymentDetail? the7Eshop,
    PaymentDetail? fmeshop,
    PaymentDetail? cod,
    PaymentDetail? chinatrust,
    PaymentDetail? hitrust,
    PaymentDetail? linePay,
  }) =>
      Payment(
        the7Eshop: the7Eshop ?? this.the7Eshop,
        fmeshop: fmeshop ?? this.fmeshop,
        cod: cod ?? this.cod,
        chinatrust: chinatrust ?? this.chinatrust,
        hitrust: hitrust ?? this.hitrust,
        linePay: linePay ?? this.linePay,
      );

  factory Payment.fromRawJson(String str) => Payment.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Payment.fromJson(Map<String, dynamic> json) => Payment(
        the7Eshop:
            json["7eshop"] == null ? null : PaymentDetail.fromJson(json["7eshop"]),
        fmeshop: json["fmeshop"] == null
            ? null
            : PaymentDetail.fromJson(json["fmeshop"]),
        cod: json["cod"] == null ? null : PaymentDetail.fromJson(json["cod"]),
        chinatrust: json["chinatrust"] == null
            ? null
            : PaymentDetail.fromJson(json["chinatrust"]),
        hitrust: json["hitrust"] == null
            ? null
            : PaymentDetail.fromJson(json["hitrust"]),
        linePay: json["line-pay"] == null
            ? null
            : PaymentDetail.fromJson(json["line-pay"]),
      );

  Map<String, dynamic> toJson() => {
        "7eshop": the7Eshop?.toJson(),
        "fmeshop": fmeshop?.toJson(),
        "cod": cod?.toJson(),
        "chinatrust": chinatrust?.toJson(),
        "hitrust": hitrust?.toJson(),
        "line-pay": linePay?.toJson(),
      };
}

class PaymentDetail {
  String? description;
  bool? enable;
  String? shippingThreshold;
  String? shippingFee;

  PaymentDetail({
    this.description,
    this.enable,
    this.shippingThreshold,
    this.shippingFee,
  });

  PaymentDetail copyWith({
    String? description,
    bool? enable,
    String? shippingThreshold,
    String? shippingFee,
  }) =>
      PaymentDetail(
        description: description ?? this.description,
        enable: enable ?? this.enable,
        shippingThreshold: shippingThreshold ?? this.shippingThreshold,
        shippingFee: shippingFee ?? this.shippingFee,
      );

  factory PaymentDetail.fromRawJson(String str) =>
      PaymentDetail.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PaymentDetail.fromJson(Map<String, dynamic> json) => PaymentDetail(
        description: json["description"],
        enable: json["enable"],
        shippingThreshold: json["shipping_threshold"],
        shippingFee: json["shipping_fee"],
      );

  Map<String, dynamic> toJson() => {
        "description": description,
        "enable": enable,
        "shipping_threshold": shippingThreshold,
        "shipping_fee": shippingFee,
      };
}
