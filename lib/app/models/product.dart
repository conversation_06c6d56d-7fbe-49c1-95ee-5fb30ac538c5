import 'dart:convert';

import 'thumbnail.dart';
import 'url.dart';

class Product {
  String? parentId;
  String? productId;
  String? productNumber;
  String? productName;
  String? productSpecName;
  String? productColor;
  String? productSize;
  String? finalPrice;
  String? quantity;
  String? giftName;
  String? cartQuantityRatio;
  String? sort;
  String? promotionName;
  String? promotionId;
  bool? isBuyNGiftM;
  String? originalPrice;
  int? orderQuantity;
  int? refundQuantity;
  int? subtotal;
  String? userComment;
  String? shareUrl;
  bool? isRefundAble;
  String? reason;
  Url? url;
  Thumbnail? thumbnail;
  String? refundProductName;
  Thumbnail? rectangleImage;
  Thumbnail? squareImage;

  Product({
    this.reason,
    this.refundQuantity,
    this.orderQuantity,
    this.parentId,
    this.productId,
    this.productNumber,
    this.productName,
    this.productSpecName,
    this.productColor,
    this.productSize,
    this.finalPrice,
    this.quantity,
    this.giftName,
    this.cartQuantityRatio,
    this.sort,
    this.promotionName,
    this.promotionId,
    this.isBuyNGiftM,
    this.originalPrice,
    this.subtotal,
    this.userComment,
    this.shareUrl,
    this.isRefundAble,
    this.url,
    this.thumbnail,
    this.refundProductName,
    this.rectangleImage,
    this.squareImage,
  });

  Product copyWith({
    String? reason,
    int? refundQuantity,
    int? orderQuantity,
    String? parentId,
    String? productId,
    String? productNumber,
    String? productName,
    String? productSpecName,
    String? productColor,
    String? productSize,
    String? finalPrice,
    String? quantity,
    String? giftName,
    String? cartQuantityRatio,
    String? sort,
    String? promotionName,
    String? promotionId,
    bool? isBuyNGiftM,
    String? originalPrice,
    int? subtotal,
    String? userComment,
    String? shareUrl,
    bool? isRefundAble,
    Url? url,
    Thumbnail? thumbnail,
    String? refundProductName,
    Thumbnail? rectangleImage,
    Thumbnail? squareImage,
  }) =>
      Product(
        parentId: parentId ?? this.parentId,
        productId: productId ?? this.productId,
        productNumber: productNumber ?? this.productNumber,
        productName: productName ?? this.productName,
        productSpecName: productSpecName ?? this.productSpecName,
        productColor: productColor ?? this.productColor,
        productSize: productSize ?? this.productSize,
        finalPrice: finalPrice ?? this.finalPrice,
        quantity: quantity ?? this.quantity,
        giftName: giftName ?? this.giftName,
        cartQuantityRatio: cartQuantityRatio ?? this.cartQuantityRatio,
        sort: sort ?? this.sort,
        promotionName: promotionName ?? this.promotionName,
        promotionId: promotionId ?? this.promotionId,
        isBuyNGiftM: isBuyNGiftM ?? this.isBuyNGiftM,
        originalPrice: originalPrice ?? this.originalPrice,
        subtotal: subtotal ?? this.subtotal,
        userComment: userComment ?? this.userComment,
        shareUrl: shareUrl ?? this.shareUrl,
        isRefundAble: isRefundAble ?? this.isRefundAble,
        url: url ?? this.url,
        thumbnail: thumbnail ?? this.thumbnail,
        refundProductName: refundProductName ?? this.refundProductName,
        rectangleImage: rectangleImage ?? this.rectangleImage,
        squareImage: squareImage ?? this.squareImage,
      );

  factory Product.fromRawJson(String str) => Product.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Product.fromJson(Map<String, dynamic> json) => Product(
        reason: json["reason"],
        refundQuantity: json["refund_quantity"],
        orderQuantity: json["order_quantity"],
        parentId: json["parent_id"],
        productId: json["product_id"],
        productNumber: json["product_number"],
        productName: json["product_name"],
        productSpecName: json["product_spec_name"],
        productColor: json["product_color"],
        productSize: json["product_size"],
        finalPrice: '${json["final_price"]}',
        quantity: '${json["quantity"]}',
        giftName: json["gift_name"],
        cartQuantityRatio: '${json["cart_quantity_ratio"]}',
        sort: json["sort"],
        promotionName: json["promotion_name"],
        promotionId: '${json["promotion_id"]}',
        isBuyNGiftM: json["is_buy_n_gift_m"],
        originalPrice: '${json["original_price"]}',
        subtotal: json["subtotal"],
        userComment: json["user_comment"],
        shareUrl: json["share_url"],
        isRefundAble: json["is_refund_able"],
        url: json["url"] is Map ? Url.fromJson(json["url"]) : null,
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        refundProductName: json["refund_product_name"],
        rectangleImage: json["rectangle_image"] == null
            ? null
            : Thumbnail.fromJson(json["rectangle_image"]),
        squareImage: json["square_image"] == null
            ? null
            : Thumbnail.fromJson(json["square_image"]),
      );

  Map<String, dynamic> toJson() => {
        "reason": reason,
        "refund_quantity": refundQuantity,
        "order_quantity": orderQuantity,
        "parent_id": parentId,
        "product_id": productId,
        "product_number": productNumber,
        "product_name": productName,
        "product_spec_name": productSpecName,
        "product_color": productColor,
        "product_size": productSize,
        "final_price": finalPrice,
        "quantity": quantity,
        "gift_name": giftName,
        "cart_quantity_ratio": cartQuantityRatio,
        "sort": sort,
        "promotion_name": promotionName,
        "promotion_id": promotionId,
        "is_buy_n_gift_m": isBuyNGiftM,
        "original_price": originalPrice,
        "subtotal": subtotal,
        "user_comment": userComment,
        "share_url": shareUrl,
        "is_refund_able": isRefundAble,
        "url": url?.toJson(),
        "thumbnail": thumbnail?.toJson(),
        "refund_product_name": refundProductName,
        "rectangle_image": rectangleImage?.toJson(),
        "square_image": squareImage?.toJson(),
      };
}
