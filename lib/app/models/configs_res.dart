// To parse this JSON data, do
//
//     final configsRes = configsResFromJson(jsonString);

import 'dart:convert';

import 'seo.dart';

class ConfigsRes {
  String? appVersionIosRequires;
  String? appVersionAndroidRequires;
  String? fraudVisible;
  String? paymentDefault;
  String? userSearchesShowLimit;
  String? productListShowColumn;
  String? cacheVersion;
  Seo? seo;
  Snow? snow;
  List<ShippingEvent>? shippingEvents;

  ConfigsRes({
    this.appVersionIosRequires,
    this.appVersionAndroidRequires,
    this.fraudVisible,
    this.paymentDefault,
    this.userSearchesShowLimit,
    this.productListShowColumn,
    this.cacheVersion,
    this.seo,
    this.snow,
    this.shippingEvents,
  });

  ConfigsRes copyWith({
    String? appVersionIosRequires,
    String? appVersionAndroidRequires,
    String? fraudVisible,
    String? paymentDefault,
    String? userSearchesShowLimit,
    String? productListShowColumn,
    String? cacheVersion,
    Seo? seo,
    Snow? snow,
    List<ShippingEvent>? shippingEvents,
  }) =>
      ConfigsRes(
        appVersionIosRequires:
            appVersionIosRequires ?? this.appVersionIosRequires,
        appVersionAndroidRequires:
            appVersionAndroidRequires ?? this.appVersionAndroidRequires,
        fraudVisible: fraudVisible ?? this.fraudVisible,
        paymentDefault: paymentDefault ?? this.paymentDefault,
        userSearchesShowLimit:
            userSearchesShowLimit ?? this.userSearchesShowLimit,
        productListShowColumn:
            productListShowColumn ?? this.productListShowColumn,
        cacheVersion: cacheVersion ?? this.cacheVersion,
        seo: seo ?? this.seo,
        snow: snow ?? this.snow,
        shippingEvents: shippingEvents ?? this.shippingEvents,
      );

  factory ConfigsRes.fromRawJson(String str) =>
      ConfigsRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ConfigsRes.fromJson(Map<String, dynamic> json) => ConfigsRes(
        appVersionIosRequires: json["app_version_ios_requires"],
        appVersionAndroidRequires: json["app_version_android_requires"],
        fraudVisible: json["fraudVisible"],
        paymentDefault: json["payment_default"],
        userSearchesShowLimit: json["user_searches_show_limit"],
        productListShowColumn: json["product_list_show_column"],
        cacheVersion: json["cache_version"],
        seo: json["seo"] == null ? null : Seo.fromJson(json["seo"]),
        snow: json["snow"] == null ? null : Snow.fromJson(json["snow"]),
        shippingEvents: json["shipping_events"] == null
            ? []
            : List<ShippingEvent>.from(
                json["shipping_events"]!.map((x) => ShippingEvent.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "app_version_ios_requires": appVersionIosRequires,
        "app_version_android_requires": appVersionAndroidRequires,
        "fraudVisible": fraudVisible,
        "payment_default": paymentDefault,
        "user_searches_show_limit": userSearchesShowLimit,
        "product_list_show_column": productListShowColumn,
        "cache_version": cacheVersion,
        "seo": seo?.toJson(),
        "snow": snow?.toJson(),
        "shipping_events": shippingEvents == null
            ? []
            : List<dynamic>.from(shippingEvents!.map((x) => x.toJson())),
      };
}

class ShippingEvent {
    bool? isMobile;
    bool? isApp;
    bool? enable;
    String? eventPrefix;
    String? eventName;
    String? eventUrl;

    ShippingEvent({
        this.isMobile,
        this.isApp,
        this.enable,
        this.eventPrefix,
        this.eventName,
        this.eventUrl,
    });

    ShippingEvent copyWith({
        bool? isMobile,
        bool? isApp,
        bool? enable,
        String? eventPrefix,
        String? eventName,
        String? eventUrl,
    }) => 
        ShippingEvent(
            isMobile: isMobile ?? this.isMobile,
            isApp: isApp ?? this.isApp,
            enable: enable ?? this.enable,
            eventPrefix: eventPrefix ?? this.eventPrefix,
            eventName: eventName ?? this.eventName,
            eventUrl: eventUrl ?? this.eventUrl,
        );

    factory ShippingEvent.fromRawJson(String str) => ShippingEvent.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory ShippingEvent.fromJson(Map<String, dynamic> json) => ShippingEvent(
        isMobile: json["is_mobile"],
        isApp: json["is_app"],
        enable: json["enable"],
        eventPrefix: json["event_prefix"],
        eventName: json["event_name"],
        eventUrl: json["event_url"],
    );

    Map<String, dynamic> toJson() => {
        "is_mobile": isMobile,
        "is_app": isApp,
        "enable": enable,
        "event_prefix": eventPrefix,
        "event_name": eventName,
        "event_url": eventUrl,
    };
}

class Snow {
  bool? enable;
  String? image;

  Snow({
    this.enable,
    this.image,
  });

  Snow copyWith({
    bool? enable,
    String? image,
  }) =>
      Snow(
        enable: enable ?? this.enable,
        image: image ?? this.image,
      );

  factory Snow.fromRawJson(String str) => Snow.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Snow.fromJson(Map<String, dynamic> json) => Snow(
        enable: json["enable"],
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "enable": enable,
        "image": image,
      };
}
