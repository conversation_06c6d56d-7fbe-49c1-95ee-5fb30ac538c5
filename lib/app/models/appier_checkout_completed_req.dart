import 'dart:convert';

class AppierCheckoutCompletedReq {
  String? orderId;
  num? numberOfProducts;
  num? orderAmount;

  AppierCheckoutCompletedReq({
    this.orderId,
    this.numberOfProducts,
    this.orderAmount,
  });

  AppierCheckoutCompletedReq copyWith({
    String? orderId,
    num? numberOfProducts,
    num? orderAmount,
  }) =>
      AppierCheckoutCompletedReq(
        orderId: orderId ?? this.orderId,
        numberOfProducts: numberOfProducts ?? this.numberOfProducts,
        orderAmount: orderAmount ?? this.orderAmount,
      );

  factory AppierCheckoutCompletedReq.fromRawJson(String str) =>
      AppierCheckoutCompletedReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppierCheckoutCompletedReq.fromJson(Map<String, dynamic> json) =>
      AppierCheckoutCompletedReq(
        orderId: json["order_id"],
        numberOfProducts: json["number_of_products"],
        orderAmount: json["order_amount"],
      );

  Map<String, dynamic> toJson() => {
        "order_id": orderId,
        "number_of_products": numberOfProducts,
        "order_amount": orderAmount,
      };
}
