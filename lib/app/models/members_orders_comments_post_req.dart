// To parse this JSON data, do
//
//     final membersOrdersCommentsPostReq = membersOrdersCommentsPostReqFromJson(jsonString);

import 'dart:convert';

class MembersOrdersCommentsPostReq {
  String? productId;
  String? comment;

  MembersOrdersCommentsPostReq({
    this.productId,
    this.comment,
  });

  MembersOrdersCommentsPostReq copyWith({
    String? productId,
    String? comment,
  }) =>
      MembersOrdersCommentsPostReq(
        productId: productId ?? this.productId,
        comment: comment ?? this.comment,
      );

  factory MembersOrdersCommentsPostReq.fromRawJson(String str) =>
      MembersOrdersCommentsPostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersOrdersCommentsPostReq.fromJson(Map<String, dynamic> json) =>
      MembersOrdersCommentsPostReq(
        productId: json["product_id"],
        comment: json["comment"],
      );

  Map<String, dynamic> toJson() => {
        "product_id": productId,
        "comment": comment,
      };
}
