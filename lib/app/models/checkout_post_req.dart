// To parse this JSON data, do
//
//     final checkoutPostReq = checkoutPostReqFromJson(jsonString);

import 'dart:convert';

class CheckoutPostReq {
  String? buyerName;
  String? buyerTelephone;
  String? buyerCity;
  String? buyerPostcode;
  String? buyerAddress;
  String? receiverName;
  String? receiverTelephone;
  String? receiverCity;
  String? storeId;
  String? storeName;
  String? storeAddress;
  String? vatNumber;
  String? invoiceVehicle;
  String? comment;
  String? parentId;

  CheckoutPostReq({
    this.buyerName,
    this.buyerTelephone,
    this.buyerCity,
    this.buyerPostcode,
    this.buyerAddress,
    this.receiverName,
    this.receiverTelephone,
    this.receiverCity,
    this.storeId,
    this.storeName,
    this.storeAddress,
    this.vatNumber,
    this.invoiceVehicle,
    this.comment,
    this.parentId,
  });

  CheckoutPostReq copyWith({
    String? buyerName,
    String? buyerTelephone,
    String? buyerCity,
    String? buyerPostcode,
    String? buyerAddress,
    String? receiverName,
    String? receiverTelephone,
    String? receiverCity,
    String? storeId,
    String? storeName,
    String? storeAddress,
    String? vatNumber,
    String? invoiceVehicle,
    String? comment,
    String? parentId,
  }) =>
      CheckoutPostReq(
        buyerName: buyerName ?? this.buyerName,
        buyerTelephone: buyerTelephone ?? this.buyerTelephone,
        buyerCity: buyerCity ?? this.buyerCity,
        buyerPostcode: buyerPostcode ?? this.buyerPostcode,
        buyerAddress: buyerAddress ?? this.buyerAddress,
        receiverName: receiverName ?? this.receiverName,
        receiverTelephone: receiverTelephone ?? this.receiverTelephone,
        receiverCity: receiverCity ?? this.receiverCity,
        storeId: storeId ?? this.storeId,
        storeName: storeName ?? this.storeName,
        storeAddress: storeAddress ?? this.storeAddress,
        vatNumber: vatNumber ?? this.vatNumber,
        invoiceVehicle: invoiceVehicle ?? this.invoiceVehicle,
        comment: comment ?? this.comment,
        parentId: parentId ?? this.parentId,
      );

  factory CheckoutPostReq.fromRawJson(String str) =>
      CheckoutPostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CheckoutPostReq.fromJson(Map<String, dynamic> json) =>
      CheckoutPostReq(
        buyerName: json["buyer_name"],
        buyerTelephone: json["buyer_telephone"],
        buyerCity: json["buyer_city"],
        buyerPostcode: json["buyer_postcode"],
        buyerAddress: json["buyer_address"],
        receiverName: json["receiver_name"],
        receiverTelephone: json["receiver_telephone"],
        receiverCity: json["receiver_city"],
        storeId: json["store_id"],
        storeName: json["store_name"],
        storeAddress: json["store_address"],
        vatNumber: json["vat_number"],
        invoiceVehicle: json["invoice_vehicle"],
        comment: json["comment"],
        parentId: json["parent_id"],
      );

  Map<String, dynamic> toJson() => {
        "buyer_name": buyerName,
        "buyer_telephone": buyerTelephone,
        "buyer_city": buyerCity,
        "buyer_postcode": buyerPostcode,
        "buyer_address": buyerAddress,
        "receiver_name": receiverName,
        "receiver_telephone": receiverTelephone,
        "receiver_city": receiverCity,
        "store_id": storeId,
        "store_name": storeName,
        "store_address": storeAddress,
        "vat_number": vatNumber,
        "invoice_vehicle": invoiceVehicle,
        "comment": comment,
        "parent_id": parentId,
      };
}
