import 'dart:convert';

class Thumbnail {
  String? src;
  num? width;
  num? height;

  Thumbnail({
    this.src,
    this.width,
    this.height,
  });

  Thumbnail copyWith({
    String? src,
    num? width,
    num? height,
  }) =>
      Thumbnail(
        src: src ?? this.src,
        width: width ?? this.width,
        height: height ?? this.height,
      );

  factory Thumbnail.fromRawJson(String str) =>
      Thumbnail.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Thumbnail.fromJson(Map<String, dynamic> json) => Thumbnail(
        src: json["src"],
        width: json["width"] is num
            ? json["width"]
            : num.tryParse(json["width"] ?? '0'),
        height: json["height"] is num
            ? json["height"]
            : num.tryParse(json["height"] ?? '0'),
      );

  Map<String, dynamic> toJson() => {
        "src": src,
        "width": width,
        "height": height,
      };
}
