// To parse this JSON data, do
//
//     final membersAddressRes = membersAddressResFromJson(jsonString);

import 'dart:convert';

class MembersAddressRes {
  String? id;
  String? type;
  bool? isDefault;
  String? receiverName;
  String? storeId;
  String? storeName;
  String? zipcode;
  String? city;
  String? town;
  String? address;

  MembersAddressRes({
    this.id,
    this.type,
    this.isDefault,
    this.receiverName,
    this.storeId,
    this.storeName,
    this.zipcode,
    this.city,
    this.town,
    this.address,
  });

  MembersAddressRes copyWith({
    String? id,
    String? type,
    bool? isDefault,
    String? receiverName,
    String? storeId,
    String? storeName,
    String? zipcode,
    String? city,
    String? town,
    String? address,
  }) =>
      MembersAddressRes(
        id: id ?? this.id,
        type: type ?? this.type,
        isDefault: isDefault ?? this.isDefault,
        receiverName: receiverName ?? this.receiverName,
        storeId: storeId ?? this.storeId,
        storeName: storeName ?? this.storeName,
        zipcode: zipcode ?? this.zipcode,
        city: city ?? this.city,
        town: town ?? this.town,
        address: address ?? this.address,
      );

  factory MembersAddressRes.fromRawJson(String str) =>
      MembersAddressRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersAddressRes.fromJson(Map<String, dynamic> json) =>
      MembersAddressRes(
        id: json["id"],
        type: json["type"],
        isDefault: json["is_default"],
        receiverName: json["receiver_name"],
        storeId: json["store_id"],
        storeName: json["store_name"],
        zipcode: json["zipcode"],
        city: json["city"],
        town: json["town"],
        address: json["address"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "type": type,
        "is_default": isDefault,
        "receiver_name": receiverName,
        "store_id": storeId,
        "store_name": storeName,
        "zipcode": zipcode,
        "city": city,
        "town": town,
        "address": address,
      };
}
