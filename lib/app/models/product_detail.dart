// To parse this JSON data, do
//
//     final productDetail = productDetailFromJson(jsonString);

import 'dart:convert';

import 'category.dart';
import 'seo.dart';
import 'thumbnail.dart';
import 'url.dart';

class ProductDetail {
  String? id;
  String? parentId;
  String? number;
  String? name;
  String? specName;
  String? specification;
  String? description;
  String? descriptionImage;
  String? tryReport;
  String? color;
  String? size;
  Thumbnail? thumbnail;
  String? quantity;
  int? maxQuantity;
  int? safeQuantity;
  String? price;
  int? promotionPrice;
  int? mobileShowComment;
  int? mobileShowSizes;
  List<Category>? categories;
  List<Category>? leafCategories;
  List<Category>? topCategories;
  List<Size>? sizes;
  Url? url;
  int? commentCount;
  String? categoryName;
  int? soldTotal;
  String? promotionType;
  String? promotionName;
  int? promotionId;
  Thumbnail? squareThumbnail;
  OgThumbnail? ogThumbnail;
  String? shareUrl;
  String? topCategoryName;
  Thumbnail? rectangleImage;
  Thumbnail? squareImage;
  OgThumbnail? openGraphImage;
  Seo? seo;
  Map<String, dynamic>? category;

  ProductDetail({
    this.id,
    this.parentId,
    this.number,
    this.name,
    this.specName,
    this.specification,
    this.description,
    this.descriptionImage,
    this.tryReport,
    this.color,
    this.size,
    this.thumbnail,
    this.quantity,
    this.maxQuantity,
    this.safeQuantity,
    this.price,
    this.promotionPrice,
    this.mobileShowComment,
    this.mobileShowSizes,
    this.categories,
    this.leafCategories,
    this.topCategories,
    this.sizes,
    this.url,
    this.commentCount,
    this.categoryName,
    this.soldTotal,
    this.promotionType,
    this.promotionName,
    this.promotionId,
    this.squareThumbnail,
    this.ogThumbnail,
    this.shareUrl,
    this.topCategoryName,
    this.rectangleImage,
    this.squareImage,
    this.openGraphImage,
    this.seo,
    this.category,
  });

  ProductDetail copyWith({
    String? id,
    String? parentId,
    String? number,
    String? name,
    String? specName,
    String? specification,
    String? description,
    String? descriptionImage,
    String? tryReport,
    String? color,
    String? size,
    Thumbnail? thumbnail,
    String? quantity,
    int? maxQuantity,
    int? safeQuantity,
    String? price,
    int? promotionPrice,
    int? mobileShowComment,
    int? mobileShowSizes,
    List<Category>? categories,
    List<Category>? leafCategories,
    List<Category>? topCategories,
    List<Size>? sizes,
    Url? url,
    int? commentCount,
    String? categoryName,
    int? soldTotal,
    String? promotionType,
    String? promotionName,
    int? promotionId,
    Thumbnail? squareThumbnail,
    OgThumbnail? ogThumbnail,
    String? shareUrl,
    String? topCategoryName,
    Thumbnail? rectangleImage,
    Thumbnail? squareImage,
    OgThumbnail? openGraphImage,
    Seo? seo,
    Map<String, dynamic>? category,
  }) =>
      ProductDetail(
        id: id ?? this.id,
        parentId: parentId ?? this.parentId,
        number: number ?? this.number,
        name: name ?? this.name,
        specName: specName ?? this.specName,
        specification: specification ?? this.specification,
        description: description ?? this.description,
        descriptionImage: descriptionImage ?? this.descriptionImage,
        tryReport: tryReport ?? this.tryReport,
        color: color ?? this.color,
        size: size ?? this.size,
        thumbnail: thumbnail ?? this.thumbnail,
        quantity: quantity ?? this.quantity,
        maxQuantity: maxQuantity ?? this.maxQuantity,
        safeQuantity: safeQuantity ?? this.safeQuantity,
        price: price ?? this.price,
        promotionPrice: promotionPrice ?? this.promotionPrice,
        mobileShowComment: mobileShowComment ?? this.mobileShowComment,
        mobileShowSizes: mobileShowSizes ?? this.mobileShowSizes,
        categories: categories ?? this.categories,
        leafCategories: leafCategories ?? this.leafCategories,
        topCategories: topCategories ?? this.topCategories,
        sizes: sizes ?? this.sizes,
        url: url ?? this.url,
        commentCount: commentCount ?? this.commentCount,
        categoryName: categoryName ?? this.categoryName,
        soldTotal: soldTotal ?? this.soldTotal,
        promotionType: promotionType ?? this.promotionType,
        promotionName: promotionName ?? this.promotionName,
        promotionId: promotionId ?? this.promotionId,
        squareThumbnail: squareThumbnail ?? this.squareThumbnail,
        ogThumbnail: ogThumbnail ?? this.ogThumbnail,
        shareUrl: shareUrl ?? this.shareUrl,
        topCategoryName: topCategoryName ?? this.topCategoryName,
        rectangleImage: rectangleImage ?? this.rectangleImage,
        squareImage: squareImage ?? this.squareImage,
        openGraphImage: openGraphImage ?? this.openGraphImage,
        seo: seo ?? this.seo,
        category: category ?? this.category,
      );

  factory ProductDetail.fromRawJson(String str) =>
      ProductDetail.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductDetail.fromJson(Map<String, dynamic> json) => ProductDetail(
        id: '${json["id"]}',
        parentId: json["parent_id"],
        number: json["number"],
        name: json["name"],
        specName: json["spec_name"],
        specification: json["specification"],
        description: json["description"],
        descriptionImage: json["description_image"],
        tryReport: json["try_report"],
        color: json["color"],
        size: json["size"],
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        quantity: '${json["quantity"]}',
        maxQuantity: json["max_quantity"],
        safeQuantity: json["safe_quantity"],
        price: '${json["price"]}',
        promotionPrice: json["promotion_price"],
        mobileShowComment: json["mobile_show_comment"],
        mobileShowSizes: json["mobile_show_sizes"],
        categories: json["categories"] == null
            ? []
            : List<Category>.from(
                json["categories"]!.map((x) => Category.fromJson(x))),
        leafCategories: json["leaf_categories"] == null
            ? []
            : List<Category>.from(
                json["leaf_categories"]!.map((x) => Category.fromJson(x))),
        topCategories: json["top_categories"] == null
            ? []
            : List<Category>.from(
                json["top_categories"]!.map((x) => Category.fromJson(x))),
        sizes: json["sizes"] == null
            ? []
            : List<Size>.from(json["sizes"]!.map((x) => Size.fromJson(x))),
        url: json["url"] is Map ? Url.fromJson(json["url"]) : null,
        commentCount: json["comment_count"],
        categoryName: '${json["category_name"]}',
        soldTotal: json["sold_total"],
        promotionType: json["promotion_type"],
        promotionName: json["promotion_name"],
        promotionId: json["promotion_id"],
        squareThumbnail: json["square_thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["square_thumbnail"]),
        ogThumbnail: json["og_thumbnail"] == null
            ? null
            : OgThumbnail.fromJson(json["og_thumbnail"]),
        shareUrl: json["share_url"],
        topCategoryName: '${json["top_category_name"]}',
        rectangleImage: json["rectangle_image"] == null
            ? null
            : Thumbnail.fromJson(json["rectangle_image"]),
        squareImage: json["square_image"] == null
            ? null
            : Thumbnail.fromJson(json["square_image"]),
        openGraphImage: json["open_graph_image"] == null
            ? null
            : OgThumbnail.fromJson(json["open_graph_image"]),
        seo: json["seo"] == null ? null : Seo.fromJson(json["seo"]),
        category: json["category"] is Map
            ? Map<String, dynamic>.from(json["category"])
            : {},
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "parent_id": parentId,
        "number": number,
        "name": name,
        "spec_name": specName,
        "specification": specification,
        "description": description,
        "description_image": descriptionImage,
        "try_report": tryReport,
        "color": color,
        "size": size,
        "thumbnail": thumbnail?.toJson(),
        "quantity": quantity,
        "max_quantity": maxQuantity,
        "safe_quantity": safeQuantity,
        "price": price,
        "promotion_price": promotionPrice,
        "mobile_show_comment": mobileShowComment,
        "mobile_show_sizes": mobileShowSizes,
        "categories": categories == null
            ? []
            : List<dynamic>.from(categories!.map((x) => x.toJson())),
        "leaf_categories": leafCategories == null
            ? []
            : List<dynamic>.from(leafCategories!.map((x) => x.toJson())),
        "top_categories": topCategories == null
            ? []
            : List<dynamic>.from(topCategories!.map((x) => x.toJson())),
        "sizes": sizes == null
            ? []
            : List<dynamic>.from(sizes!.map((x) => x.toJson())),
        "url": url?.toJson(),
        "comment_count": commentCount,
        "category_name": categoryName,
        "sold_total": soldTotal,
        "promotion_type": promotionType,
        "promotion_name": promotionName,
        "promotion_id": promotionId,
        "square_thumbnail": squareThumbnail?.toJson(),
        "og_thumbnail": ogThumbnail?.toJson(),
        "share_url": shareUrl,
        "top_category_name": topCategoryName,
        "rectangle_image": rectangleImage?.toJson(),
        "square_image": squareImage?.toJson(),
        "open_graph_image": openGraphImage?.toJson(),
        "seo": seo?.toJson(),
        "category": category ?? {},
      };
}

class OgThumbnail {
  String? src;

  OgThumbnail({
    this.src,
  });

  OgThumbnail copyWith({
    String? src,
  }) =>
      OgThumbnail(
        src: src ?? this.src,
      );

  factory OgThumbnail.fromRawJson(String str) =>
      OgThumbnail.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OgThumbnail.fromJson(Map<String, dynamic> json) => OgThumbnail(
        src: json["src"],
      );

  Map<String, dynamic> toJson() => {
        "src": src,
      };
}

class Size {
  String? id;
  String? number;
  String? specName;
  String? size;
  String? shareUrl;
  Url? url;

  Size({
    this.id,
    this.number,
    this.specName,
    this.size,
    this.shareUrl,
    this.url,
  });

  Size copyWith({
    String? id,
    String? number,
    String? specName,
    String? size,
    String? shareUrl,
    Url? url,
  }) =>
      Size(
        id: id ?? this.id,
        number: number ?? this.number,
        specName: specName ?? this.specName,
        size: size ?? this.size,
        shareUrl: shareUrl ?? this.shareUrl,
        url: url ?? this.url,
      );

  factory Size.fromRawJson(String str) => Size.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Size.fromJson(Map<String, dynamic> json) => Size(
        id: json["id"],
        number: json["number"],
        specName: json["spec_name"],
        size: json["size"],
        shareUrl: json["share_url"],
        url: json["url"] is Map ? Url.fromJson(json["url"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "number": number,
        "spec_name": specName,
        "size": size,
        "share_url": shareUrl,
        "url": url?.toJson(),
      };
}
