import 'dart:convert';

import 'thumbnail.dart';

class MembersPreordersRes {
  String? id;
  String? productId;
  String? productName;
  int? productPrice;
  int? promotionPrice;
  int? quantity;
  String? color;
  String? specName;
  String? size;
  bool? available;
  Thumbnail? thumbnail;
  Thumbnail? rectangleImage;
  Thumbnail? squareImage;

  MembersPreordersRes({
    this.id,
    this.productId,
    this.productName,
    this.productPrice,
    this.promotionPrice,
    this.quantity,
    this.color,
    this.specName,
    this.size,
    this.available,
    this.thumbnail,
    this.rectangleImage,
    this.squareImage,
  });

  MembersPreordersRes copyWith({
    String? id,
    String? productId,
    String? productName,
    int? productPrice,
    int? promotionPrice,
    int? quantity,
    String? color,
    String? specName,
    String? size,
    bool? available,
    Thumbnail? thumbnail,
    Thumbnail? rectangleImage,
    Thumbnail? squareImage,
  }) =>
      MembersPreordersRes(
        id: id ?? this.id,
        productId: productId ?? this.productId,
        productName: productName ?? this.productName,
        productPrice: productPrice ?? this.productPrice,
        promotionPrice: promotionPrice ?? this.promotionPrice,
        quantity: quantity ?? this.quantity,
        color: color ?? this.color,
        specName: specName ?? this.specName,
        size: size ?? this.size,
        available: available ?? this.available,
        thumbnail: thumbnail ?? this.thumbnail,
        rectangleImage: rectangleImage ?? this.rectangleImage,
        squareImage: squareImage ?? this.squareImage,
      );

  factory MembersPreordersRes.fromRawJson(String str) =>
      MembersPreordersRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersPreordersRes.fromJson(Map<String, dynamic> json) =>
      MembersPreordersRes(
        id: json["id"],
        productId: json["product_id"],
        productName: json["product_name"],
        productPrice: json["product_price"],
        promotionPrice: json["promotion_price"],
        quantity: json["quantity"],
        color: json["color"],
        specName: json["spec_name"],
        size: json["size"],
        available: json["available"],
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        rectangleImage: json["rectangle_image"] == null
            ? null
            : Thumbnail.fromJson(json["rectangle_image"]),
        squareImage: json["square_image"] == null
            ? null
            : Thumbnail.fromJson(json["square_image"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "product_id": productId,
        "product_name": productName,
        "product_price": productPrice,
        "promotion_price": promotionPrice,
        "quantity": quantity,
        "color": color,
        "spec_name": specName,
        "size": size,
        "available": available,
        "thumbnail": thumbnail?.toJson(),
        "rectangle_image": rectangleImage?.toJson(),
        "square_image": squareImage?.toJson(),
      };
}
