// To parse this JSON data, do
//
//     final buyNGetMDiscountRes = buyNGetMDiscountResFromJson(jsonString);

import 'dart:convert';

import 'package:efshop/app/models/product_detail.dart';

import 'seo.dart';
import 'thumbnail.dart';

class PromotionDetailRes {
  Promotion? promotion;
  List<ProductDetail>? products;
  Seo? seo;

  PromotionDetailRes({
    this.promotion,
    this.products,
    this.seo,
  });

  PromotionDetailRes copyWith({
    Promotion? promotion,
    List<ProductDetail>? products,
    Seo? seo,
  }) =>
      PromotionDetailRes(
        promotion: promotion ?? this.promotion,
        products: products ?? this.products,
        seo: seo ?? this.seo,
      );

  factory PromotionDetailRes.fromRawJson(String str) =>
      PromotionDetailRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PromotionDetailRes.fromJson(Map<String, dynamic> json) =>
      PromotionDetailRes(
        promotion: json["promotion"] == null
            ? null
            : Promotion.fromJson(json["promotion"]),
        products: json["products"] == null
            ? []
            : List<ProductDetail>.from(
                json["products"]!.map((x) => ProductDetail.fromJson(x))),
        seo: json["seo"] == null ? null : Seo.fromJson(json["seo"]),
      );

  Map<String, dynamic> toJson() => {
        "promotion": promotion?.toJson(),
        "products": products == null
            ? []
            : List<dynamic>.from(products!.map((x) => x.toJson())),
        "seo": seo?.toJson(),
      };
}

class Promotion {
  Thumbnail? thumbnail;
  String? smallTitle;
  List<Threshold>? thresholds;
  String? visible;
  Map<String, dynamic>? category;

  Promotion({
    this.thumbnail,
    this.smallTitle,
    this.thresholds,
    this.visible,
    this.category,
  });

  Promotion copyWith({
    Thumbnail? thumbnail,
    String? smallTitle,
    List<Threshold>? thresholds,
    String? visible,
    Map<String, dynamic>? category,
  }) =>
      Promotion(
        thumbnail: thumbnail ?? this.thumbnail,
        smallTitle: smallTitle ?? this.smallTitle,
        thresholds: thresholds ?? this.thresholds,
        visible: visible ?? this.visible,
        category: category ?? this.category,
      );

  factory Promotion.fromRawJson(String str) =>
      Promotion.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Promotion.fromJson(Map<String, dynamic> json) => Promotion(
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        smallTitle: json["small_title"],
        thresholds: json["thresholds"] == null
            ? []
            : List<Threshold>.from(
                json["thresholds"]!.map((x) => Threshold.fromJson(x))),
        visible: json["visible"],
        category: json["category"] is Map ? json["category"] : {},
      );

  Map<String, dynamic> toJson() => {
        "thumbnail": thumbnail?.toJson(),
        "small_title": smallTitle,
        "thresholds": thresholds == null
            ? []
            : List<dynamic>.from(thresholds!.map((x) => x.toJson())),
        "visible": visible,
        "category": category,
      };
}

class Threshold {
  String? quantity;
  String? gift;

  Threshold({
    this.quantity,
    this.gift,
  });

  Threshold copyWith({
    String? quantity,
    String? gift,
  }) =>
      Threshold(
        quantity: quantity ?? this.quantity,
        gift: gift ?? this.gift,
      );

  factory Threshold.fromRawJson(String str) =>
      Threshold.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Threshold.fromJson(Map<String, dynamic> json) => Threshold(
        quantity: json["quantity"],
        gift: json["gift"],
      );

  Map<String, dynamic> toJson() => {
        "quantity": quantity,
        "gift": gift,
      };
}
