// To parse this JSON data, do
//
//     final paymentOptionsRes = paymentOptionsResFromJson(jsonString);

import 'dart:convert';

class PaymentOptionsRes {
  String? paymentOptionsResDefault;
  Payment? payment;

  PaymentOptionsRes({
    this.paymentOptionsResDefault,
    this.payment,
  });

  PaymentOptionsRes copyWith({
    String? paymentOptionsResDefault,
    Payment? payment,
  }) =>
      PaymentOptionsRes(
        paymentOptionsResDefault:
            paymentOptionsResDefault ?? this.paymentOptionsResDefault,
        payment: payment ?? this.payment,
      );

  factory PaymentOptionsRes.fromRawJson(String str) =>
      PaymentOptionsRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PaymentOptionsRes.fromJson(Map<String, dynamic> json) =>
      PaymentOptionsRes(
        paymentOptionsResDefault: json["default"],
        payment:
            json["payment"] == null ? null : Payment.fromJson(json["payment"]),
      );

  Map<String, dynamic> toJson() => {
        "default": paymentOptionsResDefault,
        "payment": payment?.toJson(),
      };
}

class Payment {
  The7Eshop? the7Eshop;
  The7Eshop? fmeshop;
  The7Eshop? cod;
  The7Eshop? chinatrust;
  The7Eshop? hitrust;
  The7Eshop? linePay;

  Payment({
    this.the7Eshop,
    this.fmeshop,
    this.cod,
    this.chinatrust,
    this.hitrust,
    this.linePay,
  });

  Payment copyWith({
    The7Eshop? the7Eshop,
    The7Eshop? fmeshop,
    The7Eshop? cod,
    The7Eshop? chinatrust,
    The7Eshop? hitrust,
    The7Eshop? linePay,
  }) =>
      Payment(
        the7Eshop: the7Eshop ?? this.the7Eshop,
        fmeshop: fmeshop ?? this.fmeshop,
        cod: cod ?? this.cod,
        chinatrust: chinatrust ?? this.chinatrust,
        hitrust: hitrust ?? this.hitrust,
        linePay: linePay ?? this.linePay,
      );

  factory Payment.fromRawJson(String str) => Payment.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Payment.fromJson(Map<String, dynamic> json) => Payment(
        the7Eshop:
            json["7eshop"] == null ? null : The7Eshop.fromJson(json["7eshop"]),
        fmeshop: json["fmeshop"] == null
            ? null
            : The7Eshop.fromJson(json["fmeshop"]),
        cod: json["cod"] == null ? null : The7Eshop.fromJson(json["cod"]),
        chinatrust: json["chinatrust"] == null
            ? null
            : The7Eshop.fromJson(json["chinatrust"]),
        hitrust: json["hitrust"] == null
            ? null
            : The7Eshop.fromJson(json["hitrust"]),
        linePay: json["line-pay"] == null
            ? null
            : The7Eshop.fromJson(json["line-pay"]),
      );

  Map<String, dynamic> toJson() => {
        "7eshop": the7Eshop?.toJson(),
        "fmeshop": fmeshop?.toJson(),
        "cod": cod?.toJson(),
        "chinatrust": chinatrust?.toJson(),
        "hitrust": hitrust?.toJson(),
        "line-pay": linePay?.toJson(),
      };
}

class The7Eshop {
  String? description;
  bool? enable;
  String? shippingThreshold;
  String? shippingFee;

  The7Eshop({
    this.description,
    this.enable,
    this.shippingThreshold,
    this.shippingFee,
  });

  The7Eshop copyWith({
    String? description,
    bool? enable,
    String? shippingThreshold,
    String? shippingFee,
  }) =>
      The7Eshop(
        description: description ?? this.description,
        enable: enable ?? this.enable,
        shippingThreshold: shippingThreshold ?? this.shippingThreshold,
        shippingFee: shippingFee ?? this.shippingFee,
      );

  factory The7Eshop.fromRawJson(String str) =>
      The7Eshop.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory The7Eshop.fromJson(Map<String, dynamic> json) => The7Eshop(
        description: json["description"],
        enable: json["enable"],
        shippingThreshold: json["shipping_threshold"],
        shippingFee: json["shipping_fee"],
      );

  Map<String, dynamic> toJson() => {
        "description": description,
        "enable": enable,
        "shipping_threshold": shippingThreshold,
        "shipping_fee": shippingFee,
      };
}
