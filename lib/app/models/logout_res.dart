// To parse this JSON data, do
//
//     final logoutRes = logoutResFromJson(jsonString);

import 'dart:convert';

class LogoutRes {
  bool? status;
  String? message;
  SessionToken? sessionToken;

  LogoutRes({
    this.status,
    this.message,
    this.sessionToken,
  });

  LogoutRes copyWith({
    bool? status,
    String? message,
    SessionToken? sessionToken,
  }) =>
      LogoutRes(
        status: status ?? this.status,
        message: message ?? this.message,
        sessionToken: sessionToken ?? this.sessionToken,
      );

  factory LogoutRes.fromRawJson(String str) =>
      LogoutRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LogoutRes.fromJson(Map<String, dynamic> json) => LogoutRes(
        status: json["status"],
        message: json["message"],
        sessionToken: json["session_token"] == null
            ? null
            : SessionToken.fromJson(json["session_token"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "session_token": sessionToken?.toJson(),
      };
}

class SessionToken {
  Payload? payload;
  String? token;

  SessionToken({
    this.payload,
    this.token,
  });

  SessionToken copyWith({
    Payload? payload,
    String? token,
  }) =>
      SessionToken(
        payload: payload ?? this.payload,
        token: token ?? this.token,
      );

  factory SessionToken.fromRawJson(String str) =>
      SessionToken.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SessionToken.fromJson(Map<String, dynamic> json) => SessionToken(
        payload:
            json["payload"] == null ? null : Payload.fromJson(json["payload"]),
        token: json["token"],
      );

  Map<String, dynamic> toJson() => {
        "payload": payload?.toJson(),
        "token": token,
      };
}

class Payload {
  int? iat;
  int? exp;
  String? jti;
  String? payloadFor;
  String? session;

  Payload({
    this.iat,
    this.exp,
    this.jti,
    this.payloadFor,
    this.session,
  });

  Payload copyWith({
    int? iat,
    int? exp,
    String? jti,
    String? payloadFor,
    String? session,
  }) =>
      Payload(
        iat: iat ?? this.iat,
        exp: exp ?? this.exp,
        jti: jti ?? this.jti,
        payloadFor: payloadFor ?? this.payloadFor,
        session: session ?? this.session,
      );

  factory Payload.fromRawJson(String str) => Payload.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Payload.fromJson(Map<String, dynamic> json) => Payload(
        iat: json["iat"],
        exp: json["exp"],
        jti: json["jti"],
        payloadFor: json["for"],
        session: json["session"],
      );

  Map<String, dynamic> toJson() => {
        "iat": iat,
        "exp": exp,
        "jti": jti,
        "for": payloadFor,
        "session": session,
      };
}
