// To parse this JSON data, do
//
//     final membersMyFavoriteRes = membersMyFavoriteResFromJson(jsonString);

import 'dart:convert';

import 'thumbnail.dart';

class MembersMyFavoriteRes {
  int? count;
  Map<String, List<MembersMyFavorite>>? items;

  MembersMyFavoriteRes({
    this.count,
    this.items,
  });

  MembersMyFavoriteRes copyWith({
    int? count,
    Map<String, List<MembersMyFavorite>>? items,
  }) =>
      MembersMyFavoriteRes(
        count: count ?? this.count,
        items: items ?? this.items,
      );

  factory MembersMyFavoriteRes.fromRawJson(String str) =>
      MembersMyFavoriteRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMyFavoriteRes.fromJson(Map<String, dynamic> json) =>
      MembersMyFavoriteRes(
        count: json["count"],
        items: json["items"] == null || json["items"] is List
            ? <String, List<MembersMyFavorite>>{}
            : Map<String, List>.from(json["items"]!).map((key, value) {
                final newValue = value
                    .map((e) => MembersMyFavorite.fromJson(e))
                    .toList(growable: false);
                return MapEntry(key, newValue);
              }),
      );

  Map<String, dynamic> toJson() => {
        "count": count,
        "items": items == null
            ? <String, Map>{}
            : Map.from(items!.map((key, value) {
                final newValue = value.map((e) => e.toJson()).toList();
                return MapEntry(key, newValue);
              })),
      };
}

class MembersMyFavorite {
  String? parentId;
  String? id;
  String? number;
  String? name;
  String? color;
  String? size;
  String? quantity;
  String? maxQuantity;
  String? safeQuantity;
  String? available;
  int? price;
  int? promotionPrice;
  String? shareUrl;
  Thumbnail? thumbnail;
  Thumbnail? rectangleImage;
  Thumbnail? squareImage;

  MembersMyFavorite({
    this.parentId,
    this.id,
    this.number,
    this.name,
    this.color,
    this.size,
    this.quantity,
    this.maxQuantity,
    this.safeQuantity,
    this.available,
    this.price,
    this.promotionPrice,
    this.shareUrl,
    this.thumbnail,
    this.rectangleImage,
    this.squareImage,
  });

  MembersMyFavorite copyWith({
    String? parentId,
    String? id,
    String? number,
    String? name,
    String? color,
    String? size,
    String? quantity,
    String? maxQuantity,
    String? safeQuantity,
    String? available,
    int? price,
    int? promotionPrice,
    String? shareUrl,
    Thumbnail? thumbnail,
    Thumbnail? rectangleImage,
    Thumbnail? squareImage,
  }) =>
      MembersMyFavorite(
        parentId: parentId ?? this.parentId,
        id: id ?? this.id,
        number: number ?? this.number,
        name: name ?? this.name,
        color: color ?? this.color,
        size: size ?? this.size,
        quantity: quantity ?? this.quantity,
        maxQuantity: maxQuantity ?? this.maxQuantity,
        safeQuantity: safeQuantity ?? this.safeQuantity,
        available: available ?? this.available,
        price: price ?? this.price,
        promotionPrice: promotionPrice ?? this.promotionPrice,
        shareUrl: shareUrl ?? this.shareUrl,
        thumbnail: thumbnail ?? this.thumbnail,
        rectangleImage: rectangleImage ?? this.rectangleImage,
        squareImage: squareImage ?? this.squareImage,
      );

  factory MembersMyFavorite.fromRawJson(String str) =>
      MembersMyFavorite.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMyFavorite.fromJson(Map<String, dynamic> json) =>
      MembersMyFavorite(
        parentId: json["parent_id"],
        id: '${json["id"]}',
        number: json["number"],
        name: json["name"],
        color: json["color"],
        size: json["size"],
        quantity: '${json["quantity"]}',
        maxQuantity: '${json["max_quantity"]}',
        safeQuantity: '${json["safe_quantity"]}',
        available: '${json["available"]}',
        price: int.tryParse('${json["price"]}'),
        promotionPrice: json["promotion_price"],
        shareUrl: json["share_url"],
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        rectangleImage: json["rectangle_image"] == null
            ? null
            : Thumbnail.fromJson(json["rectangle_image"]),
        squareImage: json["square_image"] == null
            ? null
            : Thumbnail.fromJson(json["square_image"]),
      );

  Map<String, dynamic> toJson() => {
        "parent_id": parentId,
        "id": id,
        "number": number,
        "name": name,
        "color": color,
        "size": size,
        "quantity": quantity,
        "max_quantity": maxQuantity,
        "safe_quantity": safeQuantity,
        "available": available,
        "price": price,
        "promotion_price": promotionPrice,
        "share_url": shareUrl,
        "thumbnail": thumbnail?.toJson(),
        "rectangle_image": rectangleImage?.toJson(),
        "square_image": squareImage?.toJson(),
      };
}
