// To parse this JSON data, do
//
//     final membersOrdersComment = membersOrdersCommentFromJson(jsonString);

import 'dart:convert';

import 'thumbnail.dart';
import 'url.dart';

class MembersOrdersComment {
  String? orderId;
  String? orderNumber;
  String? productId;
  String? productName;
  String? productSpecName;
  String? userComment;
  String? userCommentDatetime;
  Url? url;
  Thumbnail? thumbnail;
  Thumbnail? rectangleImage;
  Thumbnail? squareImage;
  String? shareUrl;

  MembersOrdersComment({
    this.orderId,
    this.orderNumber,
    this.productId,
    this.productName,
    this.productSpecName,
    this.userComment,
    this.userCommentDatetime,
    this.url,
    this.thumbnail,
    this.rectangleImage,
    this.squareImage,
    this.shareUrl,
  });

  MembersOrdersComment copyWith({
    String? orderId,
    String? orderNumber,
    String? productId,
    String? productName,
    String? productSpecName,
    String? userComment,
    String? userCommentDatetime,
    Url? url,
    Thumbnail? thumbnail,
    Thumbnail? rectangleImage,
    Thumbnail? squareImage,
    String? shareUrl,
  }) =>
      MembersOrdersComment(
        orderId: orderId ?? this.orderId,
        orderNumber: orderNumber ?? this.orderNumber,
        productId: productId ?? this.productId,
        productName: productName ?? this.productName,
        productSpecName: productSpecName ?? this.productSpecName,
        userComment: userComment ?? this.userComment,
        userCommentDatetime: userCommentDatetime ?? this.userCommentDatetime,
        url: url ?? this.url,
        thumbnail: thumbnail ?? this.thumbnail,
        rectangleImage: rectangleImage ?? this.rectangleImage,
        squareImage: squareImage ?? this.squareImage,
        shareUrl: shareUrl ?? this.shareUrl,
      );

  factory MembersOrdersComment.fromRawJson(String str) =>
      MembersOrdersComment.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersOrdersComment.fromJson(Map<String, dynamic> json) =>
      MembersOrdersComment(
        orderId: json["order_id"],
        orderNumber: json["order_number"],
        productId: json["product_id"],
        productName: json["product_name"],
        productSpecName: json["product_spec_name"],
        userComment: json["user_comment"],
        userCommentDatetime: json["user_comment_datetime"],
        url: json["url"] is Map ? Url.fromJson(json["url"]) : null,
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        rectangleImage: json["rectangle_image"] == null
            ? null
            : Thumbnail.fromJson(json["rectangle_image"]),
        squareImage: json["square_image"] == null
            ? null
            : Thumbnail.fromJson(json["square_image"]),
        shareUrl: json["share_url"],
      );

  Map<String, dynamic> toJson() => {
        "order_id": orderId,
        "order_number": orderNumber,
        "product_id": productId,
        "product_name": productName,
        "product_spec_name": productSpecName,
        "user_comment": userComment,
        "user_comment_datetime": userCommentDatetime,
        "url": url?.toJson(),
        "thumbnail": thumbnail?.toJson(),
        "rectangle_image": rectangleImage?.toJson(),
        "square_image": squareImage?.toJson(),
        "share_url": shareUrl,
      };
}
