// To parse this JSON data, do
//
//     final categoriesIdReq = categoriesIdReqFromJson(jsonString);

import 'dart:convert';

class CategoriesIdReq {
  int? targetPage;
  String? sortWay;
  String? sortField;

  CategoriesIdReq({
    this.targetPage,
    this.sortWay,
    this.sortField,
  });

  CategoriesIdReq copyWith({
    int? targetPage,
    String? sortWay,
    String? sortField,
  }) =>
      CategoriesIdReq(
        targetPage: targetPage ?? this.targetPage,
        sortWay: sortWay ?? this.sortWay,
        sortField: sortField ?? this.sortField,
      );

  factory CategoriesIdReq.fromRawJson(String str) =>
      CategoriesIdReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CategoriesIdReq.fromJson(Map<String, dynamic> json) =>
      CategoriesIdReq(
        targetPage: json["target_page"],
        sortWay: json["sort_way"],
        sortField: json["sort_field"],
      );

  Map<String, dynamic> toJson() => {
        "target_page": targetPage,
        "sort_way": sortWay,
        "sort_field": sortField,
      };
}
