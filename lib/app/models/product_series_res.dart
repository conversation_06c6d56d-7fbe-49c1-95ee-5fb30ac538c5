// To parse this JSON data, do
//
//     final productSeriesRes = productSeriesResFromJson(jsonString);

import 'dart:convert';

import 'thumbnail.dart';

class ProductSeriesRes {
  int? id;
  String? number;
  String? specName;
  String? color;
  String? size;
  int? quantity;
  int? available;
  int? maxQuantity;
  String? colorImage;
  String? mainImage;
  int? promotionPrice;
  String? promotionType;
  int? price;
  Thumbnail? squareThumbnail;
  String? shareUrl;
  String? videoUrl;
  int? isShowVideoOnMobile;
  int? isShowVideoOnApp;

  ProductSeriesRes({
    this.id,
    this.number,
    this.specName,
    this.color,
    this.size,
    this.quantity,
    this.available,
    this.maxQuantity,
    this.colorImage,
    this.mainImage,
    this.promotionPrice,
    this.promotionType,
    this.price,
    this.squareThumbnail,
    this.shareUrl,
    this.videoUrl,
    this.isShowVideoOnMobile,
    this.isShowVideoOnApp,
  });

  ProductSeriesRes copyWith({
    int? id,
    String? number,
    String? specName,
    String? color,
    String? size,
    int? quantity,
    int? available,
    int? maxQuantity,
    String? colorImage,
    String? mainImage,
    int? promotionPrice,
    String? promotionType,
    int? price,
    Thumbnail? squareThumbnail,
    String? shareUrl,
    String? videoUrl,
    int? isShowVideoOnMobile,
    int? isShowVideoOnApp,
  }) =>
      ProductSeriesRes(
        id: id ?? this.id,
        number: number ?? this.number,
        specName: specName ?? this.specName,
        color: color ?? this.color,
        size: size ?? this.size,
        quantity: quantity ?? this.quantity,
        available: available ?? this.available,
        maxQuantity: maxQuantity ?? this.maxQuantity,
        colorImage: colorImage ?? this.colorImage,
        mainImage: mainImage ?? this.mainImage,
        promotionPrice: promotionPrice ?? this.promotionPrice,
        promotionType: promotionType ?? this.promotionType,
        price: price ?? this.price,
        squareThumbnail: squareThumbnail ?? this.squareThumbnail,
        shareUrl: shareUrl ?? this.shareUrl,
        videoUrl: videoUrl ?? this.videoUrl,
        isShowVideoOnMobile: isShowVideoOnMobile ?? this.isShowVideoOnMobile,
        isShowVideoOnApp: isShowVideoOnApp ?? this.isShowVideoOnApp,
      );

  factory ProductSeriesRes.fromRawJson(String str) =>
      ProductSeriesRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductSeriesRes.fromJson(Map<String, dynamic> json) =>
      ProductSeriesRes(
        id: json["id"],
        number: json["number"],
        specName: json["spec_name"],
        color: json["color"],
        size: json["size"],
        quantity: json["quantity"],
        available: json["available"],
        maxQuantity: json["max_quantity"],
        colorImage: json["color_image"],
        mainImage: json["main_image"],
        promotionPrice: json["promotion_price"],
        promotionType: json["promotion_type"],
        price: json["price"],
        squareThumbnail: json["square_thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["square_thumbnail"]),
        shareUrl: json["share_url"],
        videoUrl: json["video_url"],
        isShowVideoOnMobile: json["is_show_video_on_mobile"],
        isShowVideoOnApp: json["is_show_video_on_app"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "number": number,
        "spec_name": specName,
        "color": color,
        "size": size,
        "quantity": quantity,
        "available": available,
        "max_quantity": maxQuantity,
        "color_image": colorImage,
        "main_image": mainImage,
        "promotion_price": promotionPrice,
        "promotion_type": promotionType,
        "price": price,
        "square_thumbnail": squareThumbnail?.toJson(),
        "share_url": shareUrl,
        "video_url": videoUrl,
        "is_show_video_on_mobile": isShowVideoOnMobile,
        "is_show_video_on_app": isShowVideoOnApp,
      };
}
