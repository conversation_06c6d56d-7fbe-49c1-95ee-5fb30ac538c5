import 'dart:convert';

class AppierAuthCodeReq {
  String? crmId;
  String? pageId;
  String? crmName;

  AppierAuthCodeReq({
    this.crmId,
    this.pageId,
    this.crmName,
  });

  AppierAuthCodeReq copyWith({
    String? crmId,
    String? pageId,
    String? crmName,
  }) =>
      AppierAuthCodeReq(
        crmId: crmId ?? this.crmId,
        pageId: pageId ?? this.pageId,
        crmName: crmName ?? this.crmName,
      );

  factory AppierAuthCodeReq.fromRawJson(String str) =>
      AppierAuthCodeReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppierAuthCodeReq.fromJson(Map<String, dynamic> json) =>
      AppierAuthCodeReq(
        crmId: json["crmId"],
        pageId: json["pageId"],
        crmName: json["crmName"],
      );

  Map<String, dynamic> toJson() => {
        "crmId": crmId,
        "pageId": pageId,
        "crmName": crmName,
      };
}
