// To parse this JSON data, do
//
//     final loginReq = loginReqFromJson(jsonString);

import 'dart:convert';

class LoginReq {
  String? email;
  String? password;

  LoginReq({
    this.email,
    this.password,
  });

  LoginReq copyWith({
    String? email,
    String? password,
  }) =>
      LoginReq(
        email: email ?? this.email,
        password: password ?? this.password,
      );

  factory LoginReq.fromRawJson(String str) =>
      LoginReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LoginReq.fromJson(Map<String, dynamic> json) => LoginReq(
        email: json["email"],
        password: json["password"],
      );

  Map<String, dynamic> toJson() => {
        "email": email,
        "password": password,
      };
}
