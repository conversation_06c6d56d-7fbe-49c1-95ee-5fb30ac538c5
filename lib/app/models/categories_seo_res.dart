// To parse this JSON data, do
//
//     final categoriesSeoRes = categoriesSeoResFromJson(jsonString);

import 'dart:convert';

import 'seo.dart';

class CategoriesSeoRes {
  Seo? seo;
  Adv? adv;

  CategoriesSeoRes({
    this.seo,
    this.adv,
  });

  CategoriesSeoRes copyWith({
    Seo? seo,
    Adv? adv,
  }) =>
      CategoriesSeoRes(
        seo: seo ?? this.seo,
        adv: adv ?? this.adv,
      );

  factory CategoriesSeoRes.fromRawJson(String str) =>
      CategoriesSeoRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CategoriesSeoRes.fromJson(Map<String, dynamic> json) =>
      CategoriesSeoRes(
        seo: json["seo"] == null ? null : Seo.fromJson(json["seo"]),
        adv: json["adv"] == null ? null : Adv.fromJson(json["adv"]),
      );

  Map<String, dynamic> toJson() => {
        "seo": seo?.toJson(),
        "adv": adv?.toJson(),
      };
}

class Adv {
  String? name;
  String? image;
  String? desktopHtml;
  String? desktopHtmlEnable;
  String? mobileHtml;
  String? mobileHtmlEnable;

  Adv({
    this.name,
    this.image,
    this.desktopHtml,
    this.desktopHtmlEnable,
    this.mobileHtml,
    this.mobileHtmlEnable,
  });

  Adv copyWith({
    String? name,
    String? image,
    String? desktopHtml,
    String? desktopHtmlEnable,
    String? mobileHtml,
    String? mobileHtmlEnable,
  }) =>
      Adv(
        name: name ?? this.name,
        image: image ?? this.image,
        desktopHtml: desktopHtml ?? this.desktopHtml,
        desktopHtmlEnable: desktopHtmlEnable ?? this.desktopHtmlEnable,
        mobileHtml: mobileHtml ?? this.mobileHtml,
        mobileHtmlEnable: mobileHtmlEnable ?? this.mobileHtmlEnable,
      );

  factory Adv.fromRawJson(String str) => Adv.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Adv.fromJson(Map<String, dynamic> json) => Adv(
        name: json["name"],
        image: json["image"],
        desktopHtml: json["desktop_html"],
        desktopHtmlEnable: json["desktop_html_enable"],
        mobileHtml: json["mobile_html"],
        mobileHtmlEnable: json["mobile_html_enable"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "image": image,
        "desktop_html": desktopHtml,
        "desktop_html_enable": desktopHtmlEnable,
        "mobile_html": mobileHtml,
        "mobile_html_enable": mobileHtmlEnable,
      };
}
