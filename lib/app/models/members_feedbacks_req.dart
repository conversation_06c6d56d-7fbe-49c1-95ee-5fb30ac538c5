// To parse this JSON data, do
//
//     final membersFeedbacksReq = membersFeedbacksReqFromJson(jsonString);

import 'dart:convert';

class MembersFeedbacksReq {
  String? feedback;
  String? telephone;

  MembersFeedbacksReq({
    this.feedback,
    this.telephone,
  });

  MembersFeedbacksReq copyWith({
    String? feedback,
    String? telephone,
  }) =>
      MembersFeedbacksReq(
        feedback: feedback ?? this.feedback,
        telephone: telephone ?? this.telephone,
      );

  factory MembersFeedbacksReq.fromRawJson(String str) =>
      MembersFeedbacksReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersFeedbacksReq.fromJson(Map<String, dynamic> json) =>
      MembersFeedbacksReq(
        feedback: json["feedback"],
        telephone: json["telephone"],
      );

  Map<String, dynamic> toJson() => {
        "feedback": feedback,
        "telephone": telephone,
      };
}
