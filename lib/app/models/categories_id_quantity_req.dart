// To parse this JSON data, do
//
//     final categoriesIdQuantityReq = categoriesIdQuantityReqFromJson(jsonString);

import 'dart:convert';

class CategoriesIdQuantityReq {
  int? targetPage;
  String? sortWay;
  String? sortField;

  CategoriesIdQuantityReq({
    this.targetPage,
    this.sortWay,
    this.sortField,
  });

  CategoriesIdQuantityReq copyWith({
    int? targetPage,
    String? sortWay,
    String? sortField,
  }) =>
      CategoriesIdQuantityReq(
        targetPage: targetPage ?? this.targetPage,
        sortWay: sortWay ?? this.sortWay,
        sortField: sortField ?? this.sortField,
      );

  factory CategoriesIdQuantityReq.fromRawJson(String str) =>
      CategoriesIdQuantityReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CategoriesIdQuantityReq.fromJson(Map<String, dynamic> json) =>
      CategoriesIdQuantityReq(
        targetPage: json["target_page"],
        sortWay: json["sort_way"],
        sortField: json["sort_field"],
      );

  Map<String, dynamic> toJson() => {
        "target_page": targetPage,
        "sort_way": sortWay,
        "sort_field": sortField,
      };
}
