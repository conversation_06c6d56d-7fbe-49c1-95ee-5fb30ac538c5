import 'dart:convert';

class MembersOrdersRefundProduct {
  String? productId;
  String? promotionId;
  String? quantity;
  String? reasonId;

  MembersOrdersRefundProduct({
    this.productId,
    this.promotionId,
    this.quantity,
    this.reasonId,
  });

  MembersOrdersRefundProduct copyWith({
    String? productId,
    String? promotionId,
    String? quantity,
    String? reasonId,
  }) =>
      MembersOrdersRefundProduct(
        productId: productId ?? this.productId,
        promotionId: promotionId ?? this.promotionId,
        quantity: quantity ?? this.quantity,
        reasonId: reasonId ?? this.reasonId,
      );

  factory MembersOrdersRefundProduct.fromRawJson(String str) =>
      MembersOrdersRefundProduct.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersOrdersRefundProduct.fromJson(Map<String, dynamic> json) =>
      MembersOrdersRefundProduct(
        productId: json["product_id"],
        promotionId: json["promotion_id"],
        quantity: json["quantity"],
        reasonId: json["reason_id"],
      );

  Map<String, dynamic> toJson() => {
        "product_id": productId,
        "promotion_id": promotionId,
        "quantity": quantity,
        "reason_id": reasonId,
      };
}
