import 'dart:convert';

class AppierTypeViewedReq {
  String? typeName;

  AppierTypeViewedReq({
    this.typeName,
  });

  AppierTypeViewedReq copyWith({
    String? typeName,
  }) =>
      AppierTypeViewedReq(
        typeName: typeName ?? this.typeName,
      );

  factory AppierTypeViewedReq.fromRawJson(String str) =>
      AppierTypeViewedReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppierTypeViewedReq.fromJson(Map<String, dynamic> json) =>
      AppierTypeViewedReq(
        typeName: json["type_name"],
      );

  Map<String, dynamic> toJson() => {
        "type_name": typeName,
      };
}
