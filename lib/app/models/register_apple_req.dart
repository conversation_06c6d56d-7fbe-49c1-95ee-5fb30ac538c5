// To parse this JSON data, do
//
//     final registerAppleReq = registerAppleReqFromJson(jsonString);

import 'dart:convert';

class RegisterAppleReq {
  String? email;
  String? password;
  String? fullname;
  String? uid;

  RegisterAppleReq({
    this.email,
    this.password,
    this.fullname,
    this.uid,
  });

  RegisterAppleReq copyWith({
    String? email,
    String? password,
    String? fullname,
    String? uid,
  }) =>
      RegisterAppleReq(
        email: email ?? this.email,
        password: password ?? this.password,
        fullname: fullname ?? this.fullname,
        uid: uid ?? this.uid,
      );

  factory RegisterAppleReq.fromRawJson(String str) =>
      RegisterAppleReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RegisterAppleReq.fromJson(Map<String, dynamic> json) =>
      RegisterAppleReq(
        email: json["email"],
        password: json["password"],
        fullname: json["fullname"],
        uid: json["uid"],
      );

  Map<String, dynamic> toJson() => {
        "email": email,
        "password": password,
        "fullname": fullname,
        "uid": uid,
      };
}
