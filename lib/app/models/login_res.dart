import 'dart:convert';

class LoginRes {
  Payload? payload;
  String? token;

  LoginRes({
    this.payload,
    this.token,
  });

  LoginRes copyWith({
    Payload? payload,
    String? token,
  }) =>
      LoginRes(
        payload: payload ?? this.payload,
        token: token ?? this.token,
      );

  factory LoginRes.fromRawJson(String str) =>
      LoginRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LoginRes.fromJson(Map<String, dynamic> json) => LoginRes(
        payload:
            json["payload"] == null ? null : Payload.fromJson(json["payload"]),
        token: json["token"],
      );

  Map<String, dynamic> toJson() => {
        "payload": payload?.toJson(),
        "token": token,
      };
}

class Payload {
  int? iat;
  int? exp;
  String? jti;
  String? payloadFor;
  String? session;
  String? userid;
  String? email;
  String? fullname;
  String? birthday;
  String? gender;
  int? bonus;
  String? provider;

  Payload({
    this.iat,
    this.exp,
    this.jti,
    this.payloadFor,
    this.session,
    this.userid,
    this.email,
    this.fullname,
    this.birthday,
    this.gender,
    this.bonus,
    this.provider,
  });

  Payload copyWith({
    int? iat,
    int? exp,
    String? jti,
    String? payloadFor,
    String? session,
    String? userid,
    String? email,
    String? fullname,
    String? birthday,
    String? gender,
    int? bonus,
    String? provider,
  }) =>
      Payload(
        iat: iat ?? this.iat,
        exp: exp ?? this.exp,
        jti: jti ?? this.jti,
        payloadFor: payloadFor ?? this.payloadFor,
        session: session ?? this.session,
        userid: userid ?? this.userid,
        email: email ?? this.email,
        fullname: fullname ?? this.fullname,
        birthday: birthday ?? this.birthday,
        gender: gender ?? this.gender,
        bonus: bonus ?? this.bonus,
        provider: provider ?? this.provider,
      );

  factory Payload.fromRawJson(String str) => Payload.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Payload.fromJson(Map<String, dynamic> json) => Payload(
        iat: json["iat"],
        exp: json["exp"],
        jti: json["jti"],
        payloadFor: json["for"],
        session: json["session"],
        userid: json["userid"],
        email: json["email"],
        fullname: json["fullname"],
        birthday: json["birthday"],
        gender: json["gender"],
        bonus: json["bonus"],
        provider: json["provider"],
      );

  Map<String, dynamic> toJson() => {
        "iat": iat,
        "exp": exp,
        "jti": jti,
        "for": payloadFor,
        "session": session,
        "userid": userid,
        "email": email,
        "fullname": fullname,
        "birthday": birthday,
        "gender": gender,
        "bonus": bonus,
        "provider": provider,
      };
}
