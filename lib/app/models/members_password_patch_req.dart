// To parse this JSON data, do
//
//     final membersPasswordPatchReq = membersPasswordPatchReqFromJson(jsonString);

import 'dart:convert';

class MembersPasswordPatchReq {
  String? password;
  String? newPassword;

  MembersPasswordPatchReq({
    this.password,
    this.newPassword,
  });

  MembersPasswordPatchReq copyWith({
    String? password,
    String? newPassword,
  }) =>
      MembersPasswordPatchReq(
        password: password ?? this.password,
        newPassword: newPassword ?? this.newPassword,
      );

  factory MembersPasswordPatchReq.fromRawJson(String str) =>
      MembersPasswordPatchReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersPasswordPatchReq.fromJson(Map<String, dynamic> json) =>
      MembersPasswordPatchReq(
        password: json["password"],
        newPassword: json["new_password"],
      );

  Map<String, dynamic> toJson() => {
        "password": password,
        "new_password": newPassword,
      };
}

extension MembersPasswordPatchReqX on MembersPasswordPatchReq {
  void validate() {
    if (password == null || password!.isEmpty) {
      throw '舊密碼是必填欄位';
    }
    if (newPassword == null || newPassword!.isEmpty) {
      throw '新密碼是必填欄位';
    }
  }
}
