// To parse this JSON data, do
//
//     final productsPreorderPostReq = productsPreorderPostReqFromJson(jsonString);

import 'dart:convert';

class ProductsPreorderPostReq {
  String? productId;
  String? email;

  ProductsPreorderPostReq({
    this.productId,
    this.email,
  });

  ProductsPreorderPostReq copyWith({
    String? productId,
    String? email,
  }) =>
      ProductsPreorderPostReq(
        productId: productId ?? this.productId,
        email: email ?? this.email,
      );

  factory ProductsPreorderPostReq.fromRawJson(String str) =>
      ProductsPreorderPostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductsPreorderPostReq.fromJson(Map<String, dynamic> json) =>
      ProductsPreorderPostReq(
        productId: json["product_id"],
        email: json["email"],
      );

  Map<String, dynamic> toJson() => {
        "product_id": productId,
        "email": email,
      };
}
