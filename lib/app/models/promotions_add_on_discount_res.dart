// To parse this JSON data, do
//
//     final promotionsAddOnDiscountRes = promotionsAddOnDiscountResFromJson(jsonString);

import 'dart:convert';

import 'product_detail.dart';

class PromotionsAddOnDiscountRes {
  String? totalPrice;
  String? mobileBannerUrl;
  String? description;
  List<PromotionsAddOnDiscountResCategory>? categories;

  PromotionsAddOnDiscountRes({
    this.totalPrice,
    this.mobileBannerUrl,
    this.description,
    this.categories,
  });

  PromotionsAddOnDiscountRes copyWith({
    String? totalPrice,
    String? mobileBannerUrl,
    String? description,
    List<PromotionsAddOnDiscountResCategory>? categories,
  }) =>
      PromotionsAddOnDiscountRes(
        totalPrice: totalPrice ?? this.totalPrice,
        mobileBannerUrl: mobileBannerUrl ?? this.mobileBannerUrl,
        description: description ?? this.description,
        categories: categories ?? this.categories,
      );

  factory PromotionsAddOnDiscountRes.fromRawJson(String str) =>
      PromotionsAddOnDiscountRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PromotionsAddOnDiscountRes.fromJson(Map<String, dynamic> json) =>
      PromotionsAddOnDiscountRes(
        totalPrice: json["total_price"],
        mobileBannerUrl: json["mobile_banner_url"],
        description: json["description"],
        categories: json["categories"] == null
            ? []
            : List<PromotionsAddOnDiscountResCategory>.from(json["categories"]!
                .map((x) => PromotionsAddOnDiscountResCategory.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total_price": totalPrice,
        "mobile_banner_url": mobileBannerUrl,
        "description": description,
        "categories": categories == null
            ? []
            : List<dynamic>.from(categories!.map((x) => x.toJson())),
      };
}

class PromotionsAddOnDiscountResCategory {
  String? name;
  List<ProductDetail>? products;

  PromotionsAddOnDiscountResCategory({
    this.name,
    this.products,
  });

  PromotionsAddOnDiscountResCategory copyWith({
    String? name,
    List<ProductDetail>? products,
  }) =>
      PromotionsAddOnDiscountResCategory(
        name: name ?? this.name,
        products: products ?? this.products,
      );

  factory PromotionsAddOnDiscountResCategory.fromRawJson(String str) =>
      PromotionsAddOnDiscountResCategory.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PromotionsAddOnDiscountResCategory.fromJson(
          Map<String, dynamic> json) =>
      PromotionsAddOnDiscountResCategory(
        name: json["name"],
        products: json["products"] == null
            ? []
            : List<ProductDetail>.from(
                json["products"]!.map((x) => ProductDetail.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "products": products == null
            ? []
            : List<dynamic>.from(products!.map((x) => x.toJson())),
      };
}
