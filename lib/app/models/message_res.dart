// To parse this JSON data, do
//
//     final messageRes = messageResFromJson(jsonString);

import 'dart:convert';

class MessageRes {
  bool? status;
  String? message;

  MessageRes({
    this.status,
    this.message,
  });

  MessageRes copyWith({
    bool? status,
    String? message,
  }) =>
      MessageRes(
        status: status ?? this.status,
        message: message ?? this.message,
      );

  factory MessageRes.fromRawJson(String str) =>
      MessageRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MessageRes.fromJson(Map<String, dynamic> json) => MessageRes(
        status: json["status"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
      };

  @override
  String toString() => message ?? '';
}
