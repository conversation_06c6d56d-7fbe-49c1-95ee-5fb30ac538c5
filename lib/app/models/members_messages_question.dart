import 'dart:convert';

class MembersMessagesQuestion {
  String? orderId;
  String? title;
  String? message;
  String? createDatetime;

  MembersMessagesQuestion({
    this.orderId,
    this.title,
    this.message,
    this.createDatetime,
  });

  MembersMessagesQuestion copyWith({
    String? orderId,
    String? title,
    String? message,
    String? createDatetime,
  }) =>
      MembersMessagesQuestion(
        orderId: orderId ?? this.orderId,
        title: title ?? this.title,
        message: message ?? this.message,
        createDatetime: createDatetime ?? this.createDatetime,
      );

  factory MembersMessagesQuestion.fromRawJson(String str) =>
      MembersMessagesQuestion.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMessagesQuestion.fromJson(Map<String, dynamic> json) =>
      MembersMessagesQuestion(
        orderId: json["order_id"],
        title: json["title"],
        message: json["message"],
        createDatetime: json["create_datetime"],
      );

  Map<String, dynamic> toJson() => {
        "order_id": orderId,
        "title": title,
        "message": message,
        "create_datetime": createDatetime,
      };
}
