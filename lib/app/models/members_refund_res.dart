// To parse this JSON data, do
//
//     final membersRefundRes = membersRefundResFromJson(jsonString);

import 'dart:convert';

class MembersRefundRes {
  String? id;
  String? bankBranch;
  String? bankCode;
  String? accountName;
  String? accountNumber;
  bool? isDefault;

  MembersRefundRes({
    this.id,
    this.bankBranch,
    this.bankCode,
    this.accountName,
    this.accountNumber,
    this.isDefault,
  });

  MembersRefundRes copyWith({
    String? id,
    String? bankBranch,
    String? bankCode,
    String? accountName,
    String? accountNumber,
    bool? isDefault,
  }) =>
      MembersRefundRes(
        id: id ?? this.id,
        bankBranch: bankBranch ?? this.bankBranch,
        bankCode: bankCode ?? this.bankCode,
        accountName: accountName ?? this.accountName,
        accountNumber: accountNumber ?? this.accountNumber,
        isDefault: isDefault ?? this.isDefault,
      );

  factory MembersRefundRes.fromRawJson(String str) =>
      MembersRefundRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersRefundRes.fromJson(Map<String, dynamic> json) =>
      MembersRefundRes(
        id: json["id"],
        bankBranch: json["bank_branch"],
        bankCode: json["bank_code"],
        accountName: json["account_name"],
        accountNumber: json["account_number"],
        isDefault: json["is_default"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "bank_branch": bankBranch,
        "bank_code": bankCode,
        "account_name": accountName,
        "account_number": accountNumber,
        "is_default": isDefault,
      };
}
