import 'dart:convert';

class AppierUserLoginReq {
  String? email;
  String? phoneNo;
  String? userId;
  String? lineChannelId;
  String? lineUid;
  num? points;
  String? userName;
  String? gender;
  String? birthday;
  num? profileUpdateDate;

  AppierUserLoginReq({
    this.email,
    this.phoneNo,
    this.userId,
    this.lineChannelId,
    this.lineUid,
    this.points,
    this.userName,
    this.gender,
    this.birthday,
    this.profileUpdateDate,
  });

  AppierUserLoginReq copyWith({
    String? email,
    String? phoneNo,
    String? userId,
    String? lineChannelId,
    String? lineUid,
    num? points,
    String? userName,
    String? gender,
    String? birthday,
    num? profileUpdateDate,
  }) =>
      AppierUserLoginReq(
        email: email ?? this.email,
        phoneNo: phoneNo ?? this.phoneNo,
        userId: userId ?? this.userId,
        lineChannelId: lineChannelId ?? this.lineChannelId,
        lineUid: lineUid ?? this.lineUid,
        points: points ?? this.points,
        userName: userName ?? this.userName,
        gender: gender ?? this.gender,
        birthday: birthday ?? this.birthday,
        profileUpdateDate: profileUpdateDate ?? this.profileUpdateDate,
      );

  factory AppierUserLoginReq.fromRawJson(String str) =>
      AppierUserLoginReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppierUserLoginReq.fromJson(Map<String, dynamic> json) =>
      AppierUserLoginReq(
        email: json["email"],
        phoneNo: json["phoneNo"],
        userId: json["user_id"],
        lineChannelId: json["line_channel_id"],
        lineUid: json["line_uid"],
        points: json["points"],
        userName: json["user_name"],
        gender: json["gender"],
        birthday: json["birthday"],
        profileUpdateDate: json["profile_update_date"],
      );

  Map<String, dynamic> toJson() => {
        "email": email,
        "phoneNo": phoneNo,
        "user_id": userId,
        "line_channel_id": lineChannelId,
        "line_uid": lineUid,
        "points": points,
        "user_name": userName,
        "gender": gender,
        "birthday": birthday,
        "profile_update_date": profileUpdateDate,
      };
}
