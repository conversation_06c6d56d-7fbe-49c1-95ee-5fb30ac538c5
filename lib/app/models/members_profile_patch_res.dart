// To parse this JSON data, do
//
//     final membersProfilePatchRes = membersProfilePatchResFromJson(jsonString);

import 'dart:convert';

import 'members_profile_res.dart';

class MembersProfilePatchRes {
  bool? status;
  String? message;
  MembersProfileRes? profile;

  MembersProfilePatchRes({
    this.status,
    this.message,
    this.profile,
  });

  MembersProfilePatchRes copyWith({
    bool? status,
    String? message,
    MembersProfileRes? profile,
  }) =>
      MembersProfilePatchRes(
        status: status ?? this.status,
        message: message ?? this.message,
        profile: profile ?? this.profile,
      );

  factory MembersProfilePatchRes.fromRawJson(String str) =>
      MembersProfilePatchRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersProfilePatchRes.fromJson(Map<String, dynamic> json) =>
      MembersProfilePatchRes(
        status: json["status"],
        message: json["message"],
        profile: json["profile"] == null
            ? null
            : MembersProfileRes.fromJson(json["profile"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "profile": profile?.toJson(),
      };
}
