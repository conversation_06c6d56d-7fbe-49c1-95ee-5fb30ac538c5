import 'dart:convert';

class ErrorRes {
  bool? status;
  String? type;
  String? error;
  String? mid;
  String? uid;
  String? description;
  Map? fields;
  String? email;
  String? fullName;

  ErrorRes({
    this.status,
    this.type,
    this.error,
    this.mid,
    this.uid,
    this.description,
    this.fields,
    this.email,
    this.fullName,
  });

  ErrorRes copyWith({
    bool? status,
    String? type,
    String? error,
    String? mid,
    String? uid,
    String? description,
    Map? fields,
    String? email,
    String? fullName,
  }) =>
      ErrorRes(
        status: status ?? this.status,
        type: type ?? this.type,
        error: error ?? this.error,
        mid: mid ?? this.mid,
        uid: uid ?? this.uid,
        description: description ?? this.description,
        fields: fields ?? this.fields,
        email: email ?? this.email,
        fullName: fullName ?? this.fullName,
      );

  factory ErrorRes.fromRawJson(String str) =>
      ErrorRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ErrorRes.fromJson(Map<String, dynamic> json) => ErrorRes(
        status: json["status"],
        type: json["type"],
        error: json["error"],
        mid: json["mid"],
        uid: json["uid"],
        description: json["description"],
        fields: json["fields"],
        email: json["email"],
        fullName: json["full_name"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "type": type,
        "error": error,
        "mid": mid,
        "uid": uid,
        "description": description,
        "fields": fields,
        "email": email,
        "full_name": fullName,
      };

  @override
  String toString() {
    if (type == 'MalformedRequestError') {
      return '欄位格式錯誤\n$fields';
    }
    if (type == 'NotFoundError') {
      return '頁面不存在';
    }
    if (type == 'LoginFailError') {
      return '登入失敗\n請檢查登入帳號及密碼是否正確';
    }
    if (description != null && description!.isNotEmpty) {
      return description ?? '';
    }
    return error ?? '';
  }
}
