// To parse this JSON data, do
//
//     final addressReq = addressReqFromJson(jsonString);

import 'dart:convert';

class AddressReq {
  int? type;
  String? id;

  AddressReq({
    this.type,
    this.id,
  });

  AddressReq copyWith({
    int? type,
    String? id,
  }) =>
      AddressReq(
        type: type ?? this.type,
        id: id ?? this.id,
      );

  factory AddressReq.fromRawJson(String str) =>
      AddressReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AddressReq.fromJson(Map<String, dynamic> json) => AddressReq(
        type: json["type"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "id": id,
      };
}
