import 'dart:convert';

class Og {
  String? title;
  String? description;

  Og({
    this.title,
    this.description,
  });

  Og copyWith({
    String? title,
    String? description,
  }) =>
      Og(
        title: title ?? this.title,
        description: description ?? this.description,
      );

  factory Og.fromRawJson(String str) => Og.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Og.fromJson(Map<String, dynamic> json) => Og(
        title: json["title"],
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "description": description,
      };
}
