// To parse this JSON data, do
//
//     final cartQuantityRes = cartQuantityResFromJson(jsonString);

import 'dart:convert';

class CartQuantityRes {
  bool? status;
  int? quantity;
  int? shippingQuantity;

  CartQuantityRes({
    this.status,
    this.quantity,
    this.shippingQuantity,
  });

  CartQuantityRes copyWith({
    bool? status,
    int? quantity,
    int? shippingQuantity,
  }) =>
      CartQuantityRes(
        status: status ?? this.status,
        quantity: quantity ?? this.quantity,
        shippingQuantity: shippingQuantity ?? this.shippingQuantity,
      );

  factory CartQuantityRes.fromRawJson(String str) =>
      CartQuantityRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CartQuantityRes.fromJson(Map<String, dynamic> json) =>
      CartQuantityRes(
        status: json["status"],
        quantity: json["quantity"],
        shippingQuantity: json["shipping_quantity"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "quantity": quantity,
        "shipping_quantity": shippingQuantity,
      };
}
