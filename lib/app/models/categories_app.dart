// To parse this JSON data, do
//
//     final categoriesApp = categoriesAppFromJson(jsonString);

import 'dart:convert';

import 'product_detail.dart';

class CategoriesApp {
  String? categoryId;
  String? categoryName;
  bool? isMainCategory;
  List<ProductDetail>? items;

  CategoriesApp({
    this.categoryId,
    this.categoryName,
    this.isMainCategory,
    this.items,
  });

  CategoriesApp copyWith({
    String? categoryId,
    String? categoryName,
    bool? isMainCategory,
    List<ProductDetail>? items,
  }) =>
      CategoriesApp(
        categoryId: categoryId ?? this.categoryId,
        categoryName: categoryName ?? this.categoryName,
        isMainCategory: isMainCategory ?? this.isMainCategory,
        items: items ?? this.items,
      );

  factory CategoriesApp.fromRawJson(String str) =>
      CategoriesApp.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CategoriesApp.fromJson(Map<String, dynamic> json) => CategoriesApp(
        categoryId: json["category_id"],
        categoryName: json["category_name"],
        isMainCategory: json["is_main_category"],
        items: json["items"] == null
            ? []
            : List<ProductDetail>.from(
                json["items"]!.map((x) => ProductDetail.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "category_id": categoryId,
        "category_name": categoryName,
        "is_main_category": isMainCategory,
        "items": items == null
            ? []
            : List<dynamic>.from(items!.map((x) => x.toJson())),
      };
}
