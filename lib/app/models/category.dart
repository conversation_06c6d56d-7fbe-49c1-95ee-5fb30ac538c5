// To parse this JSON data, do
//
//     final category = categoryFromJson(jsonString);

import 'dart:convert';

import 'app.dart';
import 'color_and_is_strong.dart';
import 'thumbnail.dart';
import 'url.dart';

class Category {
  String? id;
  String? parentId;
  String? name;
  String? categoryId;
  bool? showSubCategories;
  Url? url;
  List<Category>? subCategories;
  String? sort;
  ColorAndIsStrong? colorAndIsStrong;
  Thumbnail? thumbnail;
  String? type;
  String? visible;
  List<App>? app;

  Category({
    this.id,
    this.parentId,
    this.name,
    this.categoryId,
    this.showSubCategories,
    this.url,
    this.subCategories,
    this.sort,
    this.colorAndIsStrong,
    this.thumbnail,
    this.type,
    this.visible,
    this.app,
  });

  Category copyWith({
    String? id,
    String? parentId,
    String? name,
    String? categoryId,
    bool? showSubCategories,
    Url? url,
    List<Category>? subCategories,
    String? sort,
    ColorAndIsStrong? colorAndIsStrong,
    Thumbnail? thumbnail,
    String? type,
    String? visible,
    List<App>? app,
  }) =>
      Category(
        id: id ?? this.id,
        parentId: parentId ?? this.parentId,
        name: name ?? this.name,
        categoryId: categoryId ?? this.categoryId,
        showSubCategories: showSubCategories ?? this.showSubCategories,
        url: url ?? this.url,
        subCategories: subCategories ?? this.subCategories,
        sort: sort ?? this.sort,
        colorAndIsStrong: colorAndIsStrong ?? this.colorAndIsStrong,
        thumbnail: thumbnail ?? this.thumbnail,
        type: type ?? this.type,
        visible: visible ?? this.visible,
        app: app ?? this.app,
      );

  factory Category.fromRawJson(String str) =>
      Category.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        id: '${json["id"]}',
        parentId: '${json["parent_id"]}',
        name: json["name"],
        categoryId: json["category_id"],
        showSubCategories: json["show_sub_categories"],
        url: json["url"] is Map ? Url.fromJson(json["url"]) : null,
        subCategories: json["sub_categories"] == null
            ? []
            : List<Category>.from(
                json["sub_categories"]!.map((x) => Category.fromJson(x))),
        sort: json["sort"],
        colorAndIsStrong: json["color_and_is_strong"] == null
            ? null
            : ColorAndIsStrong.fromJson(json["color_and_is_strong"]),
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        type: json["type"],
        visible: json["visible"],
        app: json["app"] == null
            ? []
            : List<App>.from(json["app"]!.map((x) => App.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "parent_id": parentId,
        "name": name,
        "category_id": categoryId,
        "show_sub_categories": showSubCategories,
        "url": url?.toJson(),
        "sub_categories": subCategories == null
            ? []
            : List<dynamic>.from(subCategories!.map((x) => x.toJson())),
        "sort": sort,
        "color_and_is_strong": colorAndIsStrong?.toJson(),
        "thumbnail": thumbnail?.toJson(),
        "type": type,
        "visible": visible,
        "app":
            app == null ? [] : List<dynamic>.from(app!.map((x) => x.toJson())),
      };
}
