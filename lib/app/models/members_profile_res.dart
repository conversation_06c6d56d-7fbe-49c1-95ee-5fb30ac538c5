// To parse this JSON data, do
//
//     final membersProfileRes = membersProfileResFromJson(jsonString);

import 'dart:convert';

class MembersProfileRes {
  String? email;
  String? fullname;
  String? provider;
  String? id;
  String? gender;
  String? birthday;
  int? isAllowNotification;
  int? subscribeEpaper;

  MembersProfileRes({
    this.email,
    this.fullname,
    this.provider,
    this.id,
    this.gender,
    this.birthday,
    this.isAllowNotification,
    this.subscribeEpaper,
  });

  MembersProfileRes copyWith({
    String? email,
    String? fullname,
    String? provider,
    String? id,
    String? gender,
    String? birthday,
    int? isAllowNotification,
    int? subscribeEpaper,
  }) =>
      MembersProfileRes(
        email: email ?? this.email,
        fullname: fullname ?? this.fullname,
        provider: provider ?? this.provider,
        id: id ?? this.id,
        gender: gender ?? this.gender,
        birthday: birthday ?? this.birthday,
        isAllowNotification: isAllowNotification ?? this.isAllowNotification,
        subscribeEpaper: subscribeEpaper ?? this.subscribeEpaper,
      );

  factory MembersProfileRes.fromRawJson(String str) =>
      MembersProfileRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersProfileRes.fromJson(Map<String, dynamic> json) =>
      MembersProfileRes(
        email: json["email"],
        fullname: json["fullname"],
        provider: json["provider"],
        id: json["id"],
        gender: '${json["gender"]}',
        birthday: json["birthday"],
        isAllowNotification: json["is_allow_notification"],
        subscribeEpaper: json["subscribe_epaper"],
      );

  Map<String, dynamic> toJson() => {
        "email": email,
        "fullname": fullname,
        "provider": provider,
        "id": id,
        "gender": gender,
        "birthday": birthday,
        "is_allow_notification": isAllowNotification,
        "subscribe_epaper": subscribeEpaper,
      };
}
