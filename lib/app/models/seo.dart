// To parse this JSON data, do
//
//     final seo = seoFrom<PERSON>son(jsonString);

import 'dart:convert';

import 'og.dart';

class Seo {
  String? title;
  String? keywords;
  String? description;
  Og? og;
  bool? showSubCategories;

  Seo({
    this.title,
    this.keywords,
    this.description,
    this.og,
    this.showSubCategories,
  });

  Seo copyWith({
    String? title,
    String? keywords,
    String? description,
    Og? og,
    bool? showSubCategories,
  }) =>
      Seo(
        title: title ?? this.title,
        keywords: keywords ?? this.keywords,
        description: description ?? this.description,
        og: og ?? this.og,
        showSubCategories: showSubCategories ?? this.showSubCategories,
      );

  factory Seo.fromRawJson(String str) => Seo.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Seo.fromJson(Map<String, dynamic> json) => Se<PERSON>(
        title: json["title"],
        keywords: json["keywords"],
        description: json["description"],
        og: json["og"] == null ? null : Og.fromJson(json["og"]),
        showSubCategories: json["show_sub_categories"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "keywords": keywords,
        "description": description,
        "og": og?.toJson(),
        "show_sub_categories": showSubCategories,
      };
}
