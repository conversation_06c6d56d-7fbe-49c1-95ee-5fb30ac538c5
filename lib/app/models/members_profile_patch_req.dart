// To parse this JSON data, do
//
//     final membersProfilePatchReq = membersProfilePatchReqFromJson(jsonString);

import 'dart:convert';

class MembersProfilePatchReq {
  String? fullname;
  String? gender;
  String? birthday;
  String? subscribeEpaper;

  MembersProfilePatchReq({
    this.fullname,
    this.gender,
    this.birthday,
    this.subscribeEpaper,
  });

  MembersProfilePatchReq copyWith({
    String? fullname,
    String? gender,
    String? birthday,
    String? subscribeEpaper,
  }) =>
      MembersProfilePatchReq(
        fullname: fullname ?? this.fullname,
        gender: gender ?? this.gender,
        birthday: birthday ?? this.birthday,
        subscribeEpaper: subscribeEpaper ?? this.subscribeEpaper,
      );

  factory MembersProfilePatchReq.fromRawJson(String str) =>
      MembersProfilePatchReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersProfilePatchReq.fromJson(Map<String, dynamic> json) =>
      MembersProfilePatchReq(
        fullname: json["fullname"],
        gender: json["gender"],
        birthday: json["birthday"],
        subscribeEpaper: json["subscribe_epaper"],
      );

  Map<String, dynamic> toJson() => {
        "fullname": fullname,
        "gender": gender,
        "birthday": birthday,
        "subscribe_epaper": subscribeEpaper,
      };
}
