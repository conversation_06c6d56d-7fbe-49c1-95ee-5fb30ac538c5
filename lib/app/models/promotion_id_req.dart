import 'dart:convert';

class PromotionIdReq {
  int? page;
  int? size;

  PromotionIdReq({
    this.page,
    this.size,
  });

  PromotionIdReq copyWith({
    int? page,
    int? size,
  }) =>
      PromotionIdReq(
        page: page ?? this.page,
        size: size ?? this.size,
      );

  factory PromotionIdReq.fromRawJson(String str) =>
      PromotionIdReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PromotionIdReq.fromJson(Map<String, dynamic> json) => PromotionIdReq(
        page: json["page"],
        size: json["size"],
      );

  Map<String, dynamic> toJson() => {
        "page": page,
        "size": size,
      };
}
