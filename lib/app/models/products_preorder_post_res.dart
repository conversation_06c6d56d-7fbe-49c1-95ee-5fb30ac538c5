// To parse this JSON data, do
//
//     final productsPreorderPostRes = productsPreorderPostResFromJson(jsonString);

import 'dart:convert';

class ProductsPreorderPostRes {
  bool? status;
  String? message;
  String? email;
  String? productId;

  ProductsPreorderPostRes({
    this.status,
    this.message,
    this.email,
    this.productId,
  });

  ProductsPreorderPostRes copyWith({
    bool? status,
    String? message,
    String? email,
    String? productId,
  }) =>
      ProductsPreorderPostRes(
        status: status ?? this.status,
        message: message ?? this.message,
        email: email ?? this.email,
        productId: productId ?? this.productId,
      );

  factory ProductsPreorderPostRes.fromRawJson(String str) =>
      ProductsPreorderPostRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductsPreorderPostRes.fromJson(Map<String, dynamic> json) =>
      ProductsPreorderPostRes(
        status: json["status"],
        message: json["message"],
        email: json["email"],
        productId: json["product_id"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "email": email,
        "product_id": productId,
      };
}
