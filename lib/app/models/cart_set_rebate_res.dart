// To parse this JSON data, do
//
//     final cartSetRebateRes = cartSetRebateResFromJson(jsonString);

import 'dart:convert';

class CartSetRebateRes {
  bool? status;
  String? message;
  String? paymentId;

  CartSetRebateRes({
    this.status,
    this.message,
    this.paymentId,
  });

  CartSetRebateRes copyWith({
    bool? status,
    String? message,
    String? paymentId,
  }) =>
      CartSetRebateRes(
        status: status ?? this.status,
        message: message ?? this.message,
        paymentId: paymentId ?? this.paymentId,
      );

  factory CartSetRebateRes.fromRawJson(String str) =>
      CartSetRebateRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CartSetRebateRes.fromJson(Map<String, dynamic> json) =>
      CartSetRebateRes(
        status: json["status"],
        message: json["message"],
        paymentId: json["payment_id"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "payment_id": paymentId,
      };
}
