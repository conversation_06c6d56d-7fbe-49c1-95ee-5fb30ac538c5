import 'dart:convert';

class ColorAndIsStrong {
  String? color;
  bool? isStrong;

  ColorAndIsStrong({
    this.color,
    this.isStrong,
  });

  ColorAndIsStrong copyWith({
    String? color,
    bool? isStrong,
  }) =>
      ColorAndIsStrong(
        color: color ?? this.color,
        isStrong: isStrong ?? this.isStrong,
      );

  factory ColorAndIsStrong.fromRawJson(String str) =>
      ColorAndIsStrong.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ColorAndIsStrong.fromJson(Map<String, dynamic> json) =>
      ColorAndIsStrong(
        color: json["color"],
        isStrong: json["is_strong"],
      );

  Map<String, dynamic> toJson() => {
        "color": color,
        "is_strong": isStrong,
      };
}
