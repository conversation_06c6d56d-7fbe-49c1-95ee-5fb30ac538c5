// To parse this JSON data, do
//
//     final membersOrdersInvoicesRes = membersOrdersInvoicesResFromJson(jsonString);

import 'dart:convert';

class MembersOrdersInvoicesRes {
  String? invoiceNumber;
  String? invoiceDate;
  String? orderNumber;
  String? buyerBan;
  String? buyerName;
  String? buyerAddress;
  String? sellerBan;
  String? sellerName;
  int? invoiceAmount;
  String? remark;
  String? randomNumber;
  String? code39;
  String? qrcodeLeft;
  String? qrcodeRight;
  String? taxType;
  String? taxAmount;
  String? comment;
  List<Detail>? detail;

  MembersOrdersInvoicesRes({
    this.invoiceNumber,
    this.invoiceDate,
    this.orderNumber,
    this.buyerBan,
    this.buyerName,
    this.buyerAddress,
    this.sellerBan,
    this.sellerName,
    this.invoiceAmount,
    this.remark,
    this.randomNumber,
    this.code39,
    this.qrcodeLeft,
    this.qrcodeRight,
    this.taxType,
    this.taxAmount,
    this.comment,
    this.detail,
  });

  MembersOrdersInvoicesRes copyWith({
    String? invoiceNumber,
    String? invoiceDate,
    String? orderNumber,
    String? buyerBan,
    String? buyerName,
    String? buyerAddress,
    String? sellerBan,
    String? sellerName,
    int? invoiceAmount,
    String? remark,
    String? randomNumber,
    String? code39,
    String? qrcodeLeft,
    String? qrcodeRight,
    String? taxType,
    String? taxAmount,
    String? comment,
    List<Detail>? detail,
  }) =>
      MembersOrdersInvoicesRes(
        invoiceNumber: invoiceNumber ?? this.invoiceNumber,
        invoiceDate: invoiceDate ?? this.invoiceDate,
        orderNumber: orderNumber ?? this.orderNumber,
        buyerBan: buyerBan ?? this.buyerBan,
        buyerName: buyerName ?? this.buyerName,
        buyerAddress: buyerAddress ?? this.buyerAddress,
        sellerBan: sellerBan ?? this.sellerBan,
        sellerName: sellerName ?? this.sellerName,
        invoiceAmount: invoiceAmount ?? this.invoiceAmount,
        remark: remark ?? this.remark,
        randomNumber: randomNumber ?? this.randomNumber,
        code39: code39 ?? this.code39,
        qrcodeLeft: qrcodeLeft ?? this.qrcodeLeft,
        qrcodeRight: qrcodeRight ?? this.qrcodeRight,
        taxType: taxType ?? this.taxType,
        taxAmount: taxAmount ?? this.taxAmount,
        comment: comment ?? this.comment,
        detail: detail ?? this.detail,
      );

  factory MembersOrdersInvoicesRes.fromRawJson(String str) =>
      MembersOrdersInvoicesRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersOrdersInvoicesRes.fromJson(Map<String, dynamic> json) =>
      MembersOrdersInvoicesRes(
        invoiceNumber: json["invoice_number"],
        invoiceDate: json["invoice_date"],
        orderNumber: '${json["order_number"]}',
        buyerBan: json["buyer_ban"],
        buyerName: json["buyer_name"],
        buyerAddress: json["buyer_address"],
        sellerBan: '${json["seller_ban"]}',
        sellerName: json["seller_name"],
        invoiceAmount: json["invoice_amount"],
        remark: json["remark"],
        randomNumber: json["random_number"],
        code39: json["code_39"],
        qrcodeLeft: json["qrcode_left"],
        qrcodeRight: json["qrcode_right"],
        taxType: json["tax_type"],
        taxAmount: '${json["tax_amount"]}',
        comment: json["comment"],
        detail: json["detail"] == null
            ? []
            : List<Detail>.from(json["detail"]!.map((x) => Detail.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "invoice_number": invoiceNumber,
        "invoice_date": invoiceDate,
        "order_number": orderNumber,
        "buyer_ban": buyerBan,
        "buyer_name": buyerName,
        "buyer_address": buyerAddress,
        "seller_ban": sellerBan,
        "seller_name": sellerName,
        "invoice_amount": invoiceAmount,
        "remark": remark,
        "random_number": randomNumber,
        "code_39": code39,
        "qrcode_left": qrcodeLeft,
        "qrcode_right": qrcodeRight,
        "tax_type": taxType,
        "tax_amount": taxAmount,
        "comment": comment,
        "detail": detail == null
            ? []
            : List<dynamic>.from(detail!.map((x) => x.toJson())),
      };
}

class Detail {
  String? productName;
  int? quantity;
  int? unitPrice;
  int? amount;

  Detail({
    this.productName,
    this.quantity,
    this.unitPrice,
    this.amount,
  });

  Detail copyWith({
    String? productName,
    int? quantity,
    int? unitPrice,
    int? amount,
  }) =>
      Detail(
        productName: productName ?? this.productName,
        quantity: quantity ?? this.quantity,
        unitPrice: unitPrice ?? this.unitPrice,
        amount: amount ?? this.amount,
      );

  factory Detail.fromRawJson(String str) => Detail.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Detail.fromJson(Map<String, dynamic> json) => Detail(
        productName: json["product_Name"],
        quantity: json["quantity"],
        unitPrice: json["unit_price"],
        amount: json["amount"],
      );

  Map<String, dynamic> toJson() => {
        "product_Name": productName,
        "quantity": quantity,
        "unit_price": unitPrice,
        "amount": amount,
      };
}
