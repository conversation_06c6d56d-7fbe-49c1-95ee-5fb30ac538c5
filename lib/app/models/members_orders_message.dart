// To parse this JSON data, do
//
//     final membersOrdersIdShipsRes = membersOrdersIdShipsResFromJson(jsonString);

import 'dart:convert';

class MembersOrdersMessage {
  String? message;
  String? createDatetime;

  MembersOrdersMessage({
    this.message,
    this.createDatetime,
  });

  MembersOrdersMessage copyWith({
    String? message,
    String? createDatetime,
  }) =>
      MembersOrdersMessage(
        message: message ?? this.message,
        createDatetime: createDatetime ?? this.createDatetime,
      );

  factory MembersOrdersMessage.fromRawJson(String str) =>
      MembersOrdersMessage.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersOrdersMessage.fromJson(Map<String, dynamic> json) =>
      MembersOrdersMessage(
        message: json["message"],
        createDatetime: json["create_datetime"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "create_datetime": createDatetime,
      };
}
