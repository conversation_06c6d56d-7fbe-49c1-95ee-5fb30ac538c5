// To parse this JSON data, do
//
//     final membersOrdersIdQuestionsPostReq = membersOrdersIdQuestionsPostReqFromJson(jsonString);

import 'dart:convert';

class MembersOrdersQuestionsPostReq {
  String? question;
  int? typeId;

  MembersOrdersQuestionsPostReq({
    this.question,
    this.typeId,
  });

  MembersOrdersQuestionsPostReq copyWith({
    String? question,
    int? typeId,
  }) =>
      MembersOrdersQuestionsPostReq(
        question: question ?? this.question,
        typeId: typeId ?? this.typeId,
      );

  factory MembersOrdersQuestionsPostReq.fromRawJson(String str) =>
      MembersOrdersQuestionsPostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersOrdersQuestionsPostReq.fromJson(Map<String, dynamic> json) =>
      MembersOrdersQuestionsPostReq(
        question: json["question"],
        typeId: json["type_id"],
      );

  Map<String, dynamic> toJson() => {
        "question": question,
        "type_id": typeId,
      };
}
