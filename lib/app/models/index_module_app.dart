import 'dart:convert';

import 'thumbnail.dart';
import 'url.dart';

class IndexModuleApp {
  String? name;
  Url? url;
  String? sort;
  String? text;
  num? price;
  num? promotionPrice;
  Thumbnail? thumbnail;

  IndexModuleApp({
    this.name,
    this.url,
    this.sort,
    this.text,
    this.price,
    this.promotionPrice,
    this.thumbnail,
  });

  IndexModuleApp copyWith({
    String? name,
    Url? url,
    String? sort,
    String? text,
    num? price,
    num? promotionPrice,
    Thumbnail? thumbnail,
  }) =>
      IndexModuleApp(
        name: name ?? this.name,
        url: url ?? this.url,
        sort: sort ?? this.sort,
        text: text ?? this.text,
        price: price ?? this.price,
        promotionPrice: promotionPrice ?? this.promotionPrice,
        thumbnail: thumbnail ?? this.thumbnail,
      );

  factory IndexModuleApp.fromRawJson(String str) =>
      IndexModuleApp.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory IndexModuleApp.fromJson(Map<String, dynamic> json) => IndexModuleApp(
        name: json["name"],
        url: json["url"] is Map ? Url.fromJson(json["url"]) : null,
        sort: json["sort"],
        text: json["text"],
        price: json["price"],
        promotionPrice: json["promotion_price"],
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "url": url?.toJson(),
        "sort": sort,
        "text": text,
        "price": price,
        "promotion_price": promotionPrice,
        "thumbnail": thumbnail?.toJson(),
      };
}
