// To parse this JSON data, do
//
//     final road = roadFromJson(jsonString);

import 'dart:convert';

class StoreDetail {
  String? storeId;
  String? storeName;
  String? storeAddress;
  String? storeTel;

  StoreDetail({
    this.storeId,
    this.storeName,
    this.storeAddress,
    this.storeTel,
  });

  StoreDetail copyWith({
    String? storeId,
    String? storeName,
    String? storeAddress,
    String? storeTel,
  }) =>
      StoreDetail(
        storeId: storeId ?? this.storeId,
        storeName: storeName ?? this.storeName,
        storeAddress: storeAddress ?? this.storeAddress,
        storeTel: storeTel ?? this.storeTel,
      );

  factory StoreDetail.fromRawJson(String str) =>
      StoreDetail.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StoreDetail.fromJson(Map<String, dynamic> json) => StoreDetail(
        storeId: json["store_id"],
        storeName: json["store_name"],
        storeAddress: json["store_address"],
        storeTel: json["store_tel"],
      );

  Map<String, dynamic> toJson() => {
        "store_id": storeId,
        "store_name": storeName,
        "store_address": storeAddress,
        "store_tel": storeTel,
      };
}
