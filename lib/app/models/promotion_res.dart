// To parse this JSON data, do
//
//     final promotionRes = promotionResFromJson(jsonString);

import 'dart:convert';

class PromotionRes {
  String? id;
  String? name;
  String? visible;

  PromotionRes({
    this.id,
    this.name,
    this.visible,
  });

  PromotionRes copyWith({
    String? id,
    String? name,
    String? visible,
  }) =>
      PromotionRes(
        id: id ?? this.id,
        name: name ?? this.name,
        visible: visible ?? this.visible,
      );

  factory PromotionRes.fromRawJson(String str) =>
      PromotionRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PromotionRes.fromJson(Map<String, dynamic> json) => PromotionRes(
        id: json["id"],
        name: json["name"],
        visible: json["visible"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "visible": visible,
      };
}
