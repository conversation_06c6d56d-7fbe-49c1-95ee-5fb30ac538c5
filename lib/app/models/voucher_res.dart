import 'dart:convert';

import 'url.dart';

class VoucherRes {
  int? voucherId;
  String? name;
  String? threshold;
  String? path;
  Url? url;
  String? title;
  String? endAt;
  bool? isValid;
  bool? isUsed;

  VoucherRes({
    this.voucherId,
    this.name,
    this.threshold,
    this.path,
    this.url,
    this.title,
    this.endAt,
    this.isValid,
    this.isUsed,
  });

  VoucherRes copyWith({
    int? voucherId,
    String? name,
    String? threshold,
    String? path,
    Url? url,
    String? title,
    String? endAt,
    bool? isValid,
    bool? isUsed,
  }) =>
      VoucherRes(
        voucherId: voucherId ?? this.voucherId,
        name: name ?? this.name,
        threshold: threshold ?? this.threshold,
        path: path ?? this.path,
        url: url ?? this.url,
        title: title ?? this.title,
        endAt: endAt ?? this.endAt,
        isValid: isValid ?? this.isValid,
        isUsed: isUsed ?? this.isUsed,
      );

  factory VoucherRes.fromRawJson(String str) =>
      VoucherRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VoucherRes.fromJson(Map<String, dynamic> json) => VoucherRes(
        voucherId: json["voucher_id"],
        name: json["name"],
        threshold: json["threshold"],
        path: json["path"],
        url: json["url"] is Map ? Url.fromJson(json["url"]) : null,
        title: json["title"],
        endAt: json["end_at"],
        isValid: json["is_valid"],
        isUsed: json["is_used"],
      );

  Map<String, dynamic> toJson() => {
        "voucher_id": voucherId,
        "name": name,
        "threshold": threshold,
        "path": path,
        "url": url?.toJson(),
        "title": title,
        "end_at": endAt,
        "is_valid": isValid,
        "is_used": isUsed,
      };
}
