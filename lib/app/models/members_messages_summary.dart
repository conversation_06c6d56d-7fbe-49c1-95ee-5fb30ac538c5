// To parse this JSON data, do
//
//     final membersMessagesSummary = membersMessagesSummaryFromJson(jsonString);

import 'dart:convert';

class MembersMessagesSummary {
  String? title;
  String? message;

  MembersMessagesSummary({
    this.title,
    this.message,
  });

  MembersMessagesSummary copyWith({
    String? title,
    String? message,
  }) =>
      MembersMessagesSummary(
        title: title ?? this.title,
        message: message ?? this.message,
      );

  factory MembersMessagesSummary.fromRawJson(String str) =>
      MembersMessagesSummary.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMessagesSummary.fromJson(Map<String, dynamic> json) =>
      MembersMessagesSummary(
        title: json["title"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "message": message,
      };
}
