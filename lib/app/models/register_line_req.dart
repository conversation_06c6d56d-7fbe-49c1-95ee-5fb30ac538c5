// To parse this JSON data, do
//
//     final registerLineReq = registerLineReqFromJson(jsonString);

import 'dart:convert';

class RegisterLineReq {
  String? email;
  String? password;
  String? fullname;
  String? mid;

  RegisterLineReq({
    this.email,
    this.password,
    this.fullname,
    this.mid,
  });

  RegisterLineReq copyWith({
    String? email,
    String? password,
    String? fullname,
    String? mid,
  }) =>
      RegisterLineReq(
        email: email ?? this.email,
        password: password ?? this.password,
        fullname: fullname ?? this.fullname,
        mid: mid ?? this.mid,
      );

  factory RegisterLineReq.fromRawJson(String str) =>
      RegisterLineReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RegisterLineReq.fromJson(Map<String, dynamic> json) =>
      RegisterLineReq(
        email: json["email"],
        password: json["password"],
        fullname: json["fullname"],
        mid: json["mid"],
      );

  Map<String, dynamic> toJson() => {
        "email": email,
        "password": password,
        "fullname": fullname,
        "mid": mid,
      };
}
