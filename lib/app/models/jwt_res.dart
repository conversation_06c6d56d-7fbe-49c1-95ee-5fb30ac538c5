import 'dart:convert';

class JwtRes {
  int? iat;
  int? exp;
  String? jti;
  String? jwtResFor;
  String? session;
  String? userid;
  String? email;
  String? fullname;
  String? birthday;
  String? gender;
  int? bonus;
  String? provider;

  JwtRes({
    this.iat,
    this.exp,
    this.jti,
    this.jwtResFor,
    this.session,
    this.userid,
    this.email,
    this.fullname,
    this.birthday,
    this.gender,
    this.bonus,
    this.provider,
  });

  JwtRes copyWith({
    int? iat,
    int? exp,
    String? jti,
    String? jwtResFor,
    String? session,
    String? userid,
    String? email,
    String? fullname,
    String? birthday,
    String? gender,
    int? bonus,
    String? provider,
  }) =>
      JwtRes(
        iat: iat ?? this.iat,
        exp: exp ?? this.exp,
        jti: jti ?? this.jti,
        jwtResFor: jwtResFor ?? this.jwtResFor,
        session: session ?? this.session,
        userid: userid ?? this.userid,
        email: email ?? this.email,
        fullname: fullname ?? this.fullname,
        birthday: birthday ?? this.birthday,
        gender: gender ?? this.gender,
        bonus: bonus ?? this.bonus,
        provider: provider ?? this.provider,
      );

  factory JwtRes.fromRawJson(String str) => JwtRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory JwtRes.fromJson(Map<String, dynamic> json) => JwtRes(
        iat: json["iat"],
        exp: json["exp"],
        jti: json["jti"],
        jwtResFor: json["for"],
        session: json["session"],
        userid: json["userid"],
        email: json["email"],
        fullname: json["fullname"],
        birthday: json["birthday"],
        gender: json["gender"],
        bonus: json["bonus"],
        provider: json["provider"],
      );

  Map<String, dynamic> toJson() => {
        "iat": iat,
        "exp": exp,
        "jti": jti,
        "for": jwtResFor,
        "session": session,
        "userid": userid,
        "email": email,
        "fullname": fullname,
        "birthday": birthday,
        "gender": gender,
        "bonus": bonus,
        "provider": provider,
      };
}
