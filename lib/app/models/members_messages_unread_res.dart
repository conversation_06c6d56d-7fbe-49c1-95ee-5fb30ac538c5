// To parse this JSON data, do
//
//     final membersMessagesUnreadRes = membersMessagesUnreadResFromJson(jsonString);

import 'dart:convert';

class MembersMessagesUnreadRes {
  bool? status;
  String? activities;
  String? orders;
  String? ships;
  String? questions;

  MembersMessagesUnreadRes({
    this.status,
    this.activities,
    this.orders,
    this.ships,
    this.questions,
  });

  MembersMessagesUnreadRes copyWith({
    bool? status,
    String? activities,
    String? orders,
    String? ships,
    String? questions,
  }) =>
      MembersMessagesUnreadRes(
        status: status ?? this.status,
        activities: activities ?? this.activities,
        orders: orders ?? this.orders,
        ships: ships ?? this.ships,
        questions: questions ?? this.questions,
      );

  factory MembersMessagesUnreadRes.fromRawJson(String str) =>
      MembersMessagesUnreadRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMessagesUnreadRes.fromJson(Map<String, dynamic> json) =>
      MembersMessagesUnreadRes(
        status: json["status"],
        activities: json["activities"],
        orders: json["orders"],
        ships: json["ships"],
        questions: json["questions"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "activities": activities,
        "orders": orders,
        "ships": ships,
        "questions": questions,
      };
}
