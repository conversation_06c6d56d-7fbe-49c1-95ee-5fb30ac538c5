// To parse this JSON data, do
//
//     final membersOrdersRefundPostReq = membersOrdersRefundPostReqFromJson(jsonString);

import 'dart:convert';

class MembersOrdersRefundPostReq {
  String? userRefundId;
  String? bankBranch;
  String? bankCode;
  String? accountName;
  String? accountNumber;
  String? comment;
  String? products;

  MembersOrdersRefundPostReq({
    this.userRefundId,
    this.bankBranch,
    this.bankCode,
    this.accountName,
    this.accountNumber,
    this.comment,
    this.products,
  });

  MembersOrdersRefundPostReq copyWith({
    String? userRefundId,
    String? bankBranch,
    String? bankCode,
    String? accountName,
    String? accountNumber,
    String? comment,
    String? products,
  }) =>
      MembersOrdersRefundPostReq(
        userRefundId: userRefundId ?? this.userRefundId,
        bankBranch: bankBranch ?? this.bankBranch,
        bankCode: bankCode ?? this.bankCode,
        accountName: accountName ?? this.accountName,
        accountNumber: accountNumber ?? this.accountNumber,
        comment: comment ?? this.comment,
        products: products ?? this.products,
      );

  factory MembersOrdersRefundPostReq.fromRawJson(String str) =>
      MembersOrdersRefundPostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersOrdersRefundPostReq.fromJson(Map<String, dynamic> json) =>
      MembersOrdersRefundPostReq(
        userRefundId: json["user_refund_id"],
        bankBranch: json["bank_branch"],
        bankCode: json["bank_code"],
        accountName: json["account_name"],
        accountNumber: json["account_number"],
        comment: json["comment"],
        products: json["products"],
      );

  Map<String, dynamic> toJson() => {
        "user_refund_id": userRefundId,
        "bank_branch": bankBranch,
        "bank_code": bankCode,
        "account_name": accountName,
        "account_number": accountNumber,
        "comment": comment,
        "products": products,
      };
}
