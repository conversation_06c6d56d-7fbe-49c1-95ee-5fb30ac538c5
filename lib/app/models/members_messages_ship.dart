// To parse this JSON data, do
//
//     final membersMessagesShip = membersMessagesShipFromJson(jsonString);

import 'dart:convert';

import 'thumbnail.dart';

class MembersMessagesShip {
  String? orderId;
  String? title;
  String? productId;
  String? productName;
  String? quantity;
  Thumbnail? thumbnail;
  String? createDatetime;

  MembersMessagesShip({
    this.orderId,
    this.title,
    this.productId,
    this.productName,
    this.quantity,
    this.thumbnail,
    this.createDatetime,
  });

  MembersMessagesShip copyWith({
    String? orderId,
    String? title,
    String? productId,
    String? productName,
    String? quantity,
    Thumbnail? thumbnail,
    String? createDatetime,
  }) =>
      MembersMessagesShip(
        orderId: orderId ?? this.orderId,
        title: title ?? this.title,
        productId: productId ?? this.productId,
        productName: productName ?? this.productName,
        quantity: quantity ?? this.quantity,
        thumbnail: thumbnail ?? this.thumbnail,
        createDatetime: createDatetime ?? this.createDatetime,
      );

  factory MembersMessagesShip.fromRawJson(String str) =>
      MembersMessagesShip.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMessagesShip.fromJson(Map<String, dynamic> json) =>
      MembersMessagesShip(
        orderId: '${json["order_id"]}',
        title: json["title"],
        productId: '${json["product_id"]}',
        productName: json["product_name"],
        quantity: '${json["quantity"]}',
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        createDatetime: json["create_datetime"],
      );

  Map<String, dynamic> toJson() => {
        "order_id": orderId,
        "title": title,
        "product_id": productId,
        "product_name": productName,
        "quantity": quantity,
        "thumbnail": thumbnail?.toJson(),
        "create_datetime": createDatetime,
      };
}
