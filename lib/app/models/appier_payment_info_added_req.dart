import 'dart:convert';

class AppierPaymentInfoAddedReq {
  String? paymentMethod;
  String? store;
  String? addressReceive;
  num? pointsUsed;
  String? couponUsed;

  AppierPaymentInfoAddedReq({
    this.paymentMethod,
    this.store,
    this.addressReceive,
    this.pointsUsed,
    this.couponUsed,
  });

  AppierPaymentInfoAddedReq copyWith({
    String? paymentMethod,
    String? store,
    String? addressReceive,
    num? pointsUsed,
    String? couponUsed,
  }) =>
      AppierPaymentInfoAddedReq(
        paymentMethod: paymentMethod ?? this.paymentMethod,
        store: store ?? this.store,
        addressReceive: addressReceive ?? this.addressReceive,
        pointsUsed: pointsUsed ?? this.pointsUsed,
        couponUsed: couponUsed ?? this.couponUsed,
      );

  factory AppierPaymentInfoAddedReq.fromRawJson(String str) =>
      AppierPaymentInfoAddedReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppierPaymentInfoAddedReq.fromJson(Map<String, dynamic> json) =>
      AppierPaymentInfoAddedReq(
        paymentMethod: json["payment_method"],
        store: json["store"],
        addressReceive: json["address_receive"],
        pointsUsed: json["points_used"],
        couponUsed: json["coupon_used"],
      );

  Map<String, dynamic> toJson() => {
        "payment_method": paymentMethod,
        "store": store,
        "address_receive": addressReceive,
        "points_used": pointsUsed,
        "coupon_used": couponUsed,
      };
}
