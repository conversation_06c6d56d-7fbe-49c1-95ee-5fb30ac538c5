// To parse this JSON data, do
//
//     final productsCollocationRes = productsCollocationResFromJson(jsonString);

import 'dart:convert';

import 'thumbnail.dart';

class ProductsCollocationRes {
  int? id;
  String? number;
  Thumbnail? thumbnail;
  String? shareUrl;

  ProductsCollocationRes({
    this.id,
    this.number,
    this.thumbnail,
    this.shareUrl,
  });

  ProductsCollocationRes copyWith({
    int? id,
    String? number,
    Thumbnail? thumbnail,
    String? shareUrl,
  }) =>
      ProductsCollocationRes(
        id: id ?? this.id,
        number: number ?? this.number,
        thumbnail: thumbnail ?? this.thumbnail,
        shareUrl: shareUrl ?? this.shareUrl,
      );

  factory ProductsCollocationRes.fromRawJson(String str) =>
      ProductsCollocationRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductsCollocationRes.fromJson(Map<String, dynamic> json) =>
      ProductsCollocationRes(
        id: json["id"],
        number: json["number"],
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        shareUrl: json["share_url"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "number": number,
        "thumbnail": thumbnail?.toJson(),
        "share_url": shareUrl,
      };
}
