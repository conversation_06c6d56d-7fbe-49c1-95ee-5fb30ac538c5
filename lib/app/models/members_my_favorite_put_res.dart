// To parse this JSON data, do
//
//     final membersMyFavoritePutRes = membersMyFavoritePutResFromJson(jsonString);

import 'dart:convert';

class MembersMyFavoritePutRes {
  bool? status;
  Map<String, dynamic>? synchronizedItems;

  MembersMyFavoritePutRes({
    this.status,
    this.synchronizedItems,
  });

  MembersMyFavoritePutRes copyWith({
    bool? status,
    Map<String, dynamic>? synchronizedItems,
  }) =>
      MembersMyFavoritePutRes(
        status: status ?? this.status,
        synchronizedItems: synchronizedItems ?? this.synchronizedItems,
      );

  factory MembersMyFavoritePutRes.fromRawJson(String str) =>
      MembersMyFavoritePutRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMyFavoritePutRes.fromJson(Map<String, dynamic> json) =>
      MembersMyFavoritePutRes(
        status: json["status"],
        synchronizedItems: json["synchronizedItems"] ?? {},
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "synchronizedItems": synchronizedItems ?? {},
      };
}
