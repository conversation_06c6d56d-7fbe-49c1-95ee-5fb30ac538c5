// To parse this JSON data, do
//
//     final membersMyFavoritePostReq = membersMyFavoritePostReqFromJson(jsonString);

import 'dart:convert';

class MembersMyFavoritePostReq {
  String? number;
  String? color;

  MembersMyFavoritePostReq({
    this.number,
    this.color,
  });

  MembersMyFavoritePostReq copyWith({
    String? number,
    String? color,
  }) =>
      MembersMyFavoritePostReq(
        number: number ?? this.number,
        color: color ?? this.color,
      );

  factory MembersMyFavoritePostReq.fromRawJson(String str) =>
      MembersMyFavoritePostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMyFavoritePostReq.fromJson(Map<String, dynamic> json) =>
      MembersMyFavoritePostReq(
        number: json["number"],
        color: json["color"],
      );

  Map<String, dynamic> toJson() => {
        "number": number,
        "color": color,
      };
}
