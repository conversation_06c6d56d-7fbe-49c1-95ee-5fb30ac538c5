// To parse this JSON data, do
//
//     final membersMyBonusRes = membersMyBonusResFromJson(jsonString);

import 'dart:convert';

import 'thumbnail.dart';

class MembersMyBonusRes {
  int? bonusUnused;
  int? bonusNotEffect;
  int? bonusTotal;
  Thumbnail? thumbnail;
  List<BonusDetail>? bonusList;

  MembersMyBonusRes({
    this.bonusUnused,
    this.bonusNotEffect,
    this.bonusTotal,
    this.thumbnail,
    this.bonusList,
  });

  MembersMyBonusRes copyWith({
    int? bonusUnused,
    int? bonusNotEffect,
    int? bonusTotal,
    Thumbnail? thumbnail,
    List<BonusDetail>? bonusList,
  }) =>
      MembersMyBonusRes(
        bonusUnused: bonusUnused ?? this.bonusUnused,
        bonusNotEffect: bonusNotEffect ?? this.bonusNotEffect,
        bonusTotal: bonusTotal ?? this.bonusTotal,
        thumbnail: thumbnail ?? this.thumbnail,
        bonusList: bonusList ?? this.bonusList,
      );

  factory MembersMyBonusRes.fromRawJson(String str) =>
      MembersMyBonusRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMyBonusRes.fromJson(Map<String, dynamic> json) =>
      MembersMyBonusRes(
        bonusUnused: json["bonus_unused"],
        bonusNotEffect: json["bonus_not_effect"],
        bonusTotal: json["bonus_total"],
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        bonusList: json["bonus_list"] == null
            ? []
            : List<BonusDetail>.from(
                json["bonus_list"]!.map((x) => BonusDetail.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "bonus_unused": bonusUnused,
        "bonus_not_effect": bonusNotEffect,
        "bonus_total": bonusTotal,
        "thumbnail": thumbnail?.toJson(),
        "bonus_list": bonusList == null
            ? []
            : List<dynamic>.from(bonusList!.map((x) => x.toJson())),
      };
}

class BonusDetail {
  DateTime? createDatetime;
  int? points;
  DateTime? effectDatetime;
  DateTime? expiryDatetime;

  BonusDetail({
    this.createDatetime,
    this.points,
    this.effectDatetime,
    this.expiryDatetime,
  });

  BonusDetail copyWith({
    DateTime? createDatetime,
    int? points,
    DateTime? effectDatetime,
    DateTime? expiryDatetime,
  }) =>
      BonusDetail(
        createDatetime: createDatetime ?? this.createDatetime,
        points: points ?? this.points,
        effectDatetime: effectDatetime ?? this.effectDatetime,
        expiryDatetime: expiryDatetime ?? this.expiryDatetime,
      );

  factory BonusDetail.fromRawJson(String str) =>
      BonusDetail.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BonusDetail.fromJson(Map<String, dynamic> json) => BonusDetail(
        createDatetime: json["create_datetime"] == null
            ? null
            : DateTime.parse(json["create_datetime"]),
        points: json["points"],
        effectDatetime: json["effect_datetime"] == null
            ? null
            : DateTime.parse(json["effect_datetime"]),
        expiryDatetime: json["expiry_datetime"] == null
            ? null
            : DateTime.parse(json["expiry_datetime"]),
      );

  Map<String, dynamic> toJson() => {
        "create_datetime":
            "${createDatetime!.year.toString().padLeft(4, '0')}-${createDatetime!.month.toString().padLeft(2, '0')}-${createDatetime!.day.toString().padLeft(2, '0')}",
        "points": points,
        "effect_datetime":
            "${effectDatetime!.year.toString().padLeft(4, '0')}-${effectDatetime!.month.toString().padLeft(2, '0')}-${effectDatetime!.day.toString().padLeft(2, '0')}",
        "expiry_datetime":
            "${expiryDatetime!.year.toString().padLeft(4, '0')}-${expiryDatetime!.month.toString().padLeft(2, '0')}-${expiryDatetime!.day.toString().padLeft(2, '0')}",
      };
}
