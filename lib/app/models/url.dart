import 'dart:convert';

class Url {
  String? action;
  String? id;
  String? category;
  String? page;
  String? partnerId;
  String? url;
  String? urlMobile;
  String? urlApp;
  String? contentMain;
  String? keyword;

  Url({
    this.action,
    this.id,
    this.category,
    this.page,
    this.partnerId,
    this.url,
    this.urlMobile,
    this.urlApp,
    this.contentMain,
    this.keyword,
  });

  Url copyWith({
    String? action,
    String? id,
    String? category,
    String? page,
    String? partnerId,
    String? url,
    String? urlMobile,
    String? urlApp,
    String? contentMain,
    String? keyword,
  }) =>
      Url(
        action: action ?? this.action,
        id: id ?? this.id,
        category: category ?? this.category,
        page: page ?? this.page,
        partnerId: partnerId ?? this.partnerId,
        url: url ?? this.url,
        urlMobile: urlMobile ?? this.urlMobile,
        urlApp: urlApp ?? this.urlApp,
        contentMain: contentMain ?? this.contentMain,
        keyword: keyword ?? this.keyword,
      );

  factory Url.fromRawJson(String str) => Url.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Url.fromJson(Map<String, dynamic> json) => Url(
        action: json["action"],
        id: '${json["id"]}',
        category: json["category"],
        page: json["page"],
        partnerId: json["partner_id"],
        url: json["url"],
        urlMobile: json["url_mobile"],
        urlApp: json["url_app"],
        contentMain: json["content_main"],
        keyword: json["keyword"],
      );

  Map<String, dynamic> toJson() => {
        "action": action,
        "id": id,
        "category": category,
        "page": page,
        "partner_id": partnerId,
        "url": url,
        "url_mobile": urlMobile,
        "url_app": urlApp,
        "content_main": contentMain,
        "keyword": keyword,
      };
}
