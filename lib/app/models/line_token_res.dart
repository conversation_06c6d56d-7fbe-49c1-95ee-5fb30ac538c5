// To parse this JSON data, do
//
//     final lineTokenRes = lineTokenResFromJson(jsonString);

import 'dart:convert';

class LineTokenRes {
  String? accessToken;
  String? tokenType;
  String? refreshToken;
  int? expiresIn;
  String? scope;
  String? idToken;

  LineTokenRes({
    this.accessToken,
    this.tokenType,
    this.refreshToken,
    this.expiresIn,
    this.scope,
    this.idToken,
  });

  LineTokenRes copyWith({
    String? accessToken,
    String? tokenType,
    String? refreshToken,
    int? expiresIn,
    String? scope,
    String? idToken,
  }) =>
      LineTokenRes(
        accessToken: accessToken ?? this.accessToken,
        tokenType: tokenType ?? this.tokenType,
        refreshToken: refreshToken ?? this.refreshToken,
        expiresIn: expiresIn ?? this.expiresIn,
        scope: scope ?? this.scope,
        idToken: idToken ?? this.idToken,
      );

  factory LineTokenRes.fromRawJson(String str) =>
      LineTokenRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LineTokenRes.fromJson(Map<String, dynamic> json) => LineTokenRes(
        accessToken: json["access_token"],
        tokenType: json["token_type"],
        refreshToken: json["refresh_token"],
        expiresIn: json["expires_in"],
        scope: json["scope"],
        idToken: json["id_token"],
      );

  Map<String, dynamic> toJson() => {
        "access_token": accessToken,
        "token_type": tokenType,
        "refresh_token": refreshToken,
        "expires_in": expiresIn,
        "scope": scope,
        "id_token": idToken,
      };
}
