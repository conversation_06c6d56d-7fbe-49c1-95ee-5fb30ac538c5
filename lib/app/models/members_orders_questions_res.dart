// To parse this JSON data, do
//
//     final membersOrdersQuestionsRes = membersOrdersQuestionsResFromJson(jsonString);

import 'dart:convert';

class MembersOrdersQuestionsRes {
  String? question;
  String? reply;
  String? createDatetime;
  String? replyDatetime;
  String? type;

  MembersOrdersQuestionsRes({
    this.question,
    this.reply,
    this.createDatetime,
    this.replyDatetime,
    this.type,
  });

  MembersOrdersQuestionsRes copyWith({
    String? question,
    String? reply,
    String? createDatetime,
    String? replyDatetime,
    String? type,
  }) =>
      MembersOrdersQuestionsRes(
        question: question ?? this.question,
        reply: reply ?? this.reply,
        createDatetime: createDatetime ?? this.createDatetime,
        replyDatetime: replyDatetime ?? this.replyDatetime,
        type: type ?? this.type,
      );

  factory MembersOrdersQuestionsRes.fromRawJson(String str) =>
      MembersOrdersQuestionsRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersOrdersQuestionsRes.fromJson(Map<String, dynamic> json) =>
      MembersOrdersQuestionsRes(
        question: json["question"],
        reply: json["reply"],
        createDatetime: json["create_datetime"],
        replyDatetime: json["reply_datetime"],
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "question": question,
        "reply": reply,
        "create_datetime": createDatetime,
        "reply_datetime": replyDatetime,
        "type": type,
      };
}
