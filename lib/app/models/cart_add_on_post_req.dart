// To parse this JSON data, do
//
//     final cartAddOnPostReq = cartAddOnPostReqFromJson(jsonString);

import 'dart:convert';

class CartAddOnPostReq {
  String? productId;
  String? quantity;

  CartAddOnPostReq({
    this.productId,
    this.quantity,
  });

  CartAddOnPostReq copyWith({
    String? productId,
    String? quantity,
  }) =>
      CartAddOnPostReq(
        productId: productId ?? this.productId,
        quantity: quantity ?? this.quantity,
      );

  factory CartAddOnPostReq.fromRawJson(String str) =>
      CartAddOnPostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CartAddOnPostReq.fromJson(Map<String, dynamic> json) =>
      CartAddOnPostReq(
        productId: json["product_id"],
        quantity: json["quantity"],
      );

  Map<String, dynamic> toJson() => {
        "product_id": productId,
        "quantity": quantity,
      };
}
