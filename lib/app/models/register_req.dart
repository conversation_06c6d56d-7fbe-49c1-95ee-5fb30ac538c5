// To parse this JSON data, do
//
//     final registerReq = registerReqFromJson(jsonString);

import 'dart:convert';

class RegisterReq {
  String? email;
  String? password;
  String? fullname;
  String? gender;
  String? birthday;

  RegisterReq({
    this.email,
    this.password,
    this.fullname,
    this.gender,
    this.birthday,
  });

  RegisterReq copyWith({
    String? email,
    String? password,
    String? fullname,
    String? birthday,
    String? subscribeEpaper,
  }) =>
      RegisterReq(
        email: email ?? this.email,
        password: password ?? this.password,
        fullname: fullname ?? this.fullname,
        gender: gender ?? this.gender,
        birthday: birthday ?? this.birthday,
      );

  factory RegisterReq.fromRawJson(String str) =>
      RegisterReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RegisterReq.fromJson(Map<String, dynamic> json) => RegisterReq(
        email: json["email"],
        password: json["password"],
        fullname: json["fullname"],
        gender: json["gender"],
        birthday: json["birthday"],
      );

  Map<String, dynamic> toJson() => {
        "email": email,
        "password": password,
        "fullname": fullname,
        "gender": gender,
        "birthday": birthday,
      };
}
