import 'dart:convert';

class MenuRes {
  List<MenuRes>? children;
  int? id;
  String? name;
  String? path;
  String? url;
  bool? blank;
  bool? branch;
  bool? app;
  bool? web;
  int? parentId;
  bool? expand;

  MenuRes({
    this.children,
    this.id,
    this.name,
    this.path,
    this.url,
    this.blank,
    this.branch,
    this.app,
    this.web,
    this.parentId,
    this.expand,
  });

  MenuRes copyWith({
    List<MenuRes>? children,
    int? id,
    String? name,
    String? path,
    String? url,
    bool? blank,
    bool? branch,
    bool? app,
    bool? web,
    int? parentId,
    bool? expand,
  }) =>
      MenuRes(
        children: children ?? this.children,
        id: id ?? this.id,
        name: name ?? this.name,
        path: path ?? this.path,
        url: url ?? this.url,
        blank: blank ?? this.blank,
        branch: branch ?? this.branch,
        app: app ?? this.app,
        web: web ?? this.web,
        parentId: parentId ?? this.parentId,
        expand: expand ?? this.expand,
      );

  factory MenuRes.fromRawJson(String str) => MenuRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MenuRes.fromJson(Map<String, dynamic> json) => MenuRes(
        children: json["children"] == null
            ? []
            : List<MenuRes>.from(
                json["children"]!.map((x) => MenuRes.fromJson(x))),
        id: json["id"],
        name: json["name"],
        path: json["path"],
        url: '${json["url"]}',
        blank: json["blank"],
        branch: json["branch"],
        app: json["app"],
        web: json["web"],
        parentId: json["parent_id"],
        expand: json["expand"],
      );

  Map<String, dynamic> toJson() => {
        "children": children == null
            ? []
            : List<dynamic>.from(children!.map((x) => x.toJson())),
        "id": id,
        "name": name,
        "path": path,
        "url": url,
        "blank": blank,
        "branch": branch,
        "app": app,
        "web": web,
        "parent_id": parentId,
        "expand": expand,
      };
}
