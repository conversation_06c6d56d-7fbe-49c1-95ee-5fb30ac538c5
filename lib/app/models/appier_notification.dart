import 'dart:convert';

class AppierNotification {
  bool? resizeImage;
  String? rno;
  String? expirationTime;
  bool? headsUp;
  String? notificationId;
  String? source;
  String? message;
  String? title;
  String? type;
  String? channelId;
  bool? poll;
  List<Action>? actions;
  String? bigImageUrl;
  String? subText;
  String? textColor;
  String? bgColor;
  String? deepLink;
  bool? pileUp;
  String? imageUrl;
  QgPayload? qgPayload;
  bool? q10Cf;
  String? soundUrl;
  String? qgTimestamp;
  Aps? aps;
  QgPush? qgPush;
  String? qgts;
  String? nid;
  String? qg;
  String? contentImageUrl;
  Animation? animation;
  List<Carousel>? slider;
  String? qgPrevButton;
  String? qgNextButton;
  bool? closeNotificationOnItemClick;
  List<Carousel>? carousel;
  String? iconImage;

  AppierNotification({
    this.resizeImage,
    this.rno,
    this.expirationTime,
    this.headsUp,
    this.notificationId,
    this.source,
    this.message,
    this.title,
    this.type,
    this.channelId,
    this.poll,
    this.actions,
    this.bigImageUrl,
    this.subText,
    this.textColor,
    this.bgColor,
    this.deepLink,
    this.pileUp,
    this.imageUrl,
    this.qgPayload,
    this.q10Cf,
    this.soundUrl,
    this.qgTimestamp,
    this.aps,
    this.qgPush,
    this.qgts,
    this.nid,
    this.qg,
    this.contentImageUrl,
    this.animation,
    this.slider,
    this.qgPrevButton,
    this.qgNextButton,
    this.closeNotificationOnItemClick,
    this.carousel,
    this.iconImage,
  });

  AppierNotification copyWith({
    bool? resizeImage,
    String? rno,
    String? expirationTime,
    bool? headsUp,
    String? notificationId,
    String? source,
    String? message,
    String? title,
    String? type,
    String? channelId,
    bool? poll,
    List<Action>? actions,
    String? bigImageUrl,
    String? subText,
    String? textColor,
    String? bgColor,
    String? deepLink,
    bool? pileUp,
    String? imageUrl,
    QgPayload? qgPayload,
    bool? q10Cf,
    String? soundUrl,
    String? qgTimestamp,
    Aps? aps,
    QgPush? qgPush,
    String? qgts,
    String? nid,
    String? qg,
    String? contentImageUrl,
    Animation? animation,
    List<Carousel>? slider,
    String? qgPrevButton,
    String? qgNextButton,
    bool? closeNotificationOnItemClick,
    List<Carousel>? carousel,
    String? iconImage,
  }) =>
      AppierNotification(
        resizeImage: resizeImage ?? this.resizeImage,
        rno: rno ?? this.rno,
        expirationTime: expirationTime ?? this.expirationTime,
        headsUp: headsUp ?? this.headsUp,
        notificationId: notificationId ?? this.notificationId,
        source: source ?? this.source,
        message: message ?? this.message,
        title: title ?? this.title,
        type: type ?? this.type,
        channelId: channelId ?? this.channelId,
        poll: poll ?? this.poll,
        actions: actions ?? this.actions,
        bigImageUrl: bigImageUrl ?? this.bigImageUrl,
        subText: subText ?? this.subText,
        textColor: textColor ?? this.textColor,
        bgColor: bgColor ?? this.bgColor,
        deepLink: deepLink ?? this.deepLink,
        pileUp: pileUp ?? this.pileUp,
        imageUrl: imageUrl ?? this.imageUrl,
        qgPayload: qgPayload ?? this.qgPayload,
        q10Cf: q10Cf ?? this.q10Cf,
        soundUrl: soundUrl ?? this.soundUrl,
        qgTimestamp: qgTimestamp ?? this.qgTimestamp,
        aps: aps ?? this.aps,
        qgPush: qgPush ?? this.qgPush,
        qgts: qgts ?? this.qgts,
        nid: nid ?? this.nid,
        qg: qg ?? this.qg,
        contentImageUrl: contentImageUrl ?? this.contentImageUrl,
        animation: animation ?? this.animation,
        slider: slider ?? this.slider,
        qgPrevButton: qgPrevButton ?? this.qgPrevButton,
        qgNextButton: qgNextButton ?? this.qgNextButton,
        closeNotificationOnItemClick:
            closeNotificationOnItemClick ?? this.closeNotificationOnItemClick,
        carousel: carousel ?? this.carousel,
        iconImage: iconImage ?? this.iconImage,
      );

  factory AppierNotification.fromRawJson(String str) =>
      AppierNotification.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppierNotification.fromJson(Map<String, dynamic> json) =>
      AppierNotification(
        resizeImage: json["resize_image"],
        rno: '${json["rno"]}',
        expirationTime: '${json["expiration_time"]}',
        headsUp: json["headsUp"],
        notificationId: '${json["notificationId"]}',
        source: json["source"],
        message: json["message"],
        title: json["title"],
        type: json["type"],
        channelId: json["channelId"],
        poll: json["poll"],
        actions: json["actions"] == null
            ? []
            : List<Action>.from(
                json["actions"]!.map((x) => Action.fromJson(x))),
        bigImageUrl: json["bigImageUrl"],
        subText: json["subText"],
        textColor: json["textColor"],
        bgColor: json["bgColor"],
        deepLink: json["deepLink"],
        pileUp: json["pileUp"],
        imageUrl: json["imageUrl"],
        qgPayload: json["qgPayload"] == null
            ? null
            : QgPayload.fromJson(json["qgPayload"]),
        q10Cf: json["q10CF"],
        soundUrl: json["soundUrl"],
        qgTimestamp: json["qgTimestamp"],
        aps: json["aps"] == null ? null : Aps.fromJson(json["aps"]),
        qgPush: json["qgPush"] == null ? null : QgPush.fromJson(json["qgPush"]),
        qgts: json["qgts"],
        nid: json["nid"],
        qg: '${json["qg"]}',
        contentImageUrl: json["contentImageUrl"],
        animation: json["animation"] == null
            ? null
            : Animation.fromJson(json["animation"]),
        slider: json["slider"] == null
            ? []
            : List<Carousel>.from(
                json["slider"]!.map((x) => Carousel.fromJson(x))),
        qgPrevButton: json["qg_prev_button"],
        qgNextButton: json["qg_next_button"],
        closeNotificationOnItemClick: json["closeNotificationOnItemClick"],
        carousel: json["carousel"] == null
            ? []
            : List<Carousel>.from(
                json["carousel"]!.map((x) => Carousel.fromJson(x))),
        iconImage: json["iconImage"],
      );

  Map<String, dynamic> toJson() => {
        "resize_image": resizeImage,
        "rno": rno,
        "expiration_time": expirationTime,
        "headsUp": headsUp,
        "notificationId": notificationId,
        "source": source,
        "message": message,
        "title": title,
        "type": type,
        "channelId": channelId,
        "poll": poll,
        "actions": actions == null
            ? []
            : List<dynamic>.from(actions!.map((x) => x.toJson())),
        "bigImageUrl": bigImageUrl,
        "subText": subText,
        "textColor": textColor,
        "bgColor": bgColor,
        "deepLink": deepLink,
        "pileUp": pileUp,
        "imageUrl": imageUrl,
        "qgPayload": qgPayload?.toJson(),
        "q10CF": q10Cf,
        "soundUrl": soundUrl,
        "qgTimestamp": qgTimestamp,
        "aps": aps?.toJson(),
        "qgPush": qgPush?.toJson(),
        "qgts": qgts,
        "nid": nid,
        "qg": qg,
        "contentImageUrl": contentImageUrl,
        "animation": animation?.toJson(),
        "slider": slider == null
            ? []
            : List<dynamic>.from(slider!.map((x) => x.toJson())),
        "qg_prev_button": qgPrevButton,
        "qg_next_button": qgNextButton,
        "closeNotificationOnItemClick": closeNotificationOnItemClick,
        "carousel": carousel == null
            ? []
            : List<dynamic>.from(carousel!.map((x) => x.toJson())),
        "iconImage": iconImage,
      };
}

class Action {
  String? deepLink;
  int? id;
  String? text;

  Action({
    this.deepLink,
    this.id,
    this.text,
  });

  Action copyWith({
    String? deepLink,
    int? id,
    String? text,
  }) =>
      Action(
        deepLink: deepLink ?? this.deepLink,
        id: id ?? this.id,
        text: text ?? this.text,
      );

  factory Action.fromRawJson(String str) => Action.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Action.fromJson(Map<String, dynamic> json) => Action(
        deepLink: json["deepLink"],
        id: json["id"],
        text: json["text"],
      );

  Map<String, dynamic> toJson() => {
        "deepLink": deepLink,
        "id": id,
        "text": text,
      };
}

class Animation {
  List<String>? images;
  int? millisecondsToRefresh;

  Animation({
    this.images,
    this.millisecondsToRefresh,
  });

  Animation copyWith({
    List<String>? images,
    int? millisecondsToRefresh,
  }) =>
      Animation(
        images: images ?? this.images,
        millisecondsToRefresh:
            millisecondsToRefresh ?? this.millisecondsToRefresh,
      );

  factory Animation.fromRawJson(String str) =>
      Animation.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Animation.fromJson(Map<String, dynamic> json) => Animation(
        images: json["images"] == null
            ? []
            : List<String>.from(json["images"]!.map((x) => x)),
        millisecondsToRefresh: json["millisecondsToRefresh"],
      );

  Map<String, dynamic> toJson() => {
        "images":
            images == null ? [] : List<dynamic>.from(images!.map((x) => x)),
        "millisecondsToRefresh": millisecondsToRefresh,
      };
}

class Aps {
  int? mutableContent;
  Alert? alert;
  String? category;
  String? sound;

  Aps({
    this.mutableContent,
    this.alert,
    this.category,
    this.sound,
  });

  Aps copyWith({
    int? mutableContent,
    Alert? alert,
    String? category,
    String? sound,
  }) =>
      Aps(
        mutableContent: mutableContent ?? this.mutableContent,
        alert: alert ?? this.alert,
        category: category ?? this.category,
        sound: sound ?? this.sound,
      );

  factory Aps.fromRawJson(String str) => Aps.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Aps.fromJson(Map<String, dynamic> json) => Aps(
        mutableContent: json["mutable-content"],
        alert: json["alert"] == null ? null : Alert.fromJson(json["alert"]),
        category: json["category"],
        sound: json["sound"],
      );

  Map<String, dynamic> toJson() => {
        "mutable-content": mutableContent,
        "alert": alert?.toJson(),
        "category": category,
        "sound": sound,
      };
}

class Alert {
  String? title;
  String? subtitle;
  String? body;

  Alert({
    this.title,
    this.subtitle,
    this.body,
  });

  Alert copyWith({
    String? title,
    String? subtitle,
    String? body,
  }) =>
      Alert(
        title: title ?? this.title,
        subtitle: subtitle ?? this.subtitle,
        body: body ?? this.body,
      );

  factory Alert.fromRawJson(String str) => Alert.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Alert.fromJson(Map<String, dynamic> json) => Alert(
        title: json["title"],
        subtitle: json["subtitle"],
        body: json["body"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "subtitle": subtitle,
        "body": body,
      };
}

class Carousel {
  String? image;
  String? deepLink;
  QgPayload? qgPayload;
  String? message;
  String? title;

  Carousel({
    this.image,
    this.deepLink,
    this.qgPayload,
    this.message,
    this.title,
  });

  Carousel copyWith({
    String? image,
    String? deepLink,
    QgPayload? qgPayload,
    String? message,
    String? title,
  }) =>
      Carousel(
        image: image ?? this.image,
        deepLink: deepLink ?? this.deepLink,
        qgPayload: qgPayload ?? this.qgPayload,
        message: message ?? this.message,
        title: title ?? this.title,
      );

  factory Carousel.fromRawJson(String str) =>
      Carousel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Carousel.fromJson(Map<String, dynamic> json) => Carousel(
        image: json["image"],
        deepLink: json["deepLink"],
        qgPayload: json["qgPayload"] == null
            ? null
            : QgPayload.fromJson(json["qgPayload"]),
        message: json["message"],
        title: json["title"],
      );

  Map<String, dynamic> toJson() => {
        "image": image,
        "deepLink": deepLink,
        "qgPayload": qgPayload?.toJson(),
        "message": message,
        "title": title,
      };
}

class QgPayload {
  String? qgToast;
  String? qgTextToCopy;
  String? myKey;
  String? keyOfCustomizeKeyValuePairsIfAny;
  String? key;
  String? myKey1;
  String? myKey2;
  String? k1;
  String? k2;
  String? k3;

  QgPayload({
    this.qgToast,
    this.qgTextToCopy,
    this.myKey,
    this.keyOfCustomizeKeyValuePairsIfAny,
    this.key,
    this.myKey1,
    this.myKey2,
    this.k1,
    this.k2,
    this.k3,
  });

  QgPayload copyWith({
    String? qgToast,
    String? qgTextToCopy,
    String? myKey,
    String? keyOfCustomizeKeyValuePairsIfAny,
    String? key,
    String? myKey1,
    String? myKey2,
    String? k1,
    String? k2,
    String? k3,
  }) =>
      QgPayload(
        qgToast: qgToast ?? this.qgToast,
        qgTextToCopy: qgTextToCopy ?? this.qgTextToCopy,
        myKey: myKey ?? this.myKey,
        keyOfCustomizeKeyValuePairsIfAny: keyOfCustomizeKeyValuePairsIfAny ??
            this.keyOfCustomizeKeyValuePairsIfAny,
        key: key ?? this.key,
        myKey1: myKey1 ?? this.myKey1,
        myKey2: myKey2 ?? this.myKey2,
        k1: k1 ?? this.k1,
        k2: k2 ?? this.k2,
        k3: k3 ?? this.k3,
      );

  factory QgPayload.fromRawJson(String str) =>
      QgPayload.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory QgPayload.fromJson(Map<String, dynamic> json) => QgPayload(
        qgToast: json["qgToast"],
        qgTextToCopy: json["qgTextToCopy"],
        myKey: json["myKey"],
        keyOfCustomizeKeyValuePairsIfAny:
            json["Key of customize key-value pairs if any"],
        key: json["key"],
        myKey1: json["myKey1"],
        myKey2: json["myKey2"],
        k1: json["k1"],
        k2: json["k2"],
        k3: json["k3"],
      );

  Map<String, dynamic> toJson() => {
        "qgToast": qgToast,
        "qgTextToCopy": qgTextToCopy,
        "myKey": myKey,
        "Key of customize key-value pairs if any":
            keyOfCustomizeKeyValuePairsIfAny,
        "key": key,
        "myKey1": myKey1,
        "myKey2": myKey2,
        "k1": k1,
        "k2": k2,
        "k3": k3,
      };
}

class QgPush {
  String? type;
  String? url;
  Custom? custom;

  QgPush({
    this.type,
    this.url,
    this.custom,
  });

  QgPush copyWith({
    String? type,
    String? url,
    Custom? custom,
  }) =>
      QgPush(
        type: type ?? this.type,
        url: url ?? this.url,
        custom: custom ?? this.custom,
      );

  factory QgPush.fromRawJson(String str) => QgPush.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory QgPush.fromJson(Map<String, dynamic> json) => QgPush(
        type: json["type"],
        url: json["url"],
        custom: json["custom"] == null ? null : Custom.fromJson(json["custom"]),
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "url": url,
        "custom": custom?.toJson(),
      };
}

class Custom {
  String? aspect;
  String? carouselType;
  List<Datum>? data;

  Custom({
    this.aspect,
    this.carouselType,
    this.data,
  });

  Custom copyWith({
    String? aspect,
    String? carouselType,
    List<Datum>? data,
  }) =>
      Custom(
        aspect: aspect ?? this.aspect,
        carouselType: carouselType ?? this.carouselType,
        data: data ?? this.data,
      );

  factory Custom.fromRawJson(String str) => Custom.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Custom.fromJson(Map<String, dynamic> json) => Custom(
        aspect: json["aspect"],
        carouselType: json["carouselType"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "aspect": aspect,
        "carouselType": carouselType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  String? title;
  String? imageUrl;
  String? body;
  String? deepLink;

  Datum({
    this.title,
    this.imageUrl,
    this.body,
    this.deepLink,
  });

  Datum copyWith({
    String? title,
    String? imageUrl,
    String? body,
    String? deepLink,
  }) =>
      Datum(
        title: title ?? this.title,
        imageUrl: imageUrl ?? this.imageUrl,
        body: body ?? this.body,
        deepLink: deepLink ?? this.deepLink,
      );

  factory Datum.fromRawJson(String str) => Datum.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        title: json["title"],
        imageUrl: json["imageUrl"],
        body: json["body"],
        deepLink: json["deepLink"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "imageUrl": imageUrl,
        "body": body,
        "deepLink": deepLink,
      };
}
