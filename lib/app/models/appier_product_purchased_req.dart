import 'dart:convert';

class AppierProductPurchasedReq {
  String? categoryName;
  String? typeName;
  String? productId;
  String? productName;
  String? productImageUrl;
  String? productUrl;
  num? productPrice;

  AppierProductPurchasedReq({
    this.categoryName,
    this.typeName,
    this.productId,
    this.productName,
    this.productImageUrl,
    this.productUrl,
    this.productPrice,
  });

  AppierProductPurchasedReq copyWith({
    String? categoryName,
    String? typeName,
    String? productId,
    String? productName,
    String? productImageUrl,
    String? productUrl,
    num? productPrice,
  }) =>
      AppierProductPurchasedReq(
        categoryName: categoryName ?? this.categoryName,
        typeName: typeName ?? this.typeName,
        productId: productId ?? this.productId,
        productName: productName ?? this.productName,
        productImageUrl: productImageUrl ?? this.productImageUrl,
        productUrl: productUrl ?? this.productUrl,
        productPrice: productPrice ?? this.productPrice,
      );

  factory AppierProductPurchasedReq.fromRawJson(String str) =>
      AppierProductPurchasedReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppierProductPurchasedReq.fromJson(Map<String, dynamic> json) =>
      AppierProductPurchasedReq(
        categoryName: json["category_name"],
        typeName: json["type_name"],
        productId: json["product_id"],
        productName: json["product_name"],
        productImageUrl: json["product_image_url"],
        productUrl: json["product_url"],
        productPrice: json["product_price"],
      );

  Map<String, dynamic> toJson() => {
        "category_name": categoryName,
        "type_name": typeName,
        "product_id": productId,
        "product_name": productName,
        "product_image_url": productImageUrl,
        "product_url": productUrl,
        "product_price": productPrice,
      };
}
