import 'dart:convert';

import 'category.dart';
import 'seo.dart';

class IndexRes {
  List<Category>? menu;
  List<Category>? mobile980;
  List<Category>? large;
  List<Category>? indexResNew;
  List<Category>? promotion;
  List<Category>? accessory;
  List<Category>? txt;
  List<Category>? picture;
  String? html;
  Seo? seo;

  IndexRes({
    this.menu,
    this.mobile980,
    this.large,
    this.indexResNew,
    this.promotion,
    this.accessory,
    this.txt,
    this.picture,
    this.html,
    this.seo,
  });

  IndexRes copyWith({
    List<Category>? menu,
    List<Category>? mobile980,
    List<Category>? large,
    List<Category>? indexResNew,
    List<Category>? promotion,
    List<Category>? accessory,
    List<Category>? txt,
    List<Category>? picture,
    String? html,
    Seo? seo,
  }) =>
      IndexRes(
        menu: menu ?? this.menu,
        mobile980: mobile980 ?? this.mobile980,
        large: large ?? this.large,
        indexResNew: indexResNew ?? this.indexResNew,
        promotion: promotion ?? this.promotion,
        accessory: accessory ?? this.accessory,
        txt: txt ?? this.txt,
        picture: picture ?? this.picture,
        html: html ?? this.html,
        seo: seo ?? this.seo,
      );

  factory IndexRes.fromRawJson(String str) =>
      IndexRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory IndexRes.fromJson(Map<String, dynamic> json) => IndexRes(
        menu: json["menu"] == null
            ? []
            : List<Category>.from(
                json["menu"]!.map((x) => Category.fromJson(x))),
        mobile980: json["mobile_980"] == null
            ? []
            : List<Category>.from(
                json["mobile_980"]!.map((x) => Category.fromJson(x))),
        large: json["large"] == null
            ? []
            : List<Category>.from(
                json["large"]!.map((x) => Category.fromJson(x))),
        indexResNew: json["new"] == null
            ? []
            : List<Category>.from(
                json["new"]!.map((x) => Category.fromJson(x))),
        promotion: json["promotion"] == null
            ? []
            : List<Category>.from(
                json["promotion"]!.map((x) => Category.fromJson(x))),
        accessory: json["accessory"] == null
            ? []
            : List<Category>.from(
                json["accessory"]!.map((x) => Category.fromJson(x))),
        txt: json["txt"] == null
            ? []
            : List<Category>.from(
                json["txt"]!.map((x) => Category.fromJson(x))),
        picture: json["picture"] == null
            ? []
            : List<Category>.from(
                json["picture"]!.map((x) => Category.fromJson(x))),
        html: json["html"],
        seo: json["seo"] == null ? null : Seo.fromJson(json["seo"]),
      );

  Map<String, dynamic> toJson() => {
        "menu": menu == null
            ? []
            : List<dynamic>.from(menu!.map((x) => x.toJson())),
        "mobile_980": mobile980 == null
            ? []
            : List<dynamic>.from(mobile980!.map((x) => x.toJson())),
        "large": large == null
            ? []
            : List<dynamic>.from(large!.map((x) => x.toJson())),
        "new": indexResNew == null
            ? []
            : List<dynamic>.from(indexResNew!.map((x) => x.toJson())),
        "promotion": promotion == null
            ? []
            : List<dynamic>.from(promotion!.map((x) => x)),
        "accessory": accessory == null
            ? []
            : List<dynamic>.from(accessory!.map((x) => x)),
        "txt":
            txt == null ? [] : List<dynamic>.from(txt!.map((x) => x.toJson())),
        "picture": picture == null
            ? []
            : List<dynamic>.from(picture!.map((x) => x.toJson())),
        "html": html,
        "seo": seo?.toJson(),
      };
}
