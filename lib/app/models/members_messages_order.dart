// To parse this JSON data, do
//
//     final membersMessagesOrder = membersMessagesOrderFromJson(jsonString);

import 'dart:convert';

class MembersMessagesOrder {
  String? orderId;
  String? title;
  String? message;
  String? appType;
  String? createDatetime;

  MembersMessagesOrder({
    this.orderId,
    this.title,
    this.message,
    this.appType,
    this.createDatetime,
  });

  MembersMessagesOrder copyWith({
    String? orderId,
    String? title,
    String? message,
    String? appType,
    String? createDatetime,
  }) =>
      MembersMessagesOrder(
        orderId: orderId ?? this.orderId,
        title: title ?? this.title,
        message: message ?? this.message,
        appType: appType ?? this.appType,
        createDatetime: createDatetime ?? this.createDatetime,
      );

  factory MembersMessagesOrder.fromRawJson(String str) =>
      MembersMessagesOrder.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMessagesOrder.fromJson(Map<String, dynamic> json) =>
      MembersMessagesOrder(
        orderId: json["order_id"],
        title: json["title"],
        message: json["message"],
        appType: json["app_type"],
        createDatetime: json["create_datetime"],
      );

  Map<String, dynamic> toJson() => {
        "order_id": orderId,
        "title": title,
        "message": message,
        "app_type": appType,
        "create_datetime": createDatetime,
      };
}
