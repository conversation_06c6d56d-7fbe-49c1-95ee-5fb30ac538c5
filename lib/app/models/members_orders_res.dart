// To parse this JSON data, do
//
//     final membersOrdersRes = membersOrdersResFromJson(jsonString);

import 'dart:convert';

import 'product.dart';

class MembersOrdersRes {
  String? id;
  String? paymentId;
  String? number;
  String? companyName;
  String? vatNumber;
  String? trackingNumber;
  String? buyerName;
  String? buyerGender;
  String? buyerEmail;
  String? buyerCity;
  String? buyerAddress;
  String? receiverName;
  String? receiverGender;
  String? receiverEmail;
  String? receiverCity;
  String? receiverAddress;
  int? shippingFee;
  String? shippingDate;
  int? subtotal;
  int? total;
  String? comment;
  String? paymentStatus;
  String? invoiceRequest;
  String? invoiceVehicle;
  String? buyerZipcode;
  String? receiverZipcode;
  String? paymentName;
  String? storeId;
  String? storeName;
  String? storeAddress;
  String? shippingNumber;
  String? affiliateOrderType;
  String? invoiceNumber;
  String? invoiceDate;
  bool? isRefundAble;
  bool? isCancelAble;
  bool? isRefund;
  bool? isRefundProcessing;
  bool? isCancel;
  bool? isFirstBuy;
  bool? isReAddToCart;
  bool? isProductComment;
  String? tagName;
  String? status;
  String? createDatetime;
  List<Product>? products;

  MembersOrdersRes({
    this.id,
    this.paymentId,
    this.number,
    this.companyName,
    this.vatNumber,
    this.trackingNumber,
    this.buyerName,
    this.buyerGender,
    this.buyerEmail,
    this.buyerCity,
    this.buyerAddress,
    this.receiverName,
    this.receiverGender,
    this.receiverEmail,
    this.receiverCity,
    this.receiverAddress,
    this.shippingFee,
    this.shippingDate,
    this.subtotal,
    this.total,
    this.comment,
    this.paymentStatus,
    this.invoiceRequest,
    this.invoiceVehicle,
    this.buyerZipcode,
    this.receiverZipcode,
    this.paymentName,
    this.storeId,
    this.storeName,
    this.storeAddress,
    this.shippingNumber,
    this.affiliateOrderType,
    this.invoiceNumber,
    this.invoiceDate,
    this.isRefundAble,
    this.isCancelAble,
    this.isRefund,
    this.isRefundProcessing,
    this.isCancel,
    this.isFirstBuy,
    this.isReAddToCart,
    this.isProductComment,
    this.tagName,
    this.status,
    this.createDatetime,
    this.products,
  });

  MembersOrdersRes copyWith({
    String? id,
    String? paymentId,
    String? number,
    String? companyName,
    String? vatNumber,
    String? trackingNumber,
    String? buyerName,
    String? buyerGender,
    String? buyerEmail,
    String? buyerCity,
    String? buyerAddress,
    String? receiverName,
    String? receiverGender,
    String? receiverEmail,
    String? receiverCity,
    String? receiverAddress,
    int? shippingFee,
    String? shippingDate,
    int? subtotal,
    int? total,
    String? comment,
    String? paymentStatus,
    String? invoiceRequest,
    String? invoiceVehicle,
    String? buyerZipcode,
    String? receiverZipcode,
    String? paymentName,
    String? storeId,
    String? storeName,
    String? storeAddress,
    String? shippingNumber,
    String? affiliateOrderType,
    String? invoiceNumber,
    String? invoiceDate,
    bool? isRefundAble,
    bool? isCancelAble,
    bool? isRefund,
    bool? isRefundProcessing,
    bool? isCancel,
    bool? isFirstBuy,
    bool? isReAddToCart,
    bool? isProductComment,
    String? tagName,
    String? status,
    String? createDatetime,
    List<Product>? products,
  }) =>
      MembersOrdersRes(
        id: id ?? this.id,
        paymentId: paymentId ?? this.paymentId,
        number: number ?? this.number,
        companyName: companyName ?? this.companyName,
        vatNumber: vatNumber ?? this.vatNumber,
        trackingNumber: trackingNumber ?? this.trackingNumber,
        buyerName: buyerName ?? this.buyerName,
        buyerGender: buyerGender ?? this.buyerGender,
        buyerEmail: buyerEmail ?? this.buyerEmail,
        buyerCity: buyerCity ?? this.buyerCity,
        buyerAddress: buyerAddress ?? this.buyerAddress,
        receiverName: receiverName ?? this.receiverName,
        receiverGender: receiverGender ?? this.receiverGender,
        receiverEmail: receiverEmail ?? this.receiverEmail,
        receiverCity: receiverCity ?? this.receiverCity,
        receiverAddress: receiverAddress ?? this.receiverAddress,
        shippingFee: shippingFee ?? this.shippingFee,
        shippingDate: shippingDate ?? this.shippingDate,
        subtotal: subtotal ?? this.subtotal,
        total: total ?? this.total,
        comment: comment ?? this.comment,
        paymentStatus: paymentStatus ?? this.paymentStatus,
        invoiceRequest: invoiceRequest ?? this.invoiceRequest,
        invoiceVehicle: invoiceVehicle ?? this.invoiceVehicle,
        buyerZipcode: buyerZipcode ?? this.buyerZipcode,
        receiverZipcode: receiverZipcode ?? this.receiverZipcode,
        paymentName: paymentName ?? this.paymentName,
        storeId: storeId ?? this.storeId,
        storeName: storeName ?? this.storeName,
        storeAddress: storeAddress ?? this.storeAddress,
        shippingNumber: shippingNumber ?? this.shippingNumber,
        affiliateOrderType: affiliateOrderType ?? this.affiliateOrderType,
        invoiceNumber: invoiceNumber ?? this.invoiceNumber,
        invoiceDate: invoiceDate ?? this.invoiceDate,
        isRefundAble: isRefundAble ?? this.isRefundAble,
        isCancelAble: isCancelAble ?? this.isCancelAble,
        isRefund: isRefund ?? this.isRefund,
        isRefundProcessing: isRefundProcessing ?? this.isRefundProcessing,
        isCancel: isCancel ?? this.isCancel,
        isFirstBuy: isFirstBuy ?? this.isFirstBuy,
        isReAddToCart: isReAddToCart ?? this.isReAddToCart,
        isProductComment: isProductComment ?? this.isProductComment,
        tagName: tagName ?? this.tagName,
        status: status ?? this.status,
        createDatetime: createDatetime ?? this.createDatetime,
        products: products ?? this.products,
      );

  factory MembersOrdersRes.fromRawJson(String str) =>
      MembersOrdersRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersOrdersRes.fromJson(Map<String, dynamic> json) =>
      MembersOrdersRes(
        id: json["id"],
        paymentId: json["payment_id"],
        number: json["number"],
        companyName: json["company_name"],
        vatNumber: json["vat_number"],
        trackingNumber: json["tracking_number"],
        buyerName: json["buyer_name"],
        buyerGender: json["buyer_gender"],
        buyerEmail: json["buyer_email"],
        buyerCity: json["buyer_city"],
        buyerAddress: json["buyer_address"],
        receiverName: json["receiver_name"],
        receiverGender: json["receiver_gender"],
        receiverEmail: json["receiver_email"],
        receiverCity: json["receiver_city"],
        receiverAddress: json["receiver_address"],
        shippingFee: json["shipping_fee"],
        shippingDate: json["shipping_date"],
        subtotal: json["subtotal"],
        total: json["total"],
        comment: json["comment"],
        paymentStatus: json["payment_status"],
        invoiceRequest: json["invoice_request"],
        invoiceVehicle: json["invoice_vehicle"],
        buyerZipcode: json["buyer_zipcode"],
        receiverZipcode: json["receiver_zipcode"],
        paymentName: json["payment_name"],
        storeId: json["store_id"],
        storeName: json["store_name"],
        storeAddress: json["store_address"],
        shippingNumber: json["shipping_number"],
        affiliateOrderType: json["affiliate_order_type"],
        invoiceNumber: '${json["invoice_number"]}',
        invoiceDate: json["invoice_date"],
        isRefundAble: json["is_refund_able"],
        isCancelAble: json["is_cancel_able"],
        isRefund: json["is_refund"],
        isRefundProcessing: json["is_refund_processing"],
        isCancel: json["is_cancel"],
        isFirstBuy: json["is_first_buy"],
        isReAddToCart: json["is_re_add_to_cart"],
        isProductComment: json["is_product_comment"],
        tagName: json["tag_name"],
        status: json["status"],
        createDatetime: json["create_datetime"],
        products: json["products"] == null
            ? []
            : List<Product>.from(
                json["products"]!.map((x) => Product.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "payment_id": paymentId,
        "number": number,
        "company_name": companyName,
        "vat_number": vatNumber,
        "tracking_number": trackingNumber,
        "buyer_name": buyerName,
        "buyer_gender": buyerGender,
        "buyer_email": buyerEmail,
        "buyer_city": buyerCity,
        "buyer_address": buyerAddress,
        "receiver_name": receiverName,
        "receiver_gender": receiverGender,
        "receiver_email": receiverEmail,
        "receiver_city": receiverCity,
        "receiver_address": receiverAddress,
        "shipping_fee": shippingFee,
        "shipping_date": shippingDate,
        "subtotal": subtotal,
        "total": total,
        "comment": comment,
        "payment_status": paymentStatus,
        "invoice_request": invoiceRequest,
        "invoice_vehicle": invoiceVehicle,
        "buyer_zipcode": buyerZipcode,
        "receiver_zipcode": receiverZipcode,
        "payment_name": paymentName,
        "store_id": storeId,
        "store_name": storeName,
        "store_address": storeAddress,
        "shipping_number": shippingNumber,
        "affiliate_order_type": affiliateOrderType,
        "invoice_number": invoiceNumber,
        "invoice_date": invoiceDate,
        "is_refund_able": isRefundAble,
        "is_cancel_able": isCancelAble,
        "is_refund": isRefund,
        "is_refund_processing": isRefundProcessing,
        "is_cancel": isCancel,
        "is_first_buy": isFirstBuy,
        "is_re_add_to_cart": isReAddToCart,
        "is_product_comment": isProductComment,
        "tag_name": tagName,
        "status": status,
        "create_datetime": createDatetime,
        "products": products == null
            ? []
            : List<dynamic>.from(products!.map((x) => x.toJson())),
      };
}
