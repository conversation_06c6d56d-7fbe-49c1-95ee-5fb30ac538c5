// To parse this JSON data, do
//
//     final membersMessagesPreferenceRes = membersMessagesPreferenceResFromJson(jsonString);

import 'dart:convert';

class MembersMessagesPreferenceRes {
  bool? status;
  String? message;
  bool? isAllowNotification;

  MembersMessagesPreferenceRes({
    this.status,
    this.message,
    this.isAllowNotification,
  });

  MembersMessagesPreferenceRes copyWith({
    bool? status,
    String? message,
    bool? isAllowNotification,
  }) =>
      MembersMessagesPreferenceRes(
        status: status ?? this.status,
        message: message ?? this.message,
        isAllowNotification: isAllowNotification ?? this.isAllowNotification,
      );

  factory MembersMessagesPreferenceRes.fromRawJson(String str) =>
      MembersMessagesPreferenceRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersMessagesPreferenceRes.fromJson(Map<String, dynamic> json) =>
      MembersMessagesPreferenceRes(
        status: json["status"],
        message: json["message"],
        isAllowNotification: json["is_allow_notification"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "is_allow_notification": isAllowNotification,
      };
}
