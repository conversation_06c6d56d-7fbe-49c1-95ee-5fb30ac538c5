import 'dart:convert';

import 'thumbnail.dart';
import 'url.dart';

class App {
  String? name;
  String? type;
  Thumbnail? thumbnail;
  String? videoUrl;
  Url? url;
  int? order;

  App({
    this.name,
    this.type,
    this.thumbnail,
    this.videoUrl,
    this.url,
    this.order,
  });

  App copyWith({
    String? name,
    String? type,
    Thumbnail? thumbnail,
    String? videoUrl,
    Url? url,
    int? order,
  }) =>
      App(
        name: name ?? this.name,
        type: type ?? this.type,
        thumbnail: thumbnail ?? this.thumbnail,
        videoUrl: videoUrl ?? this.videoUrl,
        url: url ?? this.url,
        order: order ?? this.order,
      );

  factory App.fromRawJson(String str) => App.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory App.fromJson(Map<String, dynamic> json) => App(
        name: json["name"],
        type: json["type"],
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        videoUrl: json["video_url"],
        url: json["url"] is Map ? Url.fromJson(json["url"]) : null,
        order: json["order"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "type": type,
        "thumbnail": thumbnail?.toJson(),
        "video_url": videoUrl,
        "url": url?.toJson(),
        "order": order,
      };
}
