// To parse this JSON data, do
//
//     final loginAppleReq = loginAppleReqFromJson(jsonString);

import 'dart:convert';

class LoginAppleReq {
  String? accessToken;
  String? uid;
  String? code;

  LoginAppleReq({
    this.accessToken,
    this.uid,
    this.code,
  });

  LoginAppleReq copyWith({
    String? accessToken,
    String? uid,
    String? code,
  }) =>
      LoginAppleReq(
        accessToken: accessToken ?? this.accessToken,
        uid: uid ?? this.uid,
        code: code ?? this.code,
      );

  factory LoginAppleReq.fromRawJson(String str) =>
      LoginAppleReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LoginAppleReq.fromJson(Map<String, dynamic> json) => LoginAppleReq(
        accessToken: json["access_token"],
        uid: json["uid"],
        code: json["code"],
      );

  Map<String, dynamic> toJson() => {
        "access_token": accessToken,
        "uid": uid,
        "code": code,
      };
}
