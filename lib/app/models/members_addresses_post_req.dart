// To parse this JSON data, do
//
//     final membersAddressesPostReq = membersAddressesPostReqFromJson(jsonString);

import 'dart:convert';

class MembersAddressesPostReq {
  bool? isDefault;
  String? receiverName;
  String? storeId;
  String? storeName;
  String? zipcode;
  String? address;

  MembersAddressesPostReq({
    this.isDefault,
    this.receiverName,
    this.storeId,
    this.storeName,
    this.zipcode,
    this.address,
  });

  MembersAddressesPostReq copyWith({
    bool? isDefault,
    String? receiverName,
    String? storeId,
    String? storeName,
    String? zipcode,
    String? address,
  }) =>
      MembersAddressesPostReq(
        isDefault: isDefault ?? this.isDefault,
        receiverName: receiverName ?? this.receiverName,
        storeId: storeId ?? this.storeId,
        storeName: storeName ?? this.storeName,
        zipcode: zipcode ?? this.zipcode,
        address: address ?? this.address,
      );

  factory MembersAddressesPostReq.fromRawJson(String str) =>
      MembersAddressesPostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MembersAddressesPostReq.fromJson(Map<String, dynamic> json) =>
      MembersAddressesPostReq(
        isDefault: json["is_default"],
        receiverName: json["receiver_name"],
        storeId: json["store_id"],
        storeName: json["store_name"],
        zipcode: json["zipcode"],
        address: json["address"],
      );

  Map<String, dynamic> toJson() => {
        "is_default": isDefault,
        "receiver_name": receiverName,
        "store_id": storeId,
        "store_name": storeName,
        "zipcode": zipcode,
        "address": address,
      };
}
