// To parse this JSON data, do
//
//     final devicesCreateRes = devicesCreateResFromJson(jsonString);

import 'dart:convert';

class DevicesCreateRes {
  bool? status;
  String? deviceId;
  String? deviceType;
  String? message;
  String? email;

  DevicesCreateRes({
    this.status,
    this.deviceId,
    this.deviceType,
    this.message,
    this.email,
  });

  DevicesCreateRes copyWith({
    bool? status,
    String? deviceId,
    String? deviceType,
    String? message,
    String? email,
  }) =>
      DevicesCreateRes(
        status: status ?? this.status,
        deviceId: deviceId ?? this.deviceId,
        deviceType: deviceType ?? this.deviceType,
        message: message ?? this.message,
        email: email ?? this.email,
      );

  factory DevicesCreateRes.fromRawJson(String str) =>
      DevicesCreateRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory DevicesCreateRes.fromJson(Map<String, dynamic> json) =>
      DevicesCreateRes(
        status: json["status"],
        deviceId: json["device_id"],
        deviceType: json["device_type"],
        message: json["message"],
        email: json["email"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "device_id": deviceId,
        "device_type": deviceType,
        "message": message,
        "email": email,
      };
}
