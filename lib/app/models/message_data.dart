import 'dart:convert';

class MessageData {
  String? iconPath;
  String? titleText;
  String? subtitleText;
  String? path;
  num? badgeCount;
  String? createDatetime;
  String? replyDatetime;
  String? id;

  MessageData({
    this.iconPath,
    this.titleText,
    this.subtitleText,
    this.path,
    this.badgeCount,
    this.createDatetime,
    this.replyDatetime,
    this.id,
  });

  MessageData copyWith({
    String? iconPath,
    String? titleText,
    String? subtitleText,
    String? path,
    num? badgeCount,
    String? createDatetime,
    String? replyDatetime,
    String? id,
  }) =>
      MessageData(
        iconPath: iconPath ?? this.iconPath,
        titleText: titleText ?? this.titleText,
        subtitleText: subtitleText ?? this.subtitleText,
        path: path ?? this.path,
        badgeCount: badgeCount ?? this.badgeCount,
        createDatetime: createDatetime ?? this.createDatetime,
        replyDatetime: replyDatetime ?? this.replyDatetime,
        id: id ?? this.id,
      );

  factory MessageData.fromRawJson(String str) =>
      MessageData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MessageData.fromJson(Map<String, dynamic> json) => MessageData(
        iconPath: json["icon_path"],
        titleText: json["title_text"],
        subtitleText: json["subtitle_text"],
        path: json["path"],
        badgeCount: json["badge_count"],
        createDatetime: json["create_datetime"],
        replyDatetime: json["reply_datetime"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "icon_path": iconPath,
        "title_text": titleText,
        "subtitle_text": subtitleText,
        "path": path,
        "badge_count": badgeCount,
        "create_datetime": createDatetime,
        "reply_datetime": replyDatetime,
        "id": id,
      };
}
