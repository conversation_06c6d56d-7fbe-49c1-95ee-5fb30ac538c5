import 'dart:convert';

class AppierRegistrationCompletedReq {
  String? email;
  String? phoneNo;
  String? userId;
  num? points;
  String? userName;
  String? gender;
  String? birthday;
  num? profileUpdateDate;

  AppierRegistrationCompletedReq({
    this.email,
    this.phoneNo,
    this.userId,
    this.points,
    this.userName,
    this.gender,
    this.birthday,
    this.profileUpdateDate,
  });

  AppierRegistrationCompletedReq copyWith({
    String? email,
    String? phoneNo,
    String? userId,
    num? points,
    String? userName,
    String? gender,
    String? birthday,
    num? profileUpdateDate,
  }) =>
      AppierRegistrationCompletedReq(
        email: email ?? this.email,
        phoneNo: phoneNo ?? this.phoneNo,
        userId: userId ?? this.userId,
        points: points ?? this.points,
        userName: userName ?? this.userName,
        gender: gender ?? this.gender,
        birthday: birthday ?? this.birthday,
        profileUpdateDate: profileUpdateDate ?? this.profileUpdateDate,
      );

  factory AppierRegistrationCompletedReq.fromRawJson(String str) =>
      AppierRegistrationCompletedReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppierRegistrationCompletedReq.fromJson(Map<String, dynamic> json) =>
      AppierRegistrationCompletedReq(
        email: json["email"],
        phoneNo: json["phoneNo"],
        userId: json["user_id"],
        points: json["points"],
        userName: json["user_name"],
        gender: json["gender"],
        birthday: json["birthday"],
        profileUpdateDate: json["profile_update_date"],
      );

  Map<String, dynamic> toJson() => {
        "email": email,
        "phoneNo": phoneNo,
        "user_id": userId,
        "points": points,
        "user_name": userName,
        "gender": gender,
        "birthday": birthday,
        "profile_update_date": profileUpdateDate,
      };
}
