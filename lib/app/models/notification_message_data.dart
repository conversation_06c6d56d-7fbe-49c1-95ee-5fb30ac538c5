import 'dart:convert';

class NotificationMessageData {
  String? sound;
  String? icon;
  String? body;
  String? title;
  String? parameters;

  NotificationMessageData({
    this.sound,
    this.icon,
    this.body,
    this.title,
    this.parameters,
  });

  NotificationMessageData copyWith({
    String? sound,
    String? icon,
    String? body,
    String? title,
    String? parameters,
  }) =>
      NotificationMessageData(
        sound: sound ?? this.sound,
        icon: icon ?? this.icon,
        body: body ?? this.body,
        title: title ?? this.title,
        parameters: parameters ?? this.parameters,
      );

  factory NotificationMessageData.fromRawJson(String str) =>
      NotificationMessageData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory NotificationMessageData.fromJson(Map<String, dynamic> json) =>
      NotificationMessageData(
        sound: json["sound"],
        icon: json["icon"],
        body: json["body"],
        title: json["title"],
        parameters: json["parameters"],
      );

  Map<String, dynamic> toJson() => {
        "sound": sound,
        "icon": icon,
        "body": body,
        "title": title,
        "parameters": parameters,
      };
}
