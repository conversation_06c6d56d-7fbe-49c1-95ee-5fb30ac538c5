// To parse this JSON data, do
//
//     final promotionsAddOnIdsRes = promotionsAddOnIdsResFromJson(jsonString);

import 'dart:convert';

class PromotionsAddOnIdsRes {
  String? id;
  String? categoryName;
  String? totalPrice;

  PromotionsAddOnIdsRes({
    this.id,
    this.categoryName,
    this.totalPrice,
  });

  PromotionsAddOnIdsRes copyWith({
    String? id,
    String? categoryName,
    String? totalPrice,
  }) =>
      PromotionsAddOnIdsRes(
        id: id ?? this.id,
        categoryName: categoryName ?? this.categoryName,
        totalPrice: totalPrice ?? this.totalPrice,
      );

  factory PromotionsAddOnIdsRes.fromRawJson(String str) =>
      PromotionsAddOnIdsRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PromotionsAddOnIdsRes.fromJson(Map<String, dynamic> json) =>
      PromotionsAddOnIdsRes(
        id: json["id"],
        categoryName: json["category_name"],
        totalPrice: json["total_price"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "category_name": categoryName,
        "total_price": totalPrice,
      };
}
