// To parse this JSON data, do
//
//     final faqRes = faqResFromJson(jsonString);

import 'dart:convert';

class FaqRes {
  String? id;
  String? subject;
  String? name;
  String? description;
  String? pin;

  FaqRes({
    this.id,
    this.subject,
    this.name,
    this.description,
    this.pin,
  });

  FaqRes copyWith({
    String? id,
    String? subject,
    String? name,
    String? description,
    String? pin,
  }) =>
      FaqRes(
        id: id ?? this.id,
        subject: subject ?? this.subject,
        name: name ?? this.name,
        description: description ?? this.description,
        pin: pin ?? this.pin,
      );

  factory FaqRes.fromRawJson(String str) => FaqRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FaqRes.fromJson(Map<String, dynamic> json) => FaqRes(
        id: json["id"],
        subject: json["subject"],
        name: json["name"],
        description: json["description"],
        pin: json["pin"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "subject": subject,
        "name": name,
        "description": description,
        "pin": pin,
      };
}
