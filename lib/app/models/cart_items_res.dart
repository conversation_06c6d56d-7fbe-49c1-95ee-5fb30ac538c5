// To parse this JSON data, do
//
//     final CartItemsRes = CartItemsResFromJson(jsonString);

import 'dart:convert';

import 'thumbnail.dart';

class CartItemsRes {
  List<Item>? items;
  Fees? fees;

  CartItemsRes({
    this.items,
    this.fees,
  });

  CartItemsRes copyWith({
    List<Item>? items,
    Fees? fees,
  }) =>
      CartItemsRes(
        items: items ?? this.items,
        fees: fees ?? this.fees,
      );

  factory CartItemsRes.fromRawJson(String str) =>
      CartItemsRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CartItemsRes.fromJson(Map<String, dynamic> json) =>
      CartItemsRes(
        items: json["items"] == null
            ? []
            : List<Item>.from(json["items"]!.map((x) => Item.fromJson(x))),
        fees: json["fees"] == null ? null : Fees.fromJson(json["fees"]),
      );

  Map<String, dynamic> toJson() => {
        "items": items == null
            ? []
            : List<dynamic>.from(items!.map((x) => x.toJson())),
        "fees": fees?.toJson(),
      };
}

class Fees {
  int? shippingFee;
  int? total;

  Fees({
    this.shippingFee,
    this.total,
  });

  Fees copyWith({
    int? shippingFee,
    int? total,
  }) =>
      Fees(
        shippingFee: shippingFee ?? this.shippingFee,
        total: total ?? this.total,
      );

  factory Fees.fromRawJson(String str) => Fees.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Fees.fromJson(Map<String, dynamic> json) => Fees(
        shippingFee: json["shipping_fee"],
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "shipping_fee": shippingFee,
        "total": total,
      };
}

class Item {
  int? productId;
  String? productNumber;
  String? name;
  Thumbnail? thumbnail;
  int? price;
  bool? isSevenAcquire;
  String? color;
  String? size;
  String? specName;
  int? availableQuantity;
  int? cartQuantity;
  int? shippingQuantity;
  bool? isRatio;
  int? subtotal;
  bool? isPromotionMustMatch;
  int? originalPromotionQuantity;
  bool? hasPromotion;
  bool? isDiscount;
  int? promotionId;
  String? promotionName;
  String? promotionType;
  Thumbnail? rectangleImage;
  Thumbnail? squareImage;

  Item({
    this.productId,
    this.productNumber,
    this.name,
    this.thumbnail,
    this.price,
    this.isSevenAcquire,
    this.color,
    this.size,
    this.specName,
    this.availableQuantity,
    this.cartQuantity,
    this.shippingQuantity,
    this.isRatio,
    this.subtotal,
    this.isPromotionMustMatch,
    this.originalPromotionQuantity,
    this.hasPromotion,
    this.isDiscount,
    this.promotionId,
    this.promotionName,
    this.promotionType,
    this.rectangleImage,
    this.squareImage,
  });

  Item copyWith({
    int? productId,
    String? productNumber,
    String? name,
    Thumbnail? thumbnail,
    int? price,
    bool? isSevenAcquire,
    String? color,
    String? size,
    String? specName,
    int? availableQuantity,
    int? cartQuantity,
    int? shippingQuantity,
    bool? isRatio,
    int? subtotal,
    bool? isPromotionMustMatch,
    int? originalPromotionQuantity,
    bool? hasPromotion,
    bool? isDiscount,
    int? promotionId,
    String? promotionName,
    String? promotionType,
    Thumbnail? rectangleImage,
    Thumbnail? squareImage,
  }) =>
      Item(
        productId: productId ?? this.productId,
        productNumber: productNumber ?? this.productNumber,
        name: name ?? this.name,
        thumbnail: thumbnail ?? this.thumbnail,
        price: price ?? this.price,
        isSevenAcquire: isSevenAcquire ?? this.isSevenAcquire,
        color: color ?? this.color,
        size: size ?? this.size,
        specName: specName ?? this.specName,
        availableQuantity: availableQuantity ?? this.availableQuantity,
        cartQuantity: cartQuantity ?? this.cartQuantity,
        shippingQuantity: shippingQuantity ?? this.shippingQuantity,
        isRatio: isRatio ?? this.isRatio,
        subtotal: subtotal ?? this.subtotal,
        isPromotionMustMatch: isPromotionMustMatch ?? this.isPromotionMustMatch,
        originalPromotionQuantity:
            originalPromotionQuantity ?? this.originalPromotionQuantity,
        hasPromotion: hasPromotion ?? this.hasPromotion,
        isDiscount: isDiscount ?? this.isDiscount,
        promotionId: promotionId ?? this.promotionId,
        promotionName: promotionName ?? this.promotionName,
        promotionType: promotionType ?? this.promotionType,
        rectangleImage: rectangleImage ?? this.rectangleImage,
        squareImage: squareImage ?? this.squareImage,
      );

  factory Item.fromRawJson(String str) => Item.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Item.fromJson(Map<String, dynamic> json) => Item(
        productId: json["product_id"],
        productNumber: json["product_number"],
        name: json["name"],
        thumbnail: json["thumbnail"] == null
            ? null
            : Thumbnail.fromJson(json["thumbnail"]),
        price: json["price"],
        isSevenAcquire: json["is_seven_acquire"],
        color: json["color"],
        size: json["size"],
        specName: json["spec_name"],
        availableQuantity: json["available_quantity"],
        cartQuantity: json["cart_quantity"],
        shippingQuantity: json["shipping_quantity"],
        isRatio: json["is_ratio"],
        subtotal: json["subtotal"],
        isPromotionMustMatch: json["is_promotion_must_match"],
        originalPromotionQuantity: json["original_promotion_quantity"],
        hasPromotion: json["has_promotion"],
        isDiscount: json["is_discount"],
        promotionId: json["promotion_id"],
        promotionName: json["promotion_name"],
        promotionType: json["promotion_type"],
        rectangleImage: json["rectangle_image"] == null
            ? null
            : Thumbnail.fromJson(json["rectangle_image"]),
        squareImage: json["square_image"] == null
            ? null
            : Thumbnail.fromJson(json["square_image"]),
      );

  Map<String, dynamic> toJson() => {
        "product_id": productId,
        "product_number": productNumber,
        "name": name,
        "thumbnail": thumbnail?.toJson(),
        "price": price,
        "is_seven_acquire": isSevenAcquire,
        "color": color,
        "size": size,
        "spec_name": specName,
        "available_quantity": availableQuantity,
        "cart_quantity": cartQuantity,
        "shipping_quantity": shippingQuantity,
        "is_ratio": isRatio,
        "subtotal": subtotal,
        "is_promotion_must_match": isPromotionMustMatch,
        "original_promotion_quantity": originalPromotionQuantity,
        "has_promotion": hasPromotion,
        "is_discount": isDiscount,
        "promotion_id": promotionId,
        "promotion_name": promotionName,
        "promotion_type": promotionType,
        "rectangle_image": rectangleImage?.toJson(),
        "square_image": squareImage?.toJson(),
      };
}
