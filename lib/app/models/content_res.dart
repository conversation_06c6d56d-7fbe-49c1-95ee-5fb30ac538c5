// To parse this JSON data, do
//
//     final contentRes = contentResFromJson(jsonString);

import 'dart:convert';

import 'seo.dart';

class ContentRes {
  String? name;
  String? description;
  String? isHeaderFooterVisible;
  Seo? seo;

  ContentRes({
    this.name,
    this.description,
    this.isHeaderFooterVisible,
    this.seo,
  });

  ContentRes copyWith({
    String? name,
    String? description,
    String? isHeaderFooterVisible,
    Seo? seo,
  }) =>
      ContentRes(
        name: name ?? this.name,
        description: description ?? this.description,
        isHeaderFooterVisible:
            isHeaderFooterVisible ?? this.isHeaderFooterVisible,
        seo: seo ?? this.seo,
      );

  factory ContentRes.fromRawJson(String str) =>
      ContentRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ContentRes.fromJson(Map<String, dynamic> json) => ContentRes(
        name: json["name"],
        description: json["description"],
        isHeaderFooterVisible: json["is_header_footer_visible"],
        seo: json["seo"] == null ? null : Seo.fromJson(json["seo"]),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "description": description,
        "is_header_footer_visible": isHeaderFooterVisible,
        "seo": seo?.toJson(),
      };
}
