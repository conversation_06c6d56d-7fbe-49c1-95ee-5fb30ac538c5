import 'dart:convert';
import 'dart:math';

import 'package:efshop/app/components/refund_item.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/refund_controller.dart';

class RefundView extends GetView<RefundController> {
  const RefundView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('申請退貨'),
        centerTitle: true,
        actions: _actions().toList(growable: false),
      ),
      body: controller.obx((state) {
        return Obx(() => _body());
      }),
    );
  }

  Iterable<Widget> _actions() sync* {
    yield Obx(() {
      return Visibility(
        visible: controller.data.needBankInfoWhenRefund,
        child: _nextStepButton(),
      );
    });
  }

  Future<void> _onNextStepPressed() async {
    final refundProducts = controller.refundProducts;
    if (refundProducts.isEmpty) {
      await Get.showAlert('請選擇退貨商品');
      return;
    }

    // 若選擇的商品其 refundProductName 全部為「未逹滿額贈，請勾選」，則跳出提示
    final selectedIds = refundProducts.map((e) => e.productId).toSet();
    final selectedProducts = controller.data.normalProducts
        .where((p) => selectedIds.contains(p.productId));
    final allAreGiftOnly = selectedProducts.isNotEmpty &&
        selectedProducts
            .every((p) => (p.refundProductName ?? '').contains('未逹滿額贈，請勾選'));
    if (allAreGiftOnly) {
      await Get.showAlert('贈品需與其他商品一同退貨\n請先勾選其它商品');
      return;
    }

    await Get.toNamed(Routes.BANK, parameters: {
      Keys.id: controller.id,
      Keys.data: jsonEncode(refundProducts.toList(growable: false)),
    });
  }

  Widget _nextStepButton() {
    return TextButton(
      onPressed: _onNextStepPressed,
      child: const Text(
        '下一步',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray98,
        ),
        textAlign: TextAlign.right,
        softWrap: false,
      ),
    );
  }

  Widget _body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield _list().expanded();
    yield Visibility(
      visible: !controller.data.needBankInfoWhenRefund,
      child: ColoredBox(
        color: Colors.white,
        child: SafeArea(
          child: _bottom(),
        ),
      ),
    );
  }

  Widget _list() {
    final it = controller.products;
    return ListView.separated(
      // padding: EdgeInsets.symmetric(horizontal: 16.dw, vertical: 16.dh),
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        final draft = controller.draft.elementAt(index);
        return RefundItem(
          data,
          draft,
          onTap: () async {
            try {
              final res = await _showPicker();
              if (res != null) {
                draft.refundReason = res;
                controller.refreshData();
              }
            } catch (e) {
              Get.showAlert(e.toString());
            }
          },
          onCheckboxChanged: (value) async {
            if (value == true) {
              try {
                final res = await _showPicker();
                if (res != null) {
                  draft.refundReason = res;
                  draft.numOfQuantity =
                      min(draft.numOfQuantity + 1, data.numOfQuantity);
                  // TODO: 同捆退貨，如果 isBuyNGiftM 為 true
                  // 則所有相同 promotion_id 皆勾選，並且數量固定為最⼤
                  if (data.isBuyNGiftM == true) {
                    draft.numOfQuantity = data.numOfQuantity;
                    for (int i = 0; i < it.length; i++) {
                      final element = it.elementAt(i);
                      if (element.promotionId == data.promotionId) {
                        controller.draft.elementAt(i).refundReason = res;
                        controller.draft.elementAt(i).numOfQuantity =
                            element.numOfQuantity;
                      }
                    }
                  }
                }
              } catch (e) {
                Get.showAlert(e.toString());
              }
            } else if (value == false) {
              draft.refundReason = RefundReason.none;
              draft.numOfQuantity = 0;
              if (data.isBuyNGiftM == true) {
                for (int i = 0; i < it.length; i++) {
                  final element = it.elementAt(i);
                  if (element.promotionId == data.promotionId) {
                    controller.draft.elementAt(i).refundReason =
                        RefundReason.none;
                    controller.draft.elementAt(i).numOfQuantity = 0;
                  }
                }
              }
            }
            controller.refreshData();
          },
          onMinus: () {
            if (draft.numOfQuantity > 0) {
              if (draft.numOfQuantity == 1) {
                draft.refundReason = RefundReason.none;
              }
              draft.numOfQuantity = max(draft.numOfQuantity - 1, 0);
              controller.refreshData();
            }
          },
          onPlus: () async {
            var needUpdate = false;
            if (draft.numOfQuantity == 0) {
              try {
                final res = await _showPicker();
                if (res != null) {
                  draft.refundReason = res;
                  needUpdate = true;
                }
              } catch (e) {
                Get.showAlert(e.toString());
              }
            } else {
              needUpdate = true;
            }
            if (needUpdate) {
              draft.numOfQuantity =
                  min(draft.numOfQuantity + 1, data.numOfQuantity);
              controller.refreshData();
            }
          },
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 10);
      },
      itemCount: it.length,
    );
  }

  Widget _bottom() {
    Iterable<Widget> children() sync* {
      yield ColoredBox(
        color: Colors.white,
        child: CheckboxListTile(
          activeColor: EfColors.primary,
          contentPadding: EdgeInsets.symmetric(horizontal: 12.dw),
          shape: const CircleBorder(),
          value: controller.agreement,
          onChanged: (value) => controller.agreement = value ?? false,
          title: Transform.translate(
            offset: Offset(-16.dw, 0),
            child: Text.rich(
              TextSpan(
                style: const TextStyle(
                  fontSize: 14,
                  color: EfColors.grayTextDark,
                ),
                children: [
                  const TextSpan(
                    text: '已詳閱',
                  ),
                  TextSpan(
                    text: '「退貨注意事項說明」',
                    style: const TextStyle(
                      color: EfColors.gray93,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Get.toNamed(Routes.EF_WEB, parameters: {
                          Keys.title: '退貨注意事項說明',
                          Keys.url: Constants.uriRefund.toString(),
                        });
                      },
                  ),
                ],
              ),
              textHeightBehavior:
                  const TextHeightBehavior(applyHeightToFirstAscent: false),
              softWrap: false,
            ),
          ),
          controlAffinity: ListTileControlAffinity.leading,
        ),
      ).expanded();
      yield SizedBox(
        width: 114.dw,
        height: 55.dh,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            // padding: EdgeInsets.symmetric(vertical: 8.dh, horizontal: 16.dw),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.zero,
            ),
            textStyle: const TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
          ),
          onPressed: controller.agreement ? _submit : null,
          child: const Text(
            '確認退貨',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    return SizedBox(
      height: 55.dh,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Future<void> _submit() async {
    try {
      // 檢查是否有選擇退貨商品
      final needToRefund =
          controller.draft.any((element) => element.numOfQuantity > 0);
      if (needToRefund == false) {
        throw '請選擇退貨商品';
      }
      // 顯示確認視窗
      final res = await Get.showConfirm(
        '即將申請退貨',
        textConfirm: '退貨',
      );
      if (res == Button.confirm) {
        await _submitImpl();
      }
    } catch (e) {
      Get.showAlert(e.toString());
    }
  }

  Future<void> _submitImpl() async {
    Get.showLoading();
    try {
      final res = await controller.submit();
      Get.back(); // 關閉 Loading
      if (res.status != true) {
        throw res.message ?? '退貨失敗';
      }
      Get.back(); // 關閉目前頁面
      Get.toNamed(Routes.REFUND_SUCCESS, parameters: {
        Keys.id: controller.id,
      });
    } catch (e) {
      Get.back(); // 關閉 Loading
      Get.showAlert(e.toString());
    }
  }

  Future<RefundReason?> _showPicker() async {
    final children = RefundReason.values
        .where((element) => element != RefundReason.none)
        .map((element) {
      return ListTile(
        title: Text(element.display),
        onTap: () {
          Get.back(result: element);
        },
      );
    });

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children.toList(growable: false),
    ).sheet<RefundReason>();
  }
}
