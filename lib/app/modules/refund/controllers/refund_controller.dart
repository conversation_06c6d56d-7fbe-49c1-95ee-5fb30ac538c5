import 'dart:async';
import 'dart:convert';

import 'package:efshop/app/models/members_orders_refund_post_req.dart';
import 'package:efshop/app/models/members_orders_refund_product.dart';
import 'package:efshop/app/models/members_orders_res.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/models/product.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
// import 'package:flutter/services.dart' show rootBundle;
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';

class RefundController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  final _id = ''.obs;
  String get id => _id.value;
  final _data = MembersOrdersRes().obs;
  MembersOrdersRes get data => _data.value;
  final draft = <MembersOrdersRefundProduct>[].obs;
  Iterable<Product> get products sync* {
    // 首先處理包含 '未逹滿額贈，請勾選' 文字的產品
    for (var element in data.normalProducts) {
      if ((element.refundProductName ?? '').contains('未逹滿額贈，請勾選')) {
        yield element;
      }
    }
    
    // 再處理其他符合條件的產品
    for (var element in data.normalProducts) {
      if ((element.refundProductName ?? '').contains('未逹滿額贈，請勾選')) {
        // 已經在上面處理過，跳過
        continue;
      }
      if (Constants.discountProduct.contains(element.productId)) {
        // 有折扣商品
        continue;
      }
      if (element.numOfFinalPrice == 0) {
        // 免費商品
        continue;
      }
      yield element;
    }
  }

  Iterable<MembersOrdersRefundProduct> get refundProducts =>
      draft.where((p0) => p0.numOfQuantity > 0);
  final _agreement = false.obs;
  bool get agreement => _agreement.value;
  set agreement(bool value) => _agreement.value = value;

  RefundController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id]!;
    } else {
      onRefresh();
    }
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      // final jsonStr = await rootBundle
      //     .loadString('docs/models/members_orders_id_res_7275693.json');
      // _data.value = MembersOrdersRes.fromRawJson(jsonStr);
      _data.value = await wabowProvider.getMembersOrdersWithId(id);
      final it = products.map((e) => MembersOrdersRefundProduct(
            productId: e.productId,
            promotionId: '${e.promotionId}',
          ));
      draft.assignAll(it);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  void refreshData() {
    _data.refresh();
  }

  Future<MessageRes> submit() async {
    final req = MembersOrdersRefundPostReq(
      products: jsonEncode(refundProducts.toList(growable: false)),
    );
    return wabowProvider.postMembersOrdersRefund(id, req);
  }
}
