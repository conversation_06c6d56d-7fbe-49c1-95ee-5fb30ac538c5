import 'dart:async';

import 'package:efshop/app/models/address_req.dart';
import 'package:efshop/app/models/members_addresses_post_req.dart';
import 'package:efshop/app/models/store_detail.dart';
import 'package:efshop/app/providers/address_provider.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:stream_transform/stream_transform.dart';

class AddressPickerController extends GetxController
    with StateMixin<String>, GetSingleTickerProviderStateMixin {
  final _disposable = Completer();
  final AddressProvider addressProvider;
  WabowProvider get wabowProvider => addressProvider.wabowProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  Logger get logger => wabowProvider.logger;

  final scrollController = ScrollController();
  // late ScrollController _scrollController;
  // set scrollController(ScrollController value) {
  //   _scrollController = value;
  //   // _scrollController.addListener(() {
  //   //   if (_scrollController.position.pixels ==
  //   //       _scrollController.position.maxScrollExtent) {
  //   //     logger.i('reach the bottom');
  //   //   }
  //   // });
  // }

  Iterable<MapEntry> get data sync* {
    if (currentTab == AddressTab.city) {
      yield* addressProvider.getCities();
    } else if (currentTab == AddressTab.town) {
      if (cityName.isNotEmpty) {
        for (final store in addressProvider.getTowns(cityName)) {
          yield MapEntry(store.name, store.code);
        }
      }
    } else if (currentTab == AddressTab.road) {
      if (townName.isNotEmpty) {
        final box = boxProvider.getGsBox(Boxes.road.name);
        for (final key in box.getKeys()) {
          final value = box.read('$key');
          yield MapEntry(key, value);
        }
      }
    } else if (currentTab == AddressTab.store) {
      if (roadName.isNotEmpty) {
        final box = boxProvider.getGsBox(Boxes.road.name);
        if (box.hasData(road.value)) {
          final it = box.read(road.value);
          for (final json in it) {
            final store = StoreDetail.fromJson(json);
            yield MapEntry(store.storeName, store.storeId);
          }
        }
      }
    }
  }

  final _city = const MapEntry(AddressTab.city, '').obs;
  MapEntry<AddressTab, String> get city => _city.value;
  String get cityName => city.value;

  final _town = const MapEntry(AddressTab.town, '').obs;
  MapEntry<AddressTab, String> get town => _town.value;
  String get townName => town.value;

  final _road = const MapEntry(AddressTab.road, '').obs;
  MapEntry<AddressTab, String> get road => _road.value;
  String get roadName => road.value;

  final _store = const MapEntry(AddressTab.store, '').obs;
  MapEntry<AddressTab, String> get store => _store.value;
  String get storeName => store.value;

  Iterable<MapEntry> get tabs sync* {
    yield city;
    yield town;
    // 只有超商才有選擇路名
    if (superMarketList.contains(type)) {
      yield road;
      yield store;
    }
  }

  late TabController _tabController;
  TabController get tabController => _tabController;
  //
  final _type = AddressType.sevenEleven.obs;
  AddressType get type => _type.value;
  //
  final _draft = MembersAddressesPostReq().obs;
  MembersAddressesPostReq get draft => _draft.value;
  //
  final _currentTab = AddressTab.city.obs;
  AddressTab get currentTab => _currentTab.value;
  set currentTab(AddressTab value) {
    _currentTab.value = value;
    if (tabs.isNotEmpty) {
      final safeIndex = value.index.clamp(0, tabs.length - 1);
      tabController.index = safeIndex;
    }
  }

  AddressPickerController({
    required this.addressProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.data)) {
      final req = AddressReq.fromRawJson(Get.parameters[Keys.data] ?? '{}');
      // _action.value = CrudType.values[req.action ?? 0];
      _type.value = AddressType.values.elementAt(req.type ?? 0);
    }
    final tabCount = tabs.isEmpty ? 1 : tabs.length;
    _tabController = TabController(
      vsync: this,
      length: tabCount,
    );
    _town.stream
        .map((event) => addressProvider.getZipCode(cityName, townName))
        .where((event) => event.isNotEmpty)
        .takeUntil(_disposable.future)
        .listen((event) {
      draft.zipcode = event;
      draft.address = cityName + townName;
    });
    _store.stream
        .where((event) => roadName.isNotEmpty && storeName.isNotEmpty)
        .map((event) => addressProvider.getStoreDetail(
            roadName, (element) => element.storeName == storeName))
        .where((event) => event.storeId != null && event.storeId!.isNotEmpty)
        .takeUntil(_disposable.future)
        .listen((storeDetail) {
      draft.storeId = storeDetail.storeId;
      draft.storeName = storeDetail.storeName;
      draft.address = storeDetail.storeAddress;
    });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    tabController.dispose();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      await _fetchAll();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  ///
  /// 取得所有資料
  ///
  Future<void> _fetchAll() async {
    if (type == AddressType.familyMart) {
      await addressProvider.fetchFamilyMartCities();
      await addressProvider.fetchFamilyMartTowns();
    } else {
      await addressProvider.fetchSevenElevenCities();
      await addressProvider.fetchSevenElevenTowns();
    }
  }

  ///
  /// 從選擇的鄉鎮市區取得路名
  ///
  Future<void> _fetchRoads(String zip) async {
    if (type == AddressType.familyMart) {
      await addressProvider.fetchFamilyMartRoads(zip);
    } else {
      await addressProvider.fetchSevenElevenRoads(zip);
    }
  }

  ///
  /// 儲存目前選擇的資料
  ///
  Future<void> save(MapEntry data) async {
    scrollController.jumpTo(0);
    if (currentTab == AddressTab.city) {
      _city.value = MapEntry(city.key, data.key);
      _town.value = MapEntry(town.key, '');
      _road.value = MapEntry(road.key, '');
      _store.value = MapEntry(store.key, '');
      currentTab = AddressTab.town;
    } else if (currentTab == AddressTab.town) {
      final zipCode = data.value as String;
      await _fetchRoads(zipCode);
      _town.value = MapEntry(town.key, data.key);
      _road.value = MapEntry(road.key, '');
      _store.value = MapEntry(store.key, '');
      if (AddressTab.road.index < tabs.length) {
        currentTab = AddressTab.road;
      }
    } else if (currentTab == AddressTab.road) {
      _road.value = MapEntry(road.key, data.key);
      _store.value = MapEntry(store.key, '');
      if (AddressTab.store.index < tabs.length) {
        currentTab = AddressTab.store;
      }
    } else if (currentTab == AddressTab.store) {
      // data.key = store name
      // data.value = store id
      _store.value = MapEntry(store.key, data.key);
    }
  }

  ///
  /// 顯示選擇的資料
  ///
  String getDisplay(AddressTab tab) {
    if (tab == AddressTab.city && city.value.isNotEmpty) {
      return city.value;
    } else if (tab == AddressTab.town && town.value.isNotEmpty) {
      return town.value;
    } else if (tab == AddressTab.road && road.value.isNotEmpty) {
      return road.value;
    } else if (tab == AddressTab.store && store.value.isNotEmpty) {
      return store.value;
    }
    return tab.display;
  }

  bool get isValid {
    if ([AddressType.home].contains(type)) {
      return townName.isNotEmpty;
    }
    if (superMarketList.contains(type)) {
      return storeName.isNotEmpty;
    }
    return false;
  }
}
