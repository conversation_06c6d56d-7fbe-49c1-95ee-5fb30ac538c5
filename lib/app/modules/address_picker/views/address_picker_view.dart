import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/address_picker_controller.dart';

class AddressPickerView extends GetView<AddressPickerController> {
  const AddressPickerView({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: AddressPickerController(
        addressProvider: Get.find(),
      ),
      builder: (controller) {
        return controller.obx(
          (state) {
            return Scaffold(
              appBar: AppBar(
                toolbarHeight: 0,
                title: const Text('AddressPickerView'),
                centerTitle: true,
                bottom: PreferredSize(
                  preferredSize: const Size.fromHeight(38),
                  child: SizedBox(
                    height: 38,
                    child: Obx(() {
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: _tabBar().toList(growable: false),
                      );
                    }),
                  ),
                ),
              ),
              body: Obx(() => _list()),
            );
            // return DraggableScrollableSheet(
            //   maxChildSize: 0.95,
            //   builder: (context, scrollController) {
            //     controller.scrollController = scrollController;
            //     return Scaffold(
            //       appBar: AppBar(
            //         toolbarHeight: 0,
            //         title: const Text('AddressPickerView'),
            //         centerTitle: true,
            //         bottom: PreferredSize(
            //           preferredSize: const Size.fromHeight(38),
            //           child: SizedBox(
            //             height: 38,
            //             child: Obx(() {
            //               return Row(
            //                 mainAxisSize: MainAxisSize.min,
            //                 children: _tabBar().toList(growable: false),
            //               );
            //             }),
            //           ),
            //         ),
            //       ),
            //       body: Obx(() => _list(scrollController)),
            //     );
            //   },
            // );
          },
        );
      },
    );
  }

  Iterable<Widget> _tabBar() sync* {
    yield SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: TabBar(
        onTap: (value) {
          controller.currentTab = AddressTab.values.elementAt(value);
        },
        controller: controller.tabController,
        indicatorColor: EfColors.primary,
        labelPadding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 0,
        ),
        isScrollable: true,
        tabs: _tabs().toList(growable: false),
      ),
    ).expanded();
    yield const SizedBox(width: 8);
    yield TextButton(
      style: TextButton.styleFrom(
        backgroundColor: EfColors.primary,
        foregroundColor: Colors.white,
        disabledForegroundColor: EfColors.gray,
        disabledBackgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(2),
        ),
        textStyle: const TextStyle(
          fontSize: 13,
        ),
      ),
      onPressed: controller.isValid ? _submit : null,
      child: const Text(
        '確定',
        textAlign: TextAlign.center,
      ),
    );
    yield const SizedBox(width: 8);
  }

  void _submit() {
    Get.back(result: controller.draft);
  }

  // TODO: use iterable map
  Iterable<Widget> _tabs() sync* {
    for (final e in controller.tabs) {
      yield Tab(
        iconMargin: EdgeInsets.zero,
        height: 38,
        text: controller.getDisplay(e.key),
      );
    }
  }

  Widget _list() {
    final it = controller.data;
    return ListView.builder(
      controller: controller.scrollController,
      itemCount: controller.data.length,
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return ListTile(
          tileColor: Colors.white,
          title: Text(data.key),
          onTap: () {
            controller.save(data);
          },
        );
      },
      // separatorBuilder: (context, index) {
      //   return const Divider(
      //     height: 1,
      //     thickness: 1,
      //   );
      // },
    );
  }
}
