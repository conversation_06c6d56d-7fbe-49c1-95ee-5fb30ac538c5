import 'package:efshop/app/components/colored_divider.dart';
import 'package:efshop/app/components/price.dart';
import 'package:efshop/app/components/product_item.dart';
import 'package:efshop/app/components/refund_code.dart';
import 'package:efshop/app/components/vertical_processing.dart';
import 'package:efshop/app/models/product.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/refund_detail_controller.dart';

class RefundDetailView extends GetView<RefundDetailController> {
  const RefundDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('退貨詳情'),
        centerTitle: true,
        actions: _actions().toList(growable: false),
      ),
      body: controller.obx((state) {
        return _body();
        // return Obx(() => _body());
      }),
    );
  }

  Iterable<Widget> _actions() sync* {
    yield IconButton(
      onPressed: () => Get.popAll(),
      icon: const Icon(Icons.person),
    );
  }

  Widget _body() {
    return SingleChildScrollView(
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: _children().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield _superMarket();
    yield SizedBox(height: 10.dh);
    yield _progressing();
    yield SizedBox(height: 10.dh);
    yield Obx(() => _bottom());
  }

  Widget _bottom() {
    Iterable<Widget> children() sync* {
      yield ListTile(
        title: const Text(
          '退貨明細',
          style: TextStyle(
            fontSize: 15,
            color: EfColors.gray6B,
          ),
          softWrap: false,
        ),
        trailing: Icon(
          controller.expand ? Icons.expand_more : Icons.chevron_right,
          color: EfColors.gray6B,
        ),
        onTap: () {
          controller.expand = !controller.expand;
        },
      );
      if (controller.expand == true) {
        yield const ColoredDivider();
        final products = controller.data.products ?? [];
        yield ListView.separated(
          itemCount: products.length,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            final src = products.elementAt(index);
            final data = Product.fromJson(src.toJson());
            data.quantity = '${src.refundQuantity}';
            return SizedBox(
              height: 84,
              child: ProductItem(data),
            );
          },
          separatorBuilder: (context, index) {
            return const ColoredDivider();
          },
        );
        yield const ColoredDivider();
        yield _conclusion();
      }
    }

    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _conclusion() {
    Iterable<TableRow> children() sync* {
      final order = controller.data;
      yield TableRow(
        children: [
          // const SizedBox(height: 40),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              '共${order.quantity}件商品',
              style: const TextStyle(
                fontSize: 12,
                color: EfColors.gray94,
              ),
              softWrap: false,
            ),
          ).sizedBox(height: 40),
          const Align(
            alignment: Alignment.centerRight,
            child: Text(
              '金額',
              style: TextStyle(
                fontSize: 12,
                color: EfColors.gray,
              ),
              softWrap: false,
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: order.numOfTotal > 0
                ? Price(order.numOfTotal)
                : const Text(
                    '待核算',
                    style: TextStyle(
                      fontSize: 12,
                      color: EfColors.gray,
                    ),
                  ),
          ),
        ],
      );
      yield TableRow(
        children: [
          const SizedBox(height: 20),
          const Align(
            alignment: Alignment.centerRight,
            child: Text(
              '運費',
              style: TextStyle(
                fontSize: 12,
                color: EfColors.gray,
              ),
              textAlign: TextAlign.right,
              softWrap: false,
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Price(order.shippingFee ?? 0),
          ),
        ],
      );
      yield TableRow(
        children: [
          const SizedBox(height: 40),
          const Align(
            alignment: Alignment.centerRight,
            child: Text(
              '應補退金額',
              style: TextStyle(
                fontSize: 12,
                color: EfColors.gray,
              ),
              softWrap: false,
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Price(order.otherMoney ?? 0),
          ),
        ],
      );
      yield const TableRow(
        children: [
          Divider(height: 1),
          Divider(height: 1),
          Divider(height: 1),
        ],
      );
      yield TableRow(
        children: [
          const Align(
            alignment: Alignment.centerLeft,
            child: Text(
              '退款金額',
              style: TextStyle(
                fontSize: 12,
                color: EfColors.gray94,
              ),
              softWrap: false,
            ),
          ),
          const SizedBox(height: 50),
          Align(
            alignment: Alignment.centerRight,
            child: order.numOfTotal > 0
                ? Price(
                    // 918,
                    order.numOfTotal,
                    color: EfColors.primary,
                  )
                : const Text(
                    '待核算',
                    style: TextStyle(
                      fontSize: 12,
                      color: EfColors.gray,
                    ),
                  ),
          ),
        ],
      );
    }

    return ColoredBox(
      color: Colors.white,
      child: Row(
        children: [
          const Spacer(),
          SizedBox(
            width: 235,
            child: Table(
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              border: TableBorder.all(color: Colors.transparent),
              children: children().toList(growable: false),
            ),
          ),
          const SizedBox(width: 14),
        ],
      ),
    );
  }

  Widget _progressing() {
    Iterable<Widget> children() sync* {
      yield ListTile(
        title: Text(
          '訂單編號：${controller.data.orderNumber}',
          style: const TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          softWrap: false,
        ),
      );
      yield Divider(height: 1.dh);
      // yield Padding(
      //   padding: const EdgeInsets.symmetric(
      //     horizontal: 20,
      //     vertical: 12,
      //   ),
      //   child: SizedBox(
      //     height: 70.dh,
      //     child: VerticalProcessing(
      //       0,
      //       1,
      //       MembersOrdersMessage(
      //         message: '申請退貨',
      //         createDatetime: controller.data.createDatetime ?? '',
      //       ),
      //     ),
      //   ),
      // );
      yield Obx(() {
        final it = controller.processingData;
        return ListView.separated(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) {
            final data = it.elementAt(index);
            return VerticalProcessing(
              index,
              it.length,
              data,
            );
          },
          separatorBuilder: (context, index) {
            return const Divider(
              indent: 20,
              height: 1,
              thickness: 1,
              color: EfColors.grayDD,
            );
          },
          itemCount: it.length,
        ).paddingSymmetric(horizontal: 20);
      });
    }

    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _superMarket() {
    Iterable<Widget> children() sync* {
      yield SizedBox(
        height: 36.dh,
        width: double.infinity,
      );
      yield RefundCode(
        type: controller.data.shippingType,
        // code: 'A55159390535',
        code: controller.data.shippingNumber ?? '',
      );
      yield SizedBox(height: 36.dh);
    }

    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }
}
