import 'dart:async';

import 'package:efshop/app/models/members_orders_message.dart';
import 'package:efshop/app/models/members_orders_refund_res.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';

class RefundDetailController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  final _data = MembersOrdersRefundRes().obs;
  MembersOrdersRefundRes get data => _data.value;
  final _disposable = Completer();
  final _id = ''.obs;
  String get id => _id.value;
  final _expand = false.obs;
  bool get expand => _expand.value;
  set expand(bool value) => _expand.value = value;
  final processingData = <MembersOrdersMessage>[].obs;

  RefundDetailController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id]!;
    }
    // onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      // 取得退貨物流
      final it = await wabowProvider.getMembersOrdersShipsRefunds(id);
      processingData.assignAll(it);
      // 取得退貨訊息
      _data.value = await wabowProvider.getMembersOrdersRefundMessages(id);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
