import 'package:efshop/app/models/appier_category_viewed_req.dart';
import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/providers/appier_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:get/get.dart';

class CategoriesPageController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  AppierProvider get appierProvider => wabowProvider.appierProvider;

  final _parentTab = Category().obs;
  Category get parentTab => _parentTab.value;
  set parentTab(Category value) => _parentTab.value = value;

  Iterable<Category> get tabs => parentTab.subCategories ?? [];

  final _currentTab = Category().obs;
  Category get currentTab => _currentTab.value;
  set currentTab(Category value) => _currentTab.value = value;

  CategoriesPageController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void refreshCurrentTab() {
    _parentTab.refresh();
  }

  Future<void> onRefresh() async {
    try {
      _currentTab.value = tabs.first;
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<void> sendCategoryViewedEvent(Category value) async {
    try {
      await appierProvider.categoryViewed(AppierCategoryViewedReq(
        categoryName: value.name,
      ));
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
