import 'package:efshop/app/components/thumbnail_image.dart';
import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/models/product_detail.dart';
import 'package:efshop/app/models/url.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/categories_page_controller.dart';

// 分類頁面的子分類頁面
class CategoriesPageView extends GetView<CategoriesPageController> {
  final String? tag;

  CategoriesPageView({super.key, Category? parent})
      : tag = parent?.id {
    Get.lazyPut(
      () => CategoriesPageController(
        wabowProvider: Get.find(),
      ),
      tag: tag,
      fenix: true,
    );
    if (parent != null) {
      final controller = Get.find<CategoriesPageController>(tag: tag);
      controller.parentTab = parent;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: Get.find<CategoriesPageController>(tag: tag),
      tag: tag,
      builder: (controller) {
        return ColoredBox(
          color: Colors.white,
          child: controller.obx((state) {
            return Obx(() => _body());
          }),
        );
      },
    );
  }

  Widget _body() {
    return Row(
      children: [
        Expanded(
          flex: 90,
          // child: Obx(() => _tabs()),
          child: _tabs(),
        ),
        const VerticalDivider(
          width: 2,
          thickness: 2,
          color: EfColors.background,
        ),
        Expanded(
          flex: 285,
          // child: Obx(() => _grid()),
          child: _grid(),
        ),
      ],
    );
  }

  Widget _tabs() {
    final it = controller.tabs;
    return ListView.builder(
      itemBuilder: (context, index) {
        final item = it.elementAt(index);
        final selected = controller.currentTab.id == item.id;
        return ListTile(
          dense: true,
          visualDensity: VisualDensity.standard,
          contentPadding: EdgeInsets.zero,
          minLeadingWidth: 0,
          horizontalTitleGap: 8,
          selected: selected,
          leading: VerticalDivider(
            width: 2,
            thickness: 2,
            color: selected ? EfColors.primary : Colors.transparent,
          ),
          textColor: EfColors.gray,
          selectedColor: EfColors.primary,
          title: Text(
            item.name ?? '',
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
          onTap: () {
            controller.currentTab = item;
            controller.sendCategoryViewedEvent(item);
          },
        );
      },
      itemCount: it.length,
    );
  }

  Widget _grid() {
    final currentTab = controller.currentTab;
    final it = currentTab.subCategories ?? [];
    return GridView.builder(
      padding: EdgeInsets.only(
        left: 8.dw,
        right: 8.dw,
        top: 8.dh,
      ),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        childAspectRatio: 80.0 / 164.0,
      ),
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return _Item(
          data.toProductDetail(),
          onPressed: () async {
            try {
              final url = data.url ?? Url();
              final uri = url.uri.replace(queryParameters: {
                ...url.uri.queryParameters,
                Keys.parentId: currentTab.id,
              });
              Get.launchUrl(uri);
            } catch (e) {
              Get.showAlert(e.toString());
            }
          },
        );
      },
      itemCount: it.length,
    );
  }
}

class _Item extends StatelessWidget {
  final ProductDetail data;
  final VoidCallback? onPressed;

  const _Item(
    this.data, {
    super.key,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ThumbnailImage(data.thumbnail),
          SizedBox(height: 8.dh),
          Expanded(
            child: _text(),
          ),
        ],
      ),
    );
  }

  Widget _text() {
    return Text(
      // '七分/長袖',
      data.name?.replaceAll('‧', '\n') ?? '',
      style: const TextStyle(
        fontSize: 14,
        color: EfColors.grayTextLight,
        overflow: TextOverflow.ellipsis,
      ),
      textAlign: TextAlign.center,
      maxLines: 2,
      softWrap: true,
      overflow: TextOverflow.ellipsis,
    );
  }
}
