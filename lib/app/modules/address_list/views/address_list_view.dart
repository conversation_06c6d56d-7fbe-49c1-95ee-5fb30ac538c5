import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/address_list_controller.dart';

class AddressListView extends GetView<AddressListController> {
  const AddressListView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AddressListView'),
        centerTitle: true,
      ),
      body: const Center(
        child: Text(
          'AddressListView is working',
          style: TextStyle(fontSize: 20),
        ),
      ),
    );
  }
}
