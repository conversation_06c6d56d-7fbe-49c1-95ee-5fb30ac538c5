import 'package:efshop/app/components/search_item.dart';
import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/models/url.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';

import '../controllers/search_result_controller.dart';

class SearchResultView extends GetView<SearchResultController> {
  final ValueSetter<Category>? onTap;

  const SearchResultView({
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SearchResultController>(
      init: Get.find<SearchResultController>(),
      builder: (controller) {
        return Scaffold(
          // appBar: AppBar(
          //   title: const Text('SearchResultView'),
          //   centerTitle: true,
          // ),
          body: controller.obx(
            (state) => Obx(() => _body()),
            // onEmpty: Obx(() => _onEmpty()),
          ),
        );
      },
    );
  }

  Widget _body() {
    final it = controller.data;
    if (it.isEmpty) {
      // 沒有資料
      return SizedBox.expand(
        child: _empty(),
      );
    }
    return SafeArea(
      child: GridView.builder(
        key: key,
        padding: EdgeInsets.only(
          left: 8.dw,
          right: 8.dw,
          top: 16.dh,
        ),
        itemCount: it.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // 列數
          mainAxisSpacing: 10.dh, // 垂直間距
          crossAxisSpacing: 12.dw, // 水平間距
          childAspectRatio: SearchItem.aspectRatio,
        ),
        itemBuilder: (BuildContext context, int index) {
          final data = it.elementAt(index);
          // 動態生成子元素
          return SearchItem(
            data,
            onPressed: () {
              final url = data.url ?? Url();
              Get.launchUrl(url.uri);
            },
            wabowProvider: controller.wabowProvider,
          );
        },
      ),
    );
  }

  Widget _empty() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(height: 20);
      yield ListTile(
        tileColor: Colors.transparent,
        title: Text(
          '《查無結果，推薦你》',
          style: TextStyle(
            fontSize: 19,
            color: EfColors.grayAA,
          ),
        ),
      );
      yield ListTile(
        contentPadding: EdgeInsets.only(left: 36.dw, top: 0, bottom: 0),
        horizontalTitleGap: 24,
        visualDensity: VisualDensity.compact,
        tileColor: Colors.transparent,
        leading: SvgPicture.asset(
          'assets/images/search_001.svg',
          width: 20,
          height: 20,
          colorFilter: ColorFilter.mode(EfColors.grayAA, BlendMode.srcIn),
          fit: BoxFit.contain,
        ),
        title: Text(
          '熱門搜尋',
          style: TextStyle(
            fontSize: 19,
            color: EfColors.grayAA,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
      for (var element in controller.keywords) {
        final text = element.name ?? '';
        if (text.isEmpty) {
          continue;
        }
        yield ListTile(
          contentPadding: EdgeInsets.only(left: 36.dw, top: 0, bottom: 0),
          dense: true,
          horizontalTitleGap: 24,
          visualDensity: VisualDensity.compact,
          minVerticalPadding: 0,
          tileColor: Colors.transparent,
          leading: SizedBox(),
          onTap: () {
            onTap?.call(element);
          },
          title: Text(
            text,
            style: const TextStyle(
              fontSize: 17,
              color: EfColors.gray93,
            ),
          ),
        );
      }
    }

    return ListView(
      // crossAxisAlignment: CrossAxisAlignment.start,
      // mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }
}
