import 'package:efshop/app/models/members_messages_ship.dart';
import 'package:efshop/app/providers/message_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/error_type.dart';
import 'package:get/get.dart';

class LogisticController extends GetxController with StateMixin<String> {
  final MessageProvider messageProvider;
  WabowProvider get wabowProvider => messageProvider.wabowProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  final data = <MembersMessagesShip>[].obs;

  LogisticController({required this.messageProvider});

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      if (prefProvider.isNotLogin) {
        throw ErrorType.unauthorized;
      }
      messageProvider.markAsRead(MessageType.ships);
      final res = await wabowProvider.getMembersMessagesShips();
      data.assignAll(res);
      change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
