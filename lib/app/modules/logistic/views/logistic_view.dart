import 'package:efshop/app/components/ef_center.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/components/logistic_item.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/logistic_controller.dart';

class LogisticView extends GetView<LogisticController> {
  const LogisticView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('物流通知'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) {
          return SafeArea(
            child: Obx(() => _body()),
          );
        },
        onEmpty: _empty(),
        onError: (error) {
          return ErrorButton(
            error,
            onTap: controller.onRefresh,
          );
        },
      ),
    );
  }

  Widget _empty() {
    Iterable<Widget> children() sync* {
      yield SvgPicture.asset(
        'assets/images/big_star.svg',
        width: 50,
        height: 46,
        fit: BoxFit.contain,
      );
      yield const SizedBox(height: 20);
      yield const Text(
        '目前沒有訊息',
        style: TextStyle(
          fontSize: 13,
          color: EfColors.gray93,
        ),
        softWrap: false,
      );
    }

    return EfCenter(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _body() {
    final it = controller.data;
    return ListView.separated(
      padding: const EdgeInsets.all(14),
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return LogisticItem(
          data,
          onTap: () {
            Get.toNamed(Routes.ORDER_DETAIL, parameters: {
              Keys.id: data.orderId ?? '',
            });
          },
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 14);
      },
      itemCount: it.length,
    );
  }
}
