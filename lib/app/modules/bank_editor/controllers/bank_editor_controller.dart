import 'package:efshop/app/models/members_refund_post_req.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/bank_provider.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

class BankEditorController extends GetxController with StateMixin<String> {
  final BankProvider bankProvider;
  WabowProvider get wabowProvider => bankProvider.wabowProvider;
  CrudType get action => id.isNotEmpty ? CrudType.update : CrudType.create;
  final _id = ''.obs;
  String get id => _id.value;
  final _draft = MembersRefundPostReq().obs;
  MembersRefundPostReq get draft => _draft.value;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  final TextEditingController accountNumberEditing = TextEditingController();
  final TextEditingController accountNameEditing = TextEditingController();
  final TextEditingController bankCodeEditing = TextEditingController();
  final TextEditingController bankBranchEditing = TextEditingController();

  BankEditorController({
    required this.bankProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id]!;
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      if (id.isNotEmpty) {
        final data = bankProvider.getBank(id);
        if (data.id != null) {
          _draft.value = MembersRefundPostReq.fromJson(data.toJson());
          accountNumberEditing.text = draft.accountNumber ?? '';
          accountNameEditing.text = draft.accountName ?? '';
          bankCodeEditing.text = draft.bankCode ?? '';
          bankBranchEditing.text = draft.bankBranch ?? '';
          draft.accountNumber = null;
          draft.accountName = null;
          draft.bankCode = null;
          draft.bankBranch = null;
        }
      }
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<MessageRes> submit() {
    final future = action == CrudType.create ? _create : _update;
    return future();
  }

  Future<MessageRes> _create() {
    return bankProvider.create(draft);
  }

  Future<MessageRes> _update() async {
    return bankProvider.update(id, draft);
  }
}
