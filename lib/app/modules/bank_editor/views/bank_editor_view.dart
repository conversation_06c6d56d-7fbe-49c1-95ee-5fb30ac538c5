import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/components/input.dart';
import 'package:efshop/app/models/error_res.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/bank_editor_controller.dart';

class BankEditorView extends GetView<BankEditorController> {
  const BankEditorView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text('${controller.action.display}退款帳號')),
        centerTitle: true,
      ),
      body: controller.obx((state) {
        return Background(
          background: _body(),
          child: _bottom(),
        );
      }),
    );
  }

  Widget _bottom() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: ColoredBox(
        color: Colors.white,
        child: <PERSON><PERSON><PERSON>(
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              minimumSize: const Size.fromHeight(48),
              backgroundColor: EfColors.primary,
              textStyle: const TextStyle(
                fontSize: 15,
                color: Colors.white,
              ),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero,
              ),
            ),
            onPressed: _submit,
            child: const Text(
              '確認',
              style: TextStyle(
                fontSize: 15,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _submit() async {
    Get.showLoading();
    try {
      await controller.submit();
      Get.back(); // hide loading
      Get.back(); // pop page
    } on ErrorRes catch (e) {
      Get.back();
      Get.showAlert(e.displayError);
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
  }

  Widget _body() {
    return SizedBox.expand(
      child: SingleChildScrollView(
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            // crossAxisAlignment: CrossAxisAlignment.start,
            children: _children().toList(growable: false),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield Input(
      // initialValue: controller.draft.bankCode,
      controller: controller.bankCodeEditing,
      hintText: '銀行代碼',
      onChanged: (value) => controller.draft.bankCode = value,
      onClear: () {
        controller.bankCodeEditing.clear();
        controller.draft.bankCode = null;
      },
    );
    yield const Divider(height: 1);
    yield Input(
      // initialValue: controller.draft.bankBranch,
      controller: controller.bankBranchEditing,
      hintText: '銀行名稱',
      onChanged: (value) => controller.draft.bankBranch = value,
      onClear: () {
        controller.bankBranchEditing.clear();
        controller.draft.bankBranch = null;
      },
    );
    yield const Divider(height: 1);
    yield Input(
      // initialValue: controller.draft.accountName,
      controller: controller.accountNameEditing,
      hintText: '帳號戶名',
      onChanged: (value) => controller.draft.accountName = value,
      onClear: () {
        controller.accountNameEditing.clear();
        controller.draft.accountName = null;
      },
    );
    yield const Divider(height: 1);
    yield Input(
      // initialValue: controller.draft.accountNumber,
      controller: controller.accountNumberEditing,
      hintText: '帳號(14碼內半形數字, 不含符號、空格)',
      onChanged: (value) => controller.draft.accountNumber = value,
      onClear: () {
        controller.accountNumberEditing.clear();
        controller.draft.accountNumber = null;
      },
    );
  }
}
