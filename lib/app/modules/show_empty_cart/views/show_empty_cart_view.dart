import 'package:efshop/app/components/ef_grid_item.dart';
import 'package:efshop/app/models/product_detail.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/show_empty_cart_controller.dart';

class ShowEmptyCartView extends GetView<ShowEmptyCartController> {
  final AsyncValueSetter<IndexTab>? changeTab;

  ShowEmptyCartView({
    super.key,
    this.changeTab,
    ValueGetter<IndexTab>? getTab,
  }) {
    Get.lazyPut<ShowEmptyCartController>(
      () => ShowEmptyCartController(
        wabowProvider: Get.find(),
        changeTab: changeTab,
        getTab: getTab,
      ),
      fenix: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('購物車'),
        centerTitle: true,
        leading: CloseButton(
          onPressed: () {
            changeTab?.call(IndexTab.category);
          },
        ),
      ),
      body: controller.obx(
        (state) {
          return Obx(() => _body());
        },
      ),
    );
  }

  Widget _body() {
    return CustomScrollView(
      slivers: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    // yield SliverAppBar(
    //   title: const Text('購物車'),
    //   centerTitle: true,
    //   pinned: true,
    //   // floating: true,
    //   // leading: Obx(() {
    //   //   if (controller.prefProvider.cartQuantity > 0) {
    //   //     return BackButton(
    //   //       // onPressed: controller.onBackPressed,
    //   //     );
    //   //   }
    //   //   return const SizedBox();
    //   // }),
    //   // actions: [
    //   //   IconButton(
    //   //     icon: const Icon(Icons.search),
    //   //     onPressed: () {
    //   //       // 跳轉到搜索
    //   //       // changeTab?.call(IndexTab.search);
    //   //     },
    //   //   ),
    //   // ],
    //   leading: CloseButton(
    //     onPressed: () {
    //       // 跳轉到首頁
    //       changeTab?.call(IndexTab.category);
    //     },
    //   ),
    // );
    // yield SliverAppBar(
    //   expandedHeight: 200.0,
    //   floating: false,
    //   pinned: true,
    //   flexibleSpace: FlexibleSpaceBar(
    //     title: Text('可滾動標題'),
    //     background: Image.network(
    //       'https://flutter.dev/images/catalog-widget-placeholder.png',
    //       fit: BoxFit.cover,
    //     ),
    //   ),
    // );
    // yield SliverList(
    //   delegate: SliverChildBuilderDelegate(
    //     (BuildContext context, int index) {
    //       return ListTile(
    //         title: Text('列表項目 $index'),
    //       );
    //     },
    //     childCount: 10, // 列表有 10 個項目
    //   ),
    // );
    yield _emptyCart().sliverBox;
    yield const SizedBox(height: 14).sliverBox;
    yield const Text(
      '熱門商品推薦',
      style: TextStyle(
        fontSize: 13,
        color: EfColors.gray,
        fontWeight: FontWeight.w500,
      ),
      textAlign: TextAlign.center,
      softWrap: false,
    ).sliverBox;
    yield const SizedBox(height: 14).sliverBox;
    // yield _gridView();
    yield _listView();
  }

  Widget _listView() {
    final it = controller.products;
    final length = (it.length ~/ 2);
    return SliverPadding(
      padding: EdgeInsets.only(
        top: 4.dh,
        left: 8.dw,
        right: 8.dw,
        bottom: 16.dh,
      ),
      sliver: SliverList.separated(
        itemBuilder: (context, index) {
          final leftIndex = index * 2;
          final left = it.elementAt(leftIndex);
          final rightIndex = leftIndex + 1;
          final right = rightIndex < it.length
              ? it.elementAt(rightIndex)
              : ProductDetail();
          return EfGridItem(
            left: left,
            right: right,
            wabowProvider: controller.wabowProvider,
          );
        },
        separatorBuilder: (context, index) {
          return SizedBox(height: 10.dh);
        },
        itemCount: length,
      ),
    );
  }

  Widget _emptyCart() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(height: 40);
      yield SvgPicture.asset(
        'assets/images/empty_cart.svg',
        height: 50.dh,
        width: 58.dw,
      );
      yield const SizedBox(height: 18);
      yield const Text(
        '您的購物車中沒有商品',
        style: TextStyle(
          fontSize: 12,
          color: EfColors.grayTextLight,
        ),
        softWrap: false,
      );
      yield const SizedBox(height: 14);
      yield OutlinedButton(
        child: const Text(
          '購物去',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray93,
          ),
          textAlign: TextAlign.center,
        ),
        onPressed: () {
          // 跳轉到分類
          changeTab?.call(IndexTab.category);
        },
      );
      yield const SizedBox(height: 30);
    }

    return ColoredBox(
      color: EfColors.grayF6,
      child: SizedBox(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: children().toList(growable: false),
        ),
      ),
    );
  }
}
