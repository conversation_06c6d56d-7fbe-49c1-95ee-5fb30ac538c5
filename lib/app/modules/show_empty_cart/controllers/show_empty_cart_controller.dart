import 'dart:async';

import 'package:efshop/app/models/categories_id_quantity_req.dart';
import 'package:efshop/app/models/index_res.dart';
import 'package:efshop/app/models/product_detail.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';

class ShowEmptyCartController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  final products = <ProductDetail>[].obs;
  final AsyncValueSetter<IndexTab>? changeTab;
  final ValueGetter<IndexTab>? getTab;

  ShowEmptyCartController({
    required this.wabowProvider,
    this.changeTab,
    this.getTab,
  });

  @override
  void onInit() {
    super.onInit();
    prefProvider.cartQuantityStream
        .where((event) => event > 0 && getTab?.call() == IndexTab.showEmptyCart)
        .takeUntil(_disposable.future)
        .listen((event) {
      if (prefProvider.isLogin) {
        changeTab?.call(IndexTab.cart);
      } else {
        changeTab?.call(IndexTab.home);
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    if (prefProvider.cartQuantity > 0) {
      changeTab?.call(IndexTab.cart);
    } else {
      onRefresh();
    }
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      var categoryId = '21';
      final box =
          boxProvider.getGsBox(Boxes.setting.name, withNamespace: false);
      if (box.hasData(Keys.index)) {
        final indexRes = IndexRes.fromJson(box.read(Keys.index));
        final category = indexRes.menu?.elementAt(1);
        categoryId = category?.categoryId ?? '21';
      }
      final it = await _getFromCategory(categoryId);
      products.assignAll(it);
      change(null, status: RxStatus.success());
    } catch (e) {
      change(null, status: RxStatus.error(e.toString()));
    }
  }

  Future<Iterable<ProductDetail>> _getFromCategory(String event) async {
    final res = await wabowProvider.getCategoriesAppWithIdAndQuantity(
      event,
      Constants.countPerPage,
      CategoriesIdQuantityReq(
        targetPage: 1,
        // sortWay: 'desc', // asc, desc
        // sortField: 'create_date', // sold_total, create_date, price
      ),
    );
    return res.first.items ?? [];
  }
}
