import 'package:efshop/app/components/ef_center.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/models/members_orders_questions_res.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/service_detail_controller.dart';

class ServiceDetailView extends GetView<ServiceDetailController> {
  const ServiceDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('客服紀錄'),
        centerTitle: true,
        actions: _actions().toList(growable: false),
      ),
      body: controller.obx(
        (state) => SafeArea(
          child: Obx(() {
            return _body();
          }),
        ),
        onEmpty: _empty(),
        onError: (error) {
          return ErrorButton(
            error,
            onTap: controller.onRefresh,
          );
        },
      ),
    );
  }

  Iterable<Widget> _actions() sync* {
    final orderId = controller.data.id ?? '';
    if (orderId.isNotEmpty) {
      yield TextButton(
        onPressed: () {
          Get.toNamed(Routes.ORDER_DETAIL, parameters: {
            Keys.id: orderId,
          });
        },
        child: const Text(
          '查看訂單',
          style: TextStyle(
            fontSize: 15,
            color: EfColors.gray98,
          ),
          softWrap: false,
        ).paddingSymmetric(
          horizontal: 16,
          // vertical: 8,
        ),
      );
    }
  }

  Widget _empty() {
    Iterable<Widget> children() sync* {
      yield SvgPicture.asset(
        'assets/images/message.svg',
        fit: BoxFit.contain,
        height: 46.dh,
        width: 56.dw,
      );
      yield SizedBox(height: 20.dh);
      yield Text(
        '目前沒有留言紀錄',
        style: TextStyle(
          fontSize: 13.dsp,
          color: EfColors.gray93,
        ),
        softWrap: false,
      );
    }

    return EfCenter(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _body() {
    final it = controller.list;
    return ListView.separated(
      itemBuilder: (context, index) {
        final i = index % it.length;
        final element = it.elementAt(i);
        return _Item(data: element);
      },
      separatorBuilder: (context, index) {
        return const Divider(height: 12);
      },
      itemCount: it.length,
    );
  }
}

class _Item extends StatelessWidget {
  final MembersOrdersQuestionsRes data;

  const _Item({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield Text(
      // '2023-07-10 15:48:19',
      data.createDatetime ?? '',
      style: const TextStyle(
        fontSize: 12,
        // color: const Color(0xff939393),
        color: EfColors.gray93,
      ),
      softWrap: false,
    );
    yield SizedBox(height: 6);
    yield Text(
      // '測試客服訊息，測試客服訊息，測試客服訊息，測試客服訊息，測試客服訊息，測試客服訊息，測試客服',
      data.question ?? '',
      style: const TextStyle(
        fontSize: 12,
        color: EfColors.gray,
      ),
    );
    yield const Divider(height: 20);
    yield const Text(
      '衣芙客服回覆',
      style: TextStyle(
        fontSize: 12,
        color: EfColors.primary,
      ),
      softWrap: false,
    );
    yield const SizedBox(height: 6);
    yield Text(
      // '2023-07-10 15:48:19',
      data.replyDatetime ?? '',
      style: const TextStyle(
        fontSize: 12,
        // color: const Color(0xff939393),
        color: EfColors.gray93,
      ),
      softWrap: false,
    );
    yield const SizedBox(height: 6);
    yield Text(
      // '已收到回覆，謝謝',
      data.reply ?? '',
      style: const TextStyle(
        fontSize: 12,
        color: EfColors.gray,
      ),
    );
  }
}
