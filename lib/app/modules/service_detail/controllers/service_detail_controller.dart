import 'package:efshop/app/models/members_orders_questions_res.dart';
import 'package:efshop/app/models/message_data.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

class ServiceDetailController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  Talker get talker => wabowProvider.talker;
  final _data = MessageData().obs;
  MessageData get data => _data.value;
  final _list = <MembersOrdersQuestionsRes>[].obs;
  Iterable<MembersOrdersQuestionsRes> get list => _list;

  ServiceDetailController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.data)) {
      final jsonString = Get.parameters[Keys.data]!;
      _data.value = MessageData.fromRawJson(jsonString);
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final res = await wabowProvider.getMembersOrdersQuestions(data.id ?? '');
      _list.assignAll(res);
      change('', status: list.isEmpty ? RxStatus.empty() : RxStatus.success());
    } catch (e, s) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
