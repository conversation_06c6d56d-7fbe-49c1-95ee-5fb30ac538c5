import 'package:efshop/app/routes/app_pages.dart';
import 'package:get/get.dart';

class LoadingController extends GetxController with StateMixin<String> {
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    await 500.milliseconds.delay();
    // 把 Map<String, String?> 转换为 Map<String, String>
    final parameters = <String, String>{};
    for (var entry in Get.parameters.entries) {
      parameters[entry.key] = entry.value ?? '';
    }
    Get.offNamed(Routes.INDEX, parameters: parameters);
  }
}
