import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'index_module_widget.dart';

class TextUrlWidget extends IndexModuleWidget {
  const TextUrlWidget(super.data, {super.key});

  @override
  Widget build(BuildContext context) {
    return _list();
    // return SizedBox(
    //   height: 40.dh,
    //   child: _body(),
    // );
  }

  // Widget _body() {
  //   final it = data.dataList;
  //   return ListView.separated(
  //     padding: EdgeInsets.symmetric(horizontal: 12.dw),
  //     scrollDirection: Axis.horizontal,
  //     itemBuilder: (context, index) {
  //       final element = it.elementAt(index);
  //       return ElevatedButton(
  //         style: ElevatedButton.styleFrom(
  //           shape: RoundedRectangleBorder(
  //             borderRadius: BorderRadius.circular(4.0),
  //           ),
  //           backgroundColor: EfColors.grayF4,
  //           padding: EdgeInsets.symmetric(horizontal: 12.dw),
  //         ),
  //         onPressed: () {
  //           if (Get.canLaunchUrl(element.url?.uri ?? Uri())) {
  //             Get.launchUrl(element.url?.uri ?? Uri());
  //           }
  //         },
  //         child: Text(
  //           element.text ?? '',
  //           style: const TextStyle(
  //             fontSize: 16,
  //             color: EfColors.gray66,
  //           ),
  //           textAlign: TextAlign.center,
  //         ),
  //       );
  //     },
  //     separatorBuilder: (context, index) {
  //       return SizedBox(width: 12.dw);
  //     },
  //     itemCount: it.length,
  //   );
  // }

  Widget _list() {
    final it = data.dataList;
    return SliverList.separated(
      itemCount: it.length,
      itemBuilder: (context, index) {
        final element = it.elementAt(index);
        return ListTile(
          dense: true,
          tileColor: EfColors.grayF3,
          title: Text(
            element.text ?? '',
            style: const TextStyle(
              fontSize: 15,
              color: EfColors.gray55,
            ),
            textAlign: TextAlign.start,
            softWrap: false,
          ),
          onTap: () {
            final uri = it.elementAt(index).url?.uri ?? Uri();
            if (Get.canLaunchUrl(uri)) {
              Get.launchUrl(uri);
            }
          },
        );
      },
      separatorBuilder: (context, index) {
        return divider();
      },
    );
  }

  static Widget divider() {
    return const Divider(
      height: 1,
      color: EfColors.grayDD,
    );
  }
}
