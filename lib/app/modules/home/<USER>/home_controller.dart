import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/models/index_module_app_res.dart';
import 'package:efshop/app/models/index_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:talker_flutter/talker_flutter.dart';

class HomeController extends GetxController
    with StateMixin<String>, GetSingleTickerProviderStateMixin, ScrollMixin {
  final WabowProvider wabowProvider;
  Logger get logger => wabowProvider.logger;
  Talker get talker => wabowProvider.talker;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  final _data = IndexRes().obs;
  IndexRes get data => _data.value;
  Iterable<Category> get tabs => data.menu ?? [];
  Iterable<Category> get banners => data.large ?? [];
  // page controller
  final _pageController = Rx<PageController?>(null);
  PageController get pageController {
    final currentIndex = tabs.isEmpty ? 0 : (tabController?.index ?? 0);
    _pageController.value ??= PageController(
      keepPage: false,
      viewportFraction: 1,
      initialPage: currentIndex,
    );
    final res = _pageController.value!;
    if (res.initialPage != currentIndex) {
      _safeJumpToPage(currentIndex);
    }
    return res;
  }

  // tab controller
  final _tabController = Rx<TabController?>(null);
  TabController? get tabController {
    if (_tabController.value == null) {
      final tabCount = tabs.isEmpty ? 1 : tabs.length;
      _tabController.value = TabController(
        vsync: this,
        length: tabCount,
      );
    }
    return _tabController.value;
  }

  final _bannerIndex = 0.obs;
  int get bannerIndex => _bannerIndex.value;
  set bannerIndex(int value) => _bannerIndex.value = value;
  // view port
  final _viewPort = const Size(1, 1).obs;
  set viewPort(Size value) => _viewPort.value = value;
  Size get viewPort => _viewPort.value;
  // 首頁模組
  final indexModuleAppRes = <String, IndexModuleAppRes>{}.obs;
  // 模組狀態
  final moduleStatus = ''.reactive;

  final _scrollOffset = 0.0.obs;
  bool get cover {
    var offset = _scrollOffset.value;
    if (offset != scroll.offset) {
      offset = scroll.offset;
    }
    return offset != 0;
  }

  bool get snowing {
    return prefProvider.configs.snow?.enable ?? false;
  }

  HomeController({
    required this.wabowProvider,
  });

  // two dimension index
  var twoDimensionIndex = 0;

  @override
  void onInit() {
    super.onInit();
    scroll.addListener(_onScroll);
  }

  void _onScroll() {
    if (scroll.hasClients) {
      _scrollOffset.value = scroll.offset;
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    scroll.removeListener(_onScroll);
    scroll.dispose();
    _pageController.value?.dispose();
    _tabController.value?.dispose();
    super.onClose();
  }

  Future<void> _fetchIndexMobile() async {
    try {
      _data.value = await wabowProvider.getIndexMobile();
      final box =
          boxProvider.getGsBox(Boxes.setting.name, withNamespace: false);
      // save to local storage
      box.write(Keys.index, data.toJson());
    } catch (e, s) {
      talker.error(e.toString(), e, s);
    }
  }

  Future<void> _fetchMenus() async {
    try {
      await wabowProvider.getMenus();
    } catch (e, s) {
      talker.error(e.toString(), e, s);
    }
  }

  bool _fetchIndexFromBox() {
    final box = boxProvider.getGsBox(Boxes.setting.name, withNamespace: false);
    if (box.hasData(Keys.index)) {
      _data.value = IndexRes.fromJson(box.read(Keys.index));
      return true;
    }
    return false;
  }

  Future<void> onRefresh() async {
    try {
      if (_fetchIndexFromBox()) {
        _fetchIndexMobile();
      } else {
        await _fetchIndexMobile();
      }
      
      // Recreate TabController when data changes
      _tabController.value?.dispose();
      _tabController.value = null;
      
      // 取得漢堡選單
      _fetchMenus();
      // 取得首頁模組
      _fetchIndexModule();
      // 取得所有分類SEO
      // _fetchAllCategorySeo();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  // Future<void> _fetchAllCategorySeo() async {
  //   final box = boxProvider.getGsBox(Boxes.categorySeo.name);
  //   final it = tabs.where((element) => element.url?.action == 'category');
  //   for (var tab in it) {
  //     try {
  //       final id = tab.url?.id ?? '';
  //       final res = await wabowProvider.getCategoriesSeo(id);
  //       box.write(id, res.toJson());
  //     } catch (e, s) {
  //       talker.error(e.toString(), e, s);
  //     }
  //   }
  // }

  ///
  /// 取得首頁模組
  ///
  Future<void> _fetchIndexModuleApp() async {
    try {
      final res = await wabowProvider.getIndexModuleApp();
      indexModuleAppRes.assignAll(res);
      moduleStatus.change('', status: RxStatus.success());
      for (final item in res.values) {
        logger.i(item.type);
      }
      _saveIndexModuleToBox();
    } catch (error, stackTrace) {
      logger.e(error.toString(), error: error, stackTrace: stackTrace);
      moduleStatus.change('', status: RxStatus.error(error.toString()));
      await FirebaseCrashlytics.instance.log(error.toString());
      await FirebaseCrashlytics.instance.recordError(
        error,
        stackTrace,
        reason: 'a non-fatal error',
        information: [
          'further diagnostic information about the error',
          'version 3.0'
        ],
      );
    }
  }

  Future<void> _saveIndexModuleToBox() async {
    final box =
        boxProvider.getGsBox(Boxes.indexModule.name, withNamespace: false);
    try {
      await box.erase();
    } catch (e) {
      logger.e(e);
    }
    for (final item in indexModuleAppRes.entries) {
      try {
        await box.write(item.key, item.value.toJson());
      } catch (e) {
        logger.e(e);
      }
    }
  }

  bool _fetchIndexModuleFromBox() {
    // final box =
    //     boxProvider.getGsBox(Boxes.indexModule.name, withNamespace: false);
    // final it = box.getKeys<Iterable<String>>();
    // if (it.isNotEmpty) {
    //   final entries =
    //       it.map((e) => MapEntry(e, IndexModuleAppRes.fromJson(box.read(e))));
    //   indexModuleAppRes.clear();
    //   indexModuleAppRes.addEntries(entries);
    //   moduleStatus.change('', status: RxStatus.success());
    //   return true;
    // }
    return false;
  }

  Future<void> _fetchIndexModule() async {
    if (_fetchIndexModuleFromBox()) {
      _fetchIndexModuleApp();
    } else {
      await _fetchIndexModuleApp();
    }
  }

  @override
  Future<void> onEndScroll() async {}

  @override
  Future<void> onTopScroll() async {}

  void revertPage() {
    try {
      if (status.isSuccess && tabs.isNotEmpty) {
        final index = tabController?.index ?? 0;
        if (index != 0) {
          _safeJumpToPage(index);
        }
      }
    } catch (e, s) {
      talker.error(e.toString(), e, s);
    }
  }

  /// 安全地跳轉到指定頁面，避免 "Bad state: No element" 錯誤
  void _safeJumpToPage(int index) {
    final controller = _pageController.value;
    if (controller == null || tabs.isEmpty) return;
    
    // 使用 addPostFrameCallback 確保在 widget 構建完成後執行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // 檢查 PageController 是否有有效的 position
        if (controller.hasClients && controller.positions.isNotEmpty) {
          final safeIndex = index.clamp(0, tabs.length - 1);
          controller.jumpToPage(safeIndex);
        } else {
          // 如果還沒有 clients，延遲重試
          Future.delayed(const Duration(milliseconds: 100), () {
            _safeJumpToPage(index);
          });
        }
      } catch (e, s) {
        talker.error('Failed to jump to page $index: $e', e, s);
      }
    });
  }
}
