import 'package:efshop/app/components/search_item.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'index_module_widget.dart';

class SlideGridProduct extends IndexModuleWidget {
  final WabowProvider wabowProvider;

  const SlideGridProduct(
    super.data, {
    super.key,
    required this.wabowProvider,
  });

  @override
  Widget build(BuildContext context) {
    final itemWidth = (Get.width - (Constants.gridSpacing * 3.0)) * 0.5;
    return SizedBox(
      height: itemWidth / SearchItem.aspectRatio,
      child: _list(),
    );
  }

  Widget _list() {
    final it = data.dataList;
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      padding: EdgeInsets.symmetric(horizontal: Constants.gridSpacing.dw),
      itemBuilder: (context, index) {
        final element = it.elementAt(index);
        return AspectRatio(
          aspectRatio: SearchItem.aspectRatio,
          child: SearchItem(
            element.toProductDetail(),
            onPressed: () {
              if (Get.canLaunchUrl(element.url?.uri ?? Uri())) {
                Get.launchUrl(element.url?.uri ?? Uri());
              }
            },
            wabowProvider: wabowProvider,
          ),
        );
      },
      separatorBuilder: (context, index) {
        return SizedBox(width: Constants.gridSpacing.dh);
      },
      itemCount: it.length,
    );
  }
}
