import 'dart:developer';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/components/thumbnail_image.dart';
import 'package:efshop/app/models/index_module_app.dart';
import 'package:efshop/app/models/index_module_app_res.dart';
import 'package:efshop/basic.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

class ScrollableArgs {
  final ScrollMetrics metrics;
  final double scrollDelta;

  ScrollableArgs({
    required this.metrics,
    required this.scrollDelta,
  });
}

class TwoDimensions extends StatefulWidget {
  final IndexModuleAppRes data;
  final RxInt _bannerIndex;
  int get bannerIndex => _bannerIndex.value;
  set bannerIndex(int value) => _bannerIndex.value = value;
  final Logger? logger;
  final double aspectRatio;
  final ValueSetter<double>? onOverscroll;
  final Predicate<ScrollableArgs>? scrollable;
  final ValueChanged<int>? onPageChanged;

  TwoDimensions(
    this.data, {
    super.key,
    this.logger,
    this.aspectRatio = 1.0,
    this.onOverscroll,
    this.scrollable,
    this.onPageChanged,
    int pageIndex = 0,
  }) : _bannerIndex = pageIndex.obs;

  @override
  State<TwoDimensions> createState() => _TwoDimensionsState();
}

class _TwoDimensionsState extends State<TwoDimensions> {
  // page controller
  final _pageController = Rx<PageController?>(null);
  PageController get pageController {
    final currentIndex = widget.bannerIndex;
    _pageController.value ??= PageController(
      initialPage: currentIndex,
      keepPage: false,
    );
    final res = _pageController.value!;
    if (res.initialPage != currentIndex) {
      Future(() => res.jumpToPage(currentIndex));
    }
    return res;
  }

  // dispose
  @override
  void dispose() {
    _pageController.value?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final it = widget.data.dataMap.entries;
    return Background(
      background: _background(it),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Obx(() {
          return DotsIndicator(
            axis: Axis.vertical,
            dotsCount: it.length,
            position: widget.bannerIndex,
          );
        }),
      ),
    );
  }

  Widget _background(Iterable<MapEntry<String, Iterable<IndexModuleApp>>> it) {
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        // logger?.i('[TwoDimensions] notification: $notification');
        final metrics = notification.metrics;
        if (metrics.axis == Axis.horizontal) {
          return false;
        }
        if (notification is ScrollUpdateNotification) {
          // logger?.i('[TwoDimensions] ScrollUpdateNotification');
          // _logMetrics(metrics);
          // final dragDetails = notification.dragDetails;
          // if (dragDetails != null) {
          //   logger?.i(
          //       '[TwoDimensions] ScrollUpdateNotification dragDetails.delta: ${dragDetails.delta}');
          //   logger?.i(
          //       '[TwoDimensions] ScrollUpdateNotification dragDetails.primaryDelta: ${dragDetails.primaryDelta}');
          //   logger?.i(
          //       '[TwoDimensions] ScrollUpdateNotification dragDetails.sourceTimeStamp: ${dragDetails.sourceTimeStamp}');
          //   logger?.i(
          //       '[TwoDimensions] ScrollUpdateNotification dragDetails.localPosition: ${dragDetails.localPosition}');
          // }
          // // 尚未歸位
          // final scrollDelta = notification.scrollDelta ?? 0;
          // logger?.i(
          //     '[TwoDimensions] ScrollUpdateNotification scrollDelta: $scrollDelta');
          // logger?.i(
          //     '[TwoDimensions] ScrollUpdateNotification page: ${pageController.page}');
          // // pageController.page?.clamp(0, it.length - 1);
          // final args = ScrollableArgs(
          //   metrics: metrics,
          //   scrollDelta: scrollDelta,
          // );
          // if (scrollable?.call(args) == false) {
          //   // do not scroll (反向滑動)
          //   pageController.position.correctBy(-scrollDelta);
          //   pageController.position.notifyListeners();
          //   return true;
          // }
          return false;
        } else if (notification is OverscrollNotification) {
          final overscroll = notification.overscroll;
          // logger?.i('[TwoDimensions] overscroll: $overscroll');
          // _logMetrics(metrics);
          if (overscroll < 0) {
            // up
            widget.onOverscroll?.call(overscroll);
          } else if (overscroll > 0) {
            // down
            widget.onOverscroll?.call(overscroll);
          }
          return false;
        } else if (notification is UserScrollNotification) {
          // logger?.i('[TwoDimensions] UserScrollNotification');
          // _logMetrics(metrics);
          return false;
        }
        return false;
      },
      child: _pager(it),
    );
  }

  Widget _pager(Iterable<MapEntry<String, Iterable<IndexModuleApp>>> it) {
    return PageView.builder(
      itemCount: it.length,
      itemBuilder: (context, index) {
        final element = it.elementAt(index);
        return OneDimension(element.value);
      },
      physics: const ClampingScrollPhysics(),
      scrollDirection: Axis.vertical,
      onPageChanged: (index) {
        widget.bannerIndex = index;
        widget.onPageChanged?.call(index);
      },
      controller: pageController,
    );
  }

  void _logMetrics(ScrollMetrics metrics) {
    final logger = widget.logger;
    logger?.i(
        '[TwoDimensions] axisDirection: ${metrics.axisDirection}'); // AxisDirection.down
    logger?.i('[TwoDimensions] axis: ${metrics.axis}'); // Axis.vertical
    logger?.i('[TwoDimensions] pixels: ${metrics.pixels}'); // 0 (目前 top 的位置)
    logger?.i(
        '[TwoDimensions] viewportDimension: ${metrics.viewportDimension}'); //463
    logger?.i(
        '[TwoDimensions] atEdge: ${metrics.atEdge}'); // true (到頂或到底都會變成 true)
    logger?.i(
        '[TwoDimensions] extentBefore: ${metrics.extentBefore}'); // 0 (滑到頂部會變0)
    logger?.i(
        '[TwoDimensions] extentInside: ${metrics.extentInside}'); // 463 (滑到底部會變0)
    logger?.i(
        '[TwoDimensions] extentAfter: ${metrics.extentAfter}'); // 926 = 462 x 2
    logger?.i(
        '[TwoDimensions] extentTotal: ${metrics.extentTotal}'); // 1389 = 463 x 3
    logger?.i('[TwoDimensions] hasPixels: ${metrics.hasPixels}'); // true
    logger?.i(
        '[TwoDimensions] hasViewportDimension: ${metrics.hasViewportDimension}'); // true
    logger?.i(
        '[TwoDimensions] maxScrollExtent: ${metrics.maxScrollExtent}'); // 926 = 463 x 2
    logger
        ?.i('[TwoDimensions] minScrollExtent: ${metrics.minScrollExtent}'); // 0
    logger?.i('[TwoDimensions] outOfRange: ${metrics.outOfRange}'); // false
  }
}

class OneDimension extends StatefulWidget {
  final Iterable<IndexModuleApp> data;

  const OneDimension(
    this.data, {
    super.key,
  });

  @override
  State<OneDimension> createState() => _OneDimensionState();
}

class _OneDimensionState extends State<OneDimension> {
  CarouselSliderController? _carouselController;
  CarouselSliderController get carouselController {
    _carouselController ??= CarouselSliderController();
    return _carouselController!;
  }

  final _index = 0.obs;
  int get index => _index.value;
  set index(int value) => _index.value = value;

  @override
  void dispose() {
    // 清理資源
    _carouselController = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Background(
      alignment: Alignment.bottomCenter,
      background: _pager(),
      child: _indicator(),
    );
  }

  Widget _indicator() {
    return Background(
      alignment: Alignment.bottomCenter,
      background: DecoratedBox(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(0.0, -1.0),
            end: Alignment(0.0, 1.0),
            colors: [Color(0x00000000), Color(0x4d000000)],
            stops: [0.0, 1.0],
          ),
        ),
        child: SizedBox(
          width: double.infinity,
          height: 52.dh,
        ),
      ),
      child: _pageIndicator(),
    );
  }

  Widget _pageIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    final it = widget.data;
    yield const SizedBox(width: Constants.gridSpacing);
    yield IconButton(
      onPressed: _onPreviousPressed,
      icon: const Icon(Icons.arrow_back_ios),
      color: Colors.white,
    );
    yield Obx(() {
      return Text(
        // '1/5',
        '${index + 1}/${it.length}',
        style: const TextStyle(
          fontSize: 18,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      );
    }).expanded();
    yield IconButton(
      onPressed: _onNextPressed,
      icon: const Icon(Icons.arrow_forward_ios),
      color: Colors.white,
    );
    yield const SizedBox(width: Constants.gridSpacing);
  }

  Future<void> _onPreviousPressed() async {
    if (!mounted || _carouselController == null) return;
    
    try {
      await carouselController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } catch (e) {
      log('Error in _onPreviousPressed: $e');
    }
  }

  Future<void> _onNextPressed() async {
    if (!mounted || _carouselController == null) return;
    
    try {
      await carouselController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } catch (e) {
      log('Error in _onNextPressed: $e');
    }
  }

  void _safeStopAutoPlay() {
    if (!mounted || _carouselController == null) return;
    
    try {
      carouselController.stopAutoPlay();
    } catch (e) {
      log('Error stopping auto play: $e');
    }
  }

  void _safeStartAutoPlay() {
    if (!mounted || _carouselController == null) return;
    
    try {
      carouselController.startAutoPlay();
    } catch (e) {
      log('Error starting auto play: $e');
    }
  }

  Widget _pager() {
    final it = widget.data;
    return CarouselSlider.builder(
      carouselController: carouselController,
      itemCount: it.length,
      itemBuilder: (context, index, realIndex) {
        final element = it.elementAt(index);
        return ThumbnailImage(
          element.thumbnail,
          onTap: () {
            final uri = element.url?.uri ?? Uri();
            if (Get.canLaunchUrl(uri)) {
              Get.launchUrl(uri);
            }
          },
          looping: false,
          onVideoEnded: () {
            _onNextPressed();
            _safeStartAutoPlay();
          },
          onVideoStarted: () {
            _safeStopAutoPlay();
          },
        );
      },
      options: CarouselOptions(
        height: double.infinity,
        scrollPhysics: const ClampingScrollPhysics(),
        viewportFraction: 1,
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 3),
        autoPlayAnimationDuration: const Duration(milliseconds: 800),
        // autoPlayCurve: Curves.fastOutSlowIn,
        // enlargeCenterPage: true,
        // onPageChanged: (index, reason) {
        //   controller.current = index;
        // },
        // aspectRatio: 980.0 / 645.0,
        initialPage: 0,
        enableInfiniteScroll: true,
        reverse: false,
        // autoPlay: true,
        // autoPlayInterval: Duration(seconds: 3),
        // autoPlayAnimationDuration: Duration(milliseconds: 800),
        autoPlayCurve: Curves.fastOutSlowIn,
        // enlargeCenterPage: true,
        // enlargeFactor: 0.3,
        onPageChanged: (index, reason) {
          this.index = index;
        },
        scrollDirection: Axis.horizontal,
      ),
    );
  }
}
