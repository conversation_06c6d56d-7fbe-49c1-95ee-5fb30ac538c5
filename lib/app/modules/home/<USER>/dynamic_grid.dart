import 'package:efshop/constants.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import 'ef_grid_tile.dart';
import 'index_module_widget.dart';

class DynamicGrid extends IndexModuleWidget {
  final int crossAxisCount;
  const DynamicGrid(super.data, this.crossAxisCount, {super.key});

  @override
  Widget build(BuildContext context) {
    return StaggeredGrid.count(
      crossAxisCount: crossAxisCount,
      mainAxisSpacing: Constants.gridSpacing,
      crossAxisSpacing: Constants.gridSpacing,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    final it = data.dataList;
    for (var i = 0; i < it.length; i += crossAxisCount) {
      var crossAxisCellCount = crossAxisCount;
      for (var j = 1; j < crossAxisCount; j++) {
        if (i + j < it.length) {
          crossAxisCellCount--;
        }
      }
      for (var j = 0; j < crossAxisCount; j++) {
        if (i + j < it.length) {
          yield EfGridTile(it.elementAt(i + j), crossAxisCellCount);
        }
      }
    }
  }
}
