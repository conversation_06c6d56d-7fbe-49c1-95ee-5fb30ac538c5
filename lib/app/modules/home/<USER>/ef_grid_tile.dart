import 'package:efshop/app/components/thumbnail_image.dart';
import 'package:efshop/app/models/index_module_app.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';

class EfGridTile extends StatelessWidget {
  final int crossAxisCount;
  final IndexModuleApp data;

  bool get _canLaunchUrl => Get.canLaunchUrl(data.url?.uri ?? Uri());

  const EfGridTile(
    this.data,
    this.crossAxisCount, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return StaggeredGridTile.count(
      crossAxisCellCount: crossAxisCount,
      mainAxisCellCount: data.thumbnail?.reverseAspectRatio ?? 1,
      child: ThumbnailImage(
        data.thumbnail,
        onTap: _canLaunchUrl
            ? () {
                Get.launchUrl(data.url?.uri ?? U<PERSON>());
              }
            : null,
      ),
    );
  }
}
