import 'package:efshop/app/components/thumbnail_image.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'index_module_widget.dart';

class SlideGrid extends IndexModuleWidget {
  final num height;
  const SlideGrid(
    super.data,
    this.height, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height * 0.75,
      child: _list(),
    );
  }

  Widget _list() {
    final it = data.dataList;
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      padding: EdgeInsets.symmetric(horizontal: Constants.gridSpacing..dw),
      itemBuilder: (context, index) {
        final element = it.elementAt(index);
        return ThumbnailImage(
          element.thumbnail,
          onTap: () {
            if (Get.canLaunchUrl(element.url?.uri ?? Uri())) {
              Get.launchUrl(element.url?.uri ?? Uri());
            }
          },
        );
      },
      separatorBuilder: (context, index) {
        return SizedBox(width: 8.dh);
      },
      itemCount: it.length,
    );
  }
}
