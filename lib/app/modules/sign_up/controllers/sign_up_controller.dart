import 'package:efshop/app/models/login_res.dart';
import 'package:efshop/app/models/register_req.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

class SignUpController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  Logger get logger => wabowProvider.logger;
  final _draft = RegisterReq(gender: Gender.female.value).obs;
  RegisterReq get draft => _draft.value;

  final _confirmPassword = ''.obs;
  String get confirmPassword => _confirmPassword.value;
  set confirmPassword(String value) => _confirmPassword.value = value;

  final _agree = false.obs;
  bool get agree => _agree.value;
  set agree(bool value) => _agree.value = value;

  final _gender = Gender.female.obs;
  Gender get gender => _gender.value;
  set gender(Gender value) {
    _gender.value = value;
    draft.gender = value.value;
  }

  // 年
  final _yyyy = ''.obs;
  String get yyyy => _yyyy.value;
  set yyyy(String value) => _yyyy.value = value;
  // 月
  final _mm = ''.obs;
  String get mm => _mm.value;
  set mm(String value) => _mm.value = value;
  // 日
  final _dd = ''.obs;
  String get dd => _dd.value;
  set dd(String value) => _dd.value = value;
  // password visible
  final _passwordVisible = false.obs;
  bool get isPasswordVisible => _passwordVisible.value;
  void togglePasswordVisibility() {
    _passwordVisible.value = !_passwordVisible.value;
  }
  // confirm password visible
  final _confirmPasswordVisible = false.obs;
  bool get isConfirmPasswordVisible => _confirmPasswordVisible.value;
  void toggleConfirmPasswordVisibility() {
    _confirmPasswordVisible.value = !_confirmPasswordVisible.value;
  }

  SignUpController({required this.wabowProvider});

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.email)) {
      draft.email = Get.parameters[Keys.email];
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<LoginRes> signUp() async {
    // GA: log sign up
    await _logSignUp();
    return wabowProvider.register(draft);
  }

  bool isValidate() {
    try {
      draft.validate();
      draft.validatePassword(confirmPassword);
      if (agree == false) {
        throw '請勾選同意隱私權政策';
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  void refreshDraft() {
    _draft.refresh();
  }

  ///
  /// GA: log sign up
  ///
  Future<void> _logSignUp() async {
    try {
      await FirebaseAnalytics.instance.logSignUp(
        signUpMethod: 'email',
        parameters: {
          'email': draft.email ?? '',
        },
      );
    } catch (e) {
      logger.e(e);
    }
  }
}
