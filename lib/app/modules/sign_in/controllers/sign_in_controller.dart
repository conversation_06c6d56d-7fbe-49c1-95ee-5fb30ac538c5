import 'dart:async';

import 'package:efshop/app/models/error_res.dart';
import 'package:efshop/app/models/login_apple_req.dart';
import 'package:efshop/app/models/login_res.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/services.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:flutter_line_sdk/flutter_line_sdk.dart';
import 'package:flutter_web_auth/flutter_web_auth.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class SignInController extends GetxController {
  final _disposable = Completer();
  final Logger logger;
  final WabowProvider wabowProvider;
  final _email = ''.obs;
  String get email => _email.value;
  set email(String value) => _email.value = value;
  bool get isEmail => GetUtils.isEmail(email);

  SignInController({
    required this.logger,
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<bool> registerCheck() {
    return wabowProvider.registerCheck(email);
  }

  Future<LoginRes> signInWithLine() async {
    // GA: log line login
    await _logLineLogin();
    final result = await LineSDK.instance.login(
        // scopes: ["openid", "email"],
        );
    logger.d('result: $result');
    final accessToken = result.accessToken.value;
    logger.d('${Keys.accessToken}: $accessToken');

    return await wabowProvider.loginLine(accessToken);
  }

  ///
  /// GA: log line login
  ///
  Future<void> _logLineLogin() async {
    try {
      await FirebaseAnalytics.instance.logLogin(
        loginMethod: 'line',
      );
    } catch (e) {
      logger.e(e);
    }
  }

  ///
  /// GA: log facebook login
  ///
  Future<void> _logFacebookLogin() async {
    try {
      await FirebaseAnalytics.instance.logLogin(
        loginMethod: 'facebook',
      );
    } catch (e) {
      logger.e(e);
    }
  }

  ///
  /// GA: log apple login
  ///
  Future<void> _logAppleLogin() async {
    try {
      await FirebaseAnalytics.instance.logLogin(
        loginMethod: 'apple',
      );
    } catch (e) {
      logger.e(e);
    }
  }

  Future<LoginRes> signInWithApple() async {
    // GA: log apple login
    await _logAppleLogin();
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );
    logger.d('credential: $credential');
    try {
      return await wabowProvider.loginApple(credential.toLoginAppleReq());
    } on ErrorRes catch (e) {
      logger.e(e);
      e.email = credential.email;
      e.fullName = credential.fullName;
      rethrow;
    }
  }

  Future<LoginRes> signInWithFacebook() async {
    if (GetPlatform.isIOS) {
      return _signInWithFacebookWithWebAuth();
    } else {
      return _signInWithFacebookWithSdk();
    }
  }

  Future<LoginRes> _signInWithFacebookWithSdk() async {
    final fb = FacebookAuth.instance;
    // by default we request the email and the public profile
    final result = await fb.login();

    // loginBehavior is only supported for Android devices, for ios it will be ignored
    // final result = await FacebookAuth.instance.login(
    //   permissions: ['email', 'public_profile', 'user_birthday', 'user_friends', 'user_gender', 'user_link'],
    //   loginBehavior: LoginBehavior
    //       .DIALOG_ONLY, // (only android) show an authentication dialog instead of redirecting to facebook app
    // );

    if (result.status == LoginStatus.success) {
      final accessToken = result.accessToken;
      return wabowProvider.loginFacebook(accessToken?.tokenString ?? '');
      // _printCredentials();
      // get the user data
      // by default we get the userId, email,name and picture
      // final userData = await FacebookAuth.instance.getUserData();
      // final userData = await FacebookAuth.instance.getUserData(fields: "email,birthday,friends,gender,link");
      // _userData = userData;
    } else {
      // print(result.status);
      // print(result.message);
      throw PlatformException(
        code: result.status.toString(),
        message: result.message,
      );
    }
  }

  Future<LoginRes> _signInWithFacebookWithWebAuth() async {
    // GA: log facebook login
    await _logFacebookLogin();
    final uri = Uri.https('graph.facebook.com', 'oauth/authorize', {
      'response_type': 'token',
      'client_id': Constants.facebookId,
      'redirect_uri': Constants.facebookRedirectUri.toString(),
      'scope': 'email,public_profile'
    });

    // Present the dialog to the user
    final result = await FlutterWebAuth.authenticate(
      url: uri.toString(),
      callbackUrlScheme: Constants.facebookScheme,
    );

    logger.d('result: $result');
    final decoded = Uri.decodeFull(result).replaceAll('#', '?');
    // parse access token
    final res = Uri.parse(decoded);
    // 錯誤處理
    // fb522314041213398://authorize/?error=access_denied&error_code=200&error_description=Permissions+error&error_reason=user_denied#_=_
    if (res.queryParameters.containsKey(Keys.error)) {
      throw PlatformException(
        code: res.queryParameters[Keys.errorCode] ?? '',
        message: res.queryParameters[Keys.error],
        details: res.queryParameters[Keys.errorDescription],
        stacktrace: res.queryParameters[Keys.errorReason],
      );
    }
    // 成功取得 access token
    // fb522314041213398://authorize/#access_token=EAAHbCrbjQdYBAGBZBzFYgilbIgSpv0yLhmugIwzXZBZCQdbZBXjfUKYeh5qYp6K2uKSEkzZCd1qsNWvof8gHNE7USDfjrMFVTLRSLqXqhhWnPYvsgcGhSNVVvnQNdGUIn0FoMVaJcCRxam4vgEMNbk2B8A33RDzqaIUQNS21pZA3cKQj72RVl2Y5NPTAn70ZC0ZD&data_access_expiration_time=**********&expires_in=5112527
    final accessToken = res.queryParameters[Keys.accessToken] ?? '';
    logger.d('${Keys.accessToken}: $accessToken');

    return wabowProvider.loginFacebook(accessToken);
  }
}

extension AuthorizationCredentialAppleIDX on AuthorizationCredentialAppleID {
  LoginAppleReq toLoginAppleReq() => LoginAppleReq(
        accessToken: identityToken,
        uid: userIdentifier,
        code: authorizationCode,
      );

  String get appleId => userIdentifier ?? '';
  String get fullName => '${givenName ?? ''} ${familyName ?? ''}';
}
