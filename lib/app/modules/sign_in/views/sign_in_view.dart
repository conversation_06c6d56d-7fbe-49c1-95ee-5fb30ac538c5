import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/components/signin_button.dart';
import 'package:efshop/app/models/error_res.dart';
import 'package:efshop/app/models/login_res.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/ef_errors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../controllers/sign_in_controller.dart';

class SignInView extends GetView<SignInController> {
  SignInView({super.key}) {
    Get.lazyPut<SignInController>(
      () => SignInController(
        logger: Get.find(),
        wabowProvider: Get.find(),
      ),
      fenix: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SignInController>(
      init: Get.find<SignInController>(),
      builder: (_) {
        return Scaffold(
          backgroundColor: Colors.white,
          resizeToAvoidBottomInset: false,
          appBar: AppBar(
            elevation: 0,
            title: Text(
              '快速登入',
              style: TextStyle(
                fontSize: 16.dsp,
                color: EfColors.gray,
              ),
              textAlign: TextAlign.center,
            ),
            centerTitle: true,
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              // icon: const Icon(Icons.close),
              icon: SvgPicture.asset(
                'assets/images/close.svg',
                width: 30.dw,
                height: 30.dh,
              ),
            ),
          ),
          body: Padding(
            padding: EdgeInsets.symmetric(
              // vertical: 14.dh,
              horizontal: 24.dw,
            ),
            // child: ListView(
            //   children: _children().toList(growable: false),
            // ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: _children().toList(growable: false),
              ),
            ),
          ),
        );
      },
    );
  }

  Iterable<Widget> _children() sync* {
    yield SizedBox(height: 18.dh);
    yield SignInButton(
      text: '登  入',
      icon: 'assets/images/line.svg',
      backgroundColor: EfColors.line,
      onPressed: _signInWithLine,
    );
    yield SizedBox(height: 18.dh);
    yield SignInButton(
      text: '登  入',
      icon: 'assets/images/facebook.svg',
      backgroundColor: EfColors.facebook,
      onPressed: _signInWithFacebook,
    );
    if (GetPlatform.isIOS) {
      yield SizedBox(height: 18.dh);
      yield OutlinedButton.icon(
        style: OutlinedButton.styleFrom(
          backgroundColor: Colors.white,
          side: const BorderSide(
            color: EfColors.border,
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: Constants.buttonBorderRadius,
          ),
        ),
        onPressed: _signInWithApple,
        icon: const Icon(
          Icons.apple,
          color: Colors.black,
          size: 26,
        ),
        label: Text(
          '使用Apple登入',
          style: TextStyle(
            fontSize: Constants.buttonFontSize.dsp,
            color: Colors.black,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
          softWrap: false,
        ),
      ).sizedBox(width: double.infinity, height: Constants.buttonHeight.dh);
    }
    yield SizedBox(height: 40.dh);
    yield _horizontalLine();
    yield SizedBox(height: 34.dh);
    yield Background(
      background: Align(
        alignment: Alignment.centerRight,
        child: Obx(() {
          return Visibility(
            visible: controller.email.isEmpty,
            child: Text(
              'E-mail',
              style: TextStyle(
                fontSize: Constants.buttonFontSize.dsp,
                color: EfColors.grayTextLight,
              ),
            ).paddingOnly(right: 16.dw),
          );
        }),
      ),
      child: TextFormField(
        keyboardType: TextInputType.emailAddress,
        initialValue: controller.email,
        onChanged: (value) => controller.email = value,
      ).sizedBox(height: Constants.buttonHeight.dh),
    );
    yield SizedBox(height: 24.dh);
    yield Obx(() {
      return ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.zero,
          backgroundColor: EfColors.primary,
          disabledBackgroundColor: EfColors.grayD5,
          shape: const RoundedRectangleBorder(
            borderRadius: Constants.buttonBorderRadius,
          ),
        ),
        onPressed: controller.isEmail ? _onNextPressed : null,
        child: Text(
          '下一步',
          style: TextStyle(
            fontSize: 14.dsp,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ).sizedBox(width: double.infinity, height: Constants.buttonHeight.dh);
    });
    yield SizedBox(height: 24.dh);
  }

  Future<void> _signInWithFacebook() async {
    controller.logger.d('facebook sign in');
    try {
      await _signIn(controller.signInWithFacebook);
    } catch (e) {
      controller.logger.e(e);
      // Get.showAlert(e.toString());
    }
  }

  Future<void> _signInWithApple() async {
    controller.logger.d('apple sign in');
    // _signIn(controller.signInWithApple);
    Get.showLoading();
    try {
      final res = await controller.signInWithApple();
      // hide loading
      Get.back();
      // 關閉所頁面，返回參數
      Get.back(result: res);
    } on ErrorRes catch (e) {
      controller.logger.e(e);
      // 關閉 Loading
      Get.back();
      // apple 註冊
      if (e.type == EfErrors.shouldRegisterError) {
        await _signUpWithApple(e.uid ?? '', e.email ?? '', e.fullName ?? '');
      } else {
        // Get.showAlert(e.error ?? '');
        Get.showAlert('登入失敗');
      }
    } catch (e) {
      // 關閉 Loading
      Get.back();
      // Get.showAlert(e.toString());
      Get.showAlert('登入失敗');
    }
  }

  Future<void> _signIn(AsyncValueGetter<LoginRes> futureCallback) async {
    Get.showLoading();
    try {
      final res = await futureCallback();
      controller.logger.d('res: $res');
      // 成功，關閉 Loading
      Get.back();
      // 關閉所頁面，返回參數
      Get.back(result: res);
    } on String catch (e) {
      controller.logger.e(e);
      // 失敗，關閉 Loading
      Get.back();
      // Get.showAlert(e.toString());
      Get.showAlert('登入失敗');
    } on PlatformException catch (e) {
      controller.logger.e(e.toString());
      // 失敗，關閉 Loading
      Get.back();
      // Get.showAlert(e.toString());
      Get.showAlert('登入失敗');
    } on SignInWithAppleAuthorizationException catch (e) {
      controller.logger.e(e.toString());
      // 失敗，關閉 Loading
      Get.back();
      // Get.showAlert(e.message);
      Get.showAlert('登入失敗');
    } catch (e) {
      controller.logger.e(e.toString());
      // 失敗，關閉 Loading
      Get.back();
      // Get.showAlert(e.toString());
      Get.showAlert('登入失敗');
    }
  }

  ///
  /// 使用蘋果帳號註冊
  ///
  Future<void> _signUpWithApple(String uid, String email, String fullName) async {
    try {
      final res = await Get.toNamed(
        Routes.SIGN_UP_UID,
        id: SubRouteType.signIn.index,
        arguments: {
          Keys.title: 'Apple 註冊',
          Keys.uid: uid,
          Keys.email: email,
          Keys.fullName: fullName,
        },
      );
      // 關閉所頁面，返回參數
      if (res != null) {
        Get.back(result: res);
      }
    } catch (e) {
      Get.showAlert(e.toString());
    }
  }

  Future<void> _signUpWithLine(String mid) async {
    try {
      final res = await Get.toNamed(
        Routes.SIGN_UP_MID,
        id: SubRouteType.signIn.index,
        arguments: {
          Keys.title: 'Line 登入',
          Keys.mid: mid,
        },
      );
      // 關閉所頁面，返回參數
      if (res != null) {
        Get.back(result: res);
      }
    } catch (e) {
      Get.showAlert(e.toString());
    }
  }

  Future<void> _signInWithLine() async {
    controller.logger.d('line sign in');
    // 檢查是否有有效的上下文
    if (Get.context == null) {
      controller.logger.e('No valid context for LINE login');
      Get.showAlert('登入失敗：無法取得應用程式上下文');
      return;
    }
    
    try {
      final res = await controller.signInWithLine();
      // 關閉所頁面，返回參數
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.back(result: res);
      });
    } on ErrorRes catch (e) {
      controller.logger.e(e);
      // line 註冊
      if (e.type == EfErrors.shouldRegisterError) {
        await _signUpWithLine(e.mid ?? '');
      } else {
        Get.showAlert('登入失敗');
      }
    } catch (e) {
      controller.logger.e(e);
      Get.showAlert('登入失敗');
    }
  }

  Future<void> _signInWithEmail() async {
    final res = await Get.toNamed(
      Routes.PASSWORD_INPUT,
      id: SubRouteType.signIn.index,
      arguments: {
        Keys.email: controller.email,
      },
    );
    // 關閉所頁面，返回參數
    if (res != null) {
      Get.back(result: res);
    }
  }

  Future<void> _signUpWithEmail() async {
    final res = await Get.toNamed(
      Routes.SIGN_UP,
      id: SubRouteType.signIn.index,
      arguments: {
        Keys.email: controller.email,
      },
    );
    // 關閉所頁面，返回參數
    if (res != null) {
      Get.back(result: res);
    }
  }

  Future<void> _onNextPressed() async {
    Get.showLoading();
    try {
      final res = await controller.registerCheck();
      // 關閉 Loading
      Get.back();
      if (res == true) {
        await _signInWithEmail();
      } else {
        await _signUpWithEmail();
      }
    } catch (e) {
      // 關閉 Loading
      Get.back();
      Get.showAlert('$e');
    }
  }

  Widget _horizontalLine() {
    return Row(
      children: [
        Expanded(
          child: SvgPicture.asset('assets/images/horizontal.svg'),
        ),
        Text(
          '會員登入/註冊',
          style: TextStyle(
            fontSize: 14.dsp,
            color: EfColors.grayAA,
          ),
          textAlign: TextAlign.center,
          softWrap: false,
        ),
        Expanded(
          child: SvgPicture.asset('assets/images/horizontal.svg'),
        ),
      ],
    );
  }
}
