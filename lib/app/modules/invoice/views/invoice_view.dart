import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/components/colored_divider.dart';
import 'package:efshop/app/components/invoice_item.dart';
import 'package:efshop/app/components/invoice_paper.dart';
import 'package:efshop/app/components/product_item.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/invoice_controller.dart';

class InvoiceView extends GetView<InvoiceController> {
  const InvoiceView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('查看發票'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) {
          return Obx(() => _body());
        },
        onError: _onError,
      ),
    );
  }

  Widget _body() {
    return SingleChildScrollView(
      child: Safe<PERSON>rea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: _children().toList(growable: false),
        ),
      ),
    );
  }

  Widget _onError(String? error) {
    Future(() => Get.showAlert('待鑑賞期後，提供發票').then((value) => Get.back()));
    // return Center(child: Text(error.toString()));
    return const SizedBox();
  }

  Iterable<Widget> _children() sync* {
    yield SizedBox(height: 10.dh);
    yield const Text(
      '1. 因財政部令本副本僅供查核，不可直接兌換。',
      style: TextStyle(
        fontSize: 13,
        color: EfColors.gray47,
        height: 1.5,
      ),
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
    ).paddingSymmetric(horizontal: 14.dw);
    yield const Text(
      '2. 中獎發票將以 mail 通知您索取發票正本，並於申請後10日內以掛號寄出',
      style: TextStyle(
        fontSize: 13,
        color: EfColors.gray47,
        height: 1.5,
      ),
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
    ).paddingSymmetric(horizontal: 14.dw);
    yield SizedBox(height: 14.dh);
    yield ColoredBox(
      color: Colors.white,
      child: SizedBox(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: _elements().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _elements() sync* {
    yield SizedBox(height: 27.dh);
    yield _invoice();
    yield SizedBox(height: 20.dh);
    yield _invoiceItem();
    yield SizedBox(height: 20.dh);
    // final it = controller.data.detail ?? [];
    // if (it.isNotEmpty) {
    //   yield Divider(height: 1.dh);
    //   yield ListTile(
    //     title: const Text(
    //       '訂單明細',
    //       style: TextStyle(
    //         fontSize: 15,
    //         color: EfColors.gray6B,
    //       ),
    //       softWrap: false,
    //     ),
    //     trailing:
    //         Icon(controller.expanded ? Icons.chevron_right : Icons.expand_more),
    //     onTap: () {
    //       controller.expanded = !controller.expanded;
    //     },
    //   );
    //   yield Divider(height: 1.dh);
    //   if (controller.expanded == true) {
    //     yield _list();
    //   }
    // }
  }

  Widget _list() {
    final it = controller.data.detail ?? [];
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return ProductItem(data.toProduct());
      },
      separatorBuilder: (context, index) {
        return const ColoredDivider();
      },
      itemCount: it.length,
      shrinkWrap: true,
    );
  }

  Widget _invoice() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/dot_border.png'),
          fit: BoxFit.fill,
        ),
      ),
      child: Background(
        background: SizedBox(
          width: 256.dw,
          height: 430.dh,
        ),
        child: InvoicePaper(controller.data),
      ),
    );
  }

  Widget _invoiceItem() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/dot_border.png'),
          fit: BoxFit.fill,
        ),
      ),
      child: Background(
        alignment: Alignment.topCenter,
        background: SizedBox(
          width: 256.dw,
          height: 370.dh,
        ),
        child: InvoiceItem(controller.data),
      ),
    );
  }
}
