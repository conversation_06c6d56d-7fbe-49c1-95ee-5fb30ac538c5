import 'package:efshop/app/models/members_orders_invoices_res.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';

class InvoiceController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  final _id = ''.obs;
  String get id => _id.value;
  final _data = MembersOrdersInvoicesRes().obs;
  MembersOrdersInvoicesRes get data => _data.value;
  final _expanded = false.obs;
  bool get expanded => _expanded.value;
  set expanded(bool value) => _expanded.value = value;

  InvoiceController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id]!;
    }
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      _data.value = await wabowProvider.getMembersOrdersInvoices(id);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
