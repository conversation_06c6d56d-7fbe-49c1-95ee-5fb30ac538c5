import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../../categories_page/views/categories_page_view.dart';
import '../../home/<USER>/ef_search_delegate.dart';
import '../controllers/categories_controller.dart';

// index 的分類頁面
class CategoriesView extends GetView<CategoriesController> {
  final AsyncValueSetter<IndexTab>? changeTab;

  const CategoriesView({
    super.key,
    this.changeTab,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      // init: Get.find<CategoriesController>(),
      init: CategoriesController(
        wabowProvider: Get.find(),
      ),
      builder: (controller) {
        return controller.obx(
          (state) {
            return Scaffold(
              appBar: AppBar(
                leading: BackButton(
                  onPressed: () => changeTab?.call(IndexTab.home),
                ),
                titleSpacing: 0,
                // title: const Text('分類'),
                title: Transform.translate(
                  offset: const Offset(-12, 0),
                  child: ListTile(
                    minLeadingWidth: 0,
                    dense: true,
                    tileColor: EfColors.grayEE,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    // leading: const Icon(Icons.search),
                    leading: SvgPicture.asset(
                      'assets/images/search_icon.svg',
                      width: 20,
                      height: 20,
                    ),
                    title: const Text(
                      '輸入關鍵字',
                      style: TextStyle(
                        fontSize: 15,
                        color: Color(0x33000000),
                      ),
                      softWrap: false,
                    ),
                    onTap: () {
                      showSearch(
                        context: context,
                        delegate: EfSearchDelegate(
                          wabowProvider: Get.find(),
                        ),
                      );
                    },
                  ),
                ),
                centerTitle: true,
                bottom: _preferredSizeWidget(),
              ),
              body: _body(),
            );
          },
          onError: (error) {
            return ErrorButton(
              error,
              onTap: controller.onRefresh,
            );
          },
        );
      },
    );
  }

  PreferredSizeWidget? _preferredSizeWidget() {
    if (controller.tabs.isEmpty) {
      return null;
    }
    return PreferredSize(
      preferredSize: const Size.fromHeight(Constants.tabHeight),
      child: Align(
        alignment: Alignment.centerLeft,
        child: _tabBar(),
      ),
    );
  }

  Widget _tabBar() {
    return TabBar(
      controller: controller.tabController,
      isScrollable: true,
      labelStyle: const TextStyle(
        fontSize: 16,
      ),
      unselectedLabelColor: EfColors.gray66,
      tabs: _tabs().toList(growable: false),
      padding: EdgeInsets.zero,
      labelPadding: EdgeInsets.zero,
      onTap: (value) {
        try {
          controller.pageController.jumpToPage(value);
        } catch (e, s) {
          controller.talker.error(e, s);
        }
      },
    );
  }

  Iterable<Widget> _tabs() {
    return controller.tabs.map((entry) {
      return Tab(
        iconMargin: const EdgeInsets.only(
          bottom: 2,
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minWidth: 74.dw,
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.dw),
            child: Text(
              entry.name ?? '',
              softWrap: false,
              textAlign: TextAlign.center,
              overflow: TextOverflow.visible,
            ),
          ),
        ),
      );
    });
  }

  Widget _body() {
    final it = controller.tabs;
    if (it.isEmpty) {
      return const SizedBox();
    }
    return PageView.builder(
      controller: controller.pageController,
      itemCount: it.length,
      onPageChanged: (value) {
        try {
          controller.tabController?.animateTo(value);
        } catch (e, s) {
          controller.talker.error(e, s);
        }
      },
      itemBuilder: (BuildContext context, int index) {
        final category = it.elementAt(index);
        return CategoriesPageView(
          parent: category,
        );
      },
    );
  }
}
