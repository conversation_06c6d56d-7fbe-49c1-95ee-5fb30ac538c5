import 'dart:async';

import 'package:efshop/app/models/login_res.dart';
import 'package:efshop/app/models/logout_res.dart';
import 'package:efshop/app/models/members_profile_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/error_type.dart';
import 'package:efshop/extension.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/logger.dart';

class ProfileController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final WabowProvider wabowProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Logger get logger => prefProvider.logger;
  final _profile = MembersProfileRes().obs;
  MembersProfileRes get profile => _profile.value;
  String get fullname => profile.fullname ?? '';
  String get email => profile.email ?? '';
  final _avatar = ''.obs;
  String get avatar => _avatar.value;
  bool get hasAvatar => avatar.isNotEmpty;
  Map<OrderStatus, num> get tabs => prefProvider.tabs;

  ProfileController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  void loadProfile() {
    _profile.value = prefProvider.profile;
    _avatar.value = prefProvider.avatar;
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      if (prefProvider.isNotLogin) {
        throw ErrorType.unauthorized;
      }
      loadProfile();
      _fetchProfile();
      _fetchOrderCount();
      // wabowProvider.getMembersOrders();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<LogoutRes> logout() async {
    return wabowProvider.logout();
  }

  Future<LoginRes> renew() async {
    return wabowProvider.renew();
  }

  Future<void> _fetchProfile() async {
    try {
      _profile.value = await wabowProvider.getMembersProfile();
      prefProvider.profile = profile;
    } catch (e) {
      logger.e(e);
    }
  }

  Future<void> _fetchOrderCount() async {
    try {
      final res = await wabowProvider.getMembersOrdersCount();
      for (var entity in res.entries) {
        final status = tabs.keys.firstWhere(
          (key) => key.display == entity.key,
          orElse: () => OrderStatus.all,
        );
        tabs[status] = entity.value;
      }
    } catch (e) {
      logger.e(e);
    }
  }

  Future<void> getImage(ImageSource source) async {
    final picker = ImagePicker();
    // Pick an image.
    // final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    // Capture a photo.
    // final XFile? photo = await picker.pickImage(source: ImageSource.camera);
    final pickedFile = await picker.pickImage(
      source: source,
      maxWidth: 62.dw,
      maxHeight: 62.dh,
    );
    logger.d(pickedFile);
    if (pickedFile != null) {
      _avatar.value = pickedFile.path;
      // save to storage
      prefProvider.avatar = avatar;
    }
  }
}
