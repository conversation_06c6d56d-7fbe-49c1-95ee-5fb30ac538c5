import 'dart:io';

import 'package:efshop/app/components/action_button.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/components/image_selector.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../controllers/profile_controller.dart';

class ProfileView extends GetView<ProfileController> {
  final AsyncValueSetter<IndexTab>? changeTab;

  ProfileView({
    super.key,
    this.changeTab,
  }) {
    Get.lazyPut(
      () => ProfileController(wabowProvider: Get.find()),
      fenix: true,
    );
    initializeController().then((value) {
      controller.onRefresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: controller.obx(
          (state) => _body(),
          onError: (error) {
            return ErrorButton(
              error,
              onTap: controller.onRefresh,
            );
          },
        ),
      ),
    );
  }

  Widget _body() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  void _toOrdersPage(OrderStatus status) {
    Get.toNamed(Routes.ORDERS, parameters: {
      Keys.id: '${status.index}',
    });
  }

  Iterable<Widget> _children() sync* {
    yield ColoredBox(
      color: EfColors.primary,
      child: SafeArea(
        child: _avatar(),
      ),
    );
    yield Container(
      padding: EdgeInsets.symmetric(horizontal: 16.dw),
      alignment: Alignment.centerLeft,
      color: EfColors.grayD5,
      height: 24.dh,
      child: Text(
        '歡迎光臨 衣芙！',
        style: TextStyle(
          fontSize: 12.dsp,
          color: Colors.white,
        ),
        softWrap: false,
      ),
    );
    yield ListTile(
      contentPadding: EdgeInsets.symmetric(horizontal: 16.dw),
      title: const Text(
        '訂單查詢',
        softWrap: false,
      ),
      titleTextStyle: TextStyle(
        fontSize: 13.dsp,
        color: EfColors.grayText,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '查看全部訂單',
            style: TextStyle(
              fontSize: 12.dsp,
              color: EfColors.grayText,
            ),
            softWrap: false,
          ),
          Icon(
            Icons.arrow_forward_ios,
            size: 12.dsp,
            color: EfColors.grayText,
          ),
        ],
      ),
      onTap: () => _toOrdersPage(OrderStatus.all),
    );
    yield Divider(
      height: 1.dh,
      thickness: 1.dh,
    );
    yield SizedBox(
      height: 86.dh,
      child: Obx(() {
        return Row(
          children: _menu1().toList(growable: false),
        );
      }),
    );
    yield Divider(
      height: 8.dh,
      thickness: 8.dh,
    );
    yield ListTile(
      contentPadding: EdgeInsets.symmetric(horizontal: 16.dw),
      title: const Text(
        '我的服務',
        softWrap: false,
      ),
      titleTextStyle: TextStyle(
        fontSize: 13.dsp,
        color: EfColors.grayText,
      ),
    );
    yield Divider(
      height: 1.dh,
      thickness: 1.dh,
    );
    yield SizedBox(
      height: 110.dh,
      child: Row(
        children: _menu2().toList(growable: false),
      ),
    );
    yield Divider(
      height: 1.dh,
      thickness: 1.dh,
    );
    yield SizedBox(
      height: 110.dh,
      child: Row(
        children: _menu3().toList(growable: false),
      ),
    );
    yield Divider(
      height: 1.dh,
      thickness: 1.dh,
    );
  }

  Iterable<Widget> _menu1() sync* {
    yield ActionButton.menuStyle1(
      badgeCount: controller.tabs[OrderStatus.padding] ?? 0,
      icon: 'assets/images/menu_prepare.svg',
      text: '待出貨',
      onPressed: () => _toOrdersPage(OrderStatus.padding),
      badgeOffset: const Offset(2, 8),
    ).expanded();

    yield ActionButton.menuStyle1(
      badgeCount: controller.tabs[OrderStatus.shipped] ?? 0,
      icon: 'assets/images/menu_shipping.svg',
      text: '已出貨',
      onPressed: () => _toOrdersPage(OrderStatus.shipped),
      badgeOffset: const Offset(2, 8),
    ).expanded();

    yield ActionButton.menuStyle1(
      badgeCount: controller.tabs[OrderStatus.signed] ?? 0,
      icon: 'assets/images/menu_shipped.svg',
      text: '待簽收',
      onPressed: () => _toOrdersPage(OrderStatus.signed),
      badgeOffset: const Offset(2, 8),
    ).expanded();

    yield ActionButton.menuStyle1(
      badgeCount: controller.tabs[OrderStatus.refund] ?? 0,
      icon: 'assets/images/menu_refund.svg',
      text: '退貨退款',
      onPressed: () => _toOrdersPage(OrderStatus.refund),
      badgeOffset: const Offset(2, 8),
    ).expanded();

    yield ActionButton.menuStyle1(
      badgeCount: controller.tabs[OrderStatus.rated] ?? 0,
      icon: 'assets/images/menu_comment.svg',
      text: '待評價',
      onPressed: () => _toOrdersPage(OrderStatus.rated),
      badgeOffset: const Offset(2, 8),
    ).expanded();
  }

  Future<void> pickImage() async {
    final res = await ImageSelector(
      onCameraPressed: () async {
        Get.back(result: ImageSource.camera);
      },
      onAlbumPressed: () async {
        Get.back(result: ImageSource.gallery);
      },
    ).sheet<ImageSource>();
    if (res != null) {
      try {
        await controller.getImage(res);
      } catch (e) {
        controller.logger.e(e);
      }
    }
  }

  Widget _avatar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.dw),
      // color: EfColors.primary,
      height: 110.dh,
      alignment: Alignment.centerLeft,
      child: ListTile(
        textColor: Colors.white,
        onTap: () async {
          await Get.toNamed(Routes.PROFILE_EDITOR);
          controller.loadProfile();
        },
        contentPadding: EdgeInsets.zero,
        leading: IconButton(
          padding: EdgeInsets.zero,
          onPressed: pickImage,
          iconSize: 62.dw,
          icon: Obx(() {
            return CircleAvatar(
              radius: 31.dw,
              backgroundImage: const AssetImage('assets/images/avatar.png'),
              foregroundImage: controller.hasAvatar
                  ? FileImage(File(controller.avatar))
                  : null,
              backgroundColor: Colors.white,
              // child: SvgPicture.asset(
              //   'assets/images/avatar.svg',
              //   width: 62.dw,
              //   height: 62.dh,
              // ),
            );
          }),
        ),
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Obx(
              () => Text(
                // '王*明',
                controller.fullname,
              ),
            ),
            const SizedBox(width: 4),
            const Icon(
              Icons.edit,
              size: 18,
              color: Colors.white,
            ),
          ],
        ),
        titleTextStyle: TextStyle(
          fontSize: 14.dsp,
          color: Colors.white,
        ),
        subtitle: Obx(
          () => Text(
            // 'Email帳號',
            controller.email,
            style: const TextStyle(
              color: Colors.white,
            ),
          ),
        ),
        subtitleTextStyle: TextStyle(
          fontSize: 12.dsp,
          color: Colors.white,
        ),
      ),
    );
  }

  Iterable<Widget> _menu2() sync* {
    yield ActionButton.menuStyle1(
      icon: 'assets/images/star.svg',
      text: '訊息',
      onPressed: () => Get.toNamed(Routes.MESSAGE),
      iconMargin: EdgeInsets.only(
        bottom: 8.dh,
      ),
    ).expanded();
    yield ActionButton.menuStyle1(
      icon: 'assets/images/menu_favorite.svg',
      text: '收藏',
      // onPressed: () => Get.toNamed(Routes.FAVORITE),
      onPressed: () async {
        final res = await Get.toNamed(Routes.FAVORITE);
        if (changeTab != null && res is IndexTab) {
          changeTab!(res);
        }
      },
      iconMargin: EdgeInsets.only(
        bottom: 8.dh,
      ),
    ).expanded();
    yield ActionButton.menuStyle1(
      icon: 'assets/images/money.svg',
      text: '紅利點數',
      onPressed: () {
        Get.launchUrl(Uri.parse(Routes.MY_BONUS));
      },
      iconMargin: EdgeInsets.only(
        bottom: 8.dh,
      ),
    ).expanded();
    yield ActionButton.menuStyle1(
      icon: 'assets/images/cloth.svg',
      text: '貨到通知',
      onPressed: () => Get.toNamed(Routes.ARRIVAL),
      iconMargin: EdgeInsets.only(
        bottom: 8.dh,
      ),
    ).expanded();
  }

  Iterable<Widget> _menu3() sync* {
    yield ActionButton.menuStyle1(
      icon: 'assets/images/menu_service.svg',
      text: '客服訊息',
      onPressed: () {
        Get.toNamed(Routes.SERVICE);
      },
      iconMargin: EdgeInsets.only(
        bottom: 8.dh,
      ),
    ).expanded();
    yield ActionButton.menuStyle1(
      icon: 'assets/images/map_ping.svg',
      text: '地址',
      onPressed: () => Get.toNamed(Routes.ADDRESS),
      iconMargin: EdgeInsets.only(
        bottom: 8.dh,
      ),
    ).expanded();
    yield ActionButton.menuStyle1(
      icon: 'assets/images/gear.svg',
      text: '設定',
      onPressed: () async {
        final res = await Get.toNamed(Routes.SETTINGS);
        if (res is IndexTab) {
          changeTab?.call(res);
        }
      },
      iconMargin: EdgeInsets.only(
        bottom: 8.dh,
      ),
    ).expanded();
    yield const Spacer();
  }
}
