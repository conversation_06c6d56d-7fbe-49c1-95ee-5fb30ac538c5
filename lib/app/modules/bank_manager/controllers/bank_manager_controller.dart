import 'dart:async';

import 'package:efshop/app/models/members_refund_post_req.dart';
import 'package:efshop/app/models/members_refund_res.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/bank_provider.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';

class BankManagerController extends GetxController with StateMixin<String> {
  final BankProvider bankProvider;
  WabowProvider get wabowProvider => bankProvider.wabowProvider;
  final data = <MembersRefundRes>[].obs;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  final _disposable = Completer();

  BankManagerController({
    required this.bankProvider,
  });

  @override
  void onInit() {
    super.onInit();
    final box = boxProvider.getGsBox(Boxes.bank.name);
    box
        .watch()
        .debounce(200.milliseconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      final res = bankProvider.getBanksFromLocalStorage();
      data.assignAll(res);
      change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
    });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      // final res = await bankProvider.getBanks();
      final res = bankProvider.getBanksFromLocalStorage();
      data.assignAll(res);
      change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<MessageRes> delete(String id) {
    return bankProvider.delete(id);
  }

  Future<MessageRes> setDefault(MembersRefundRes data) {
    final draft = MembersRefundPostReq();
    draft.isDefault = true;
    return bankProvider.update(data.id ?? '', draft);
  }

  void refreshDraft() {
    data.refresh();
  }
}
