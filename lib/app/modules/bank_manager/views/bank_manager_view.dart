import 'package:efshop/app/components/bank_item.dart';
import 'package:efshop/app/models/error_res.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/bank_manager_controller.dart';

class BankManagerView extends GetView<BankManagerController> {
  const BankManagerView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('退款帳號管理'),
        centerTitle: true,
        actions: _actions().toList(growable: false),
      ),
      body: controller.obx((state) {
        return Obx(() => _body());
      }, onEmpty: _empty()),
    );
  }

  Widget _empty() {
    return const Center(
      child: Text('目前沒有退款帳號'),
    );
  }

  Iterable<Widget> _actions() sync* {
    yield TextButton(
      onPressed: () {
        Get.toNamed(Routes.BANK_EDITOR, parameters: {
          Keys.id: '',
        });
      },
      child: const Text(
        '新增',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray98,
        ),
        softWrap: false,
      ),
    );
  }

  Widget _body() {
    final it = controller.data;
    it.length;
    return ListView.separated(
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return BankItem(
          data,
          onPressed: () => Get.back(result: data.id),
          onDeletePressed: () async {
            final res = await Get.showConfirm<Button>(
              '即將刪除此帳號',
              textConfirm: '刪除',
            );
            if (res == Button.confirm &&
                data.id != null &&
                data.id!.isNotEmpty) {
              _delete(data.id!);
            }
          },
          onEditPressed: () {
            Get.toNamed(Routes.BANK_EDITOR, parameters: {
              Keys.id: data.id ?? '0',
            });
          },
          onChanged: (value) async {
            if (value == true) {
              for (var element in controller.data) {
                element.isDefault = element.id == data.id;
              }
              controller.refreshDraft();
              try {
                await controller.setDefault(data);
              } on ErrorRes catch (e) {
                Get.showAlert(e.displayError);
              } catch (e) {
                Get.showAlert(e.toString());
              }
            }
          },
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 10);
      },
      itemCount: it.length,
    );
  }

  Future<void> _delete(String id) async {
    Get.showLoading();
    try {
      await controller.delete(id);
      Get.back();
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
  }
}
