import 'package:efshop/app/components/color_item.dart';
import 'package:efshop/app/components/price.dart';
import 'package:efshop/app/components/size_item.dart';
import 'package:efshop/app/components/thumbnail_image.dart';
import 'package:efshop/app/models/error_res.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/color_picker_controller.dart';

class ColorPickerView extends GetView<ColorPickerController> {
  // final String? tag;

  ColorPickerView({
    super.key,
    String? id,
    Iterable<int?>? ids,
    int index = 0,
  }) {
    Get.lazyPut<ColorPickerController>(
      () => ColorPickerController(
        wabowProvider: Get.find(),
      ),
      fenix: true,
      // tag: tag,
    );
    initializeController().then((value) {
      controller.index = index;
      controller.ids.assignAll(ids ?? []);
      controller.id = id ?? '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ColorPickerController>(
      init: Get.find<ColorPickerController>(),
      builder: (controller) {
        return controller.obx(
          // (state) => _body(),
          (state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(child: _body()),
                Obx(() => _addCart()),
              ],
            );
          },
          onEmpty: _empty(),
        );
        // return Scaffold(
        //   body: controller.obx(
        //     (state) => _body(),
        //     onEmpty: _empty(),
        //   ),
        // );
      },
    );
  }

  Widget _empty() {
    return const Center(
      child: Text(
        '沒有資料',
        style: TextStyle(fontSize: 20),
      ),
    );
  }

  Widget _info() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(width: 20);
      yield Obx(() => _thumbnail());
      yield const SizedBox(width: 10);
      yield Expanded(
        child: Transform.translate(
          offset: const Offset(0, -16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: Transform.translate(
                  offset: const Offset(12, 4),
                  child: CloseButton(
                    onPressed: Get.back,
                    color: EfColors.grayLight,
                  ),
                ),
              ),
              // const Spacer(),
              Obx(() {
                return Price(
                  // 100,
                  // controller.data.finalPrice,
                  controller.currentSerial.finalPrice,
                  color: EfColors.primary,
                  fontSize: 18,
                  originalPrice: controller.currentSerial.price ?? 0,
                );
              }),
              Obx(() {
                // return Text(
                //   // '羅紋配色V領純棉T恤-紅',
                //   controller.productNameWithColor,
                //   style: const TextStyle(
                //     fontSize: 15,
                //     color: EfColors.grayText,
                //   ),
                //   softWrap: true,
                // );
                return Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: controller.data.name ?? '',
                        style: const TextStyle(
                          fontSize: 15,
                          color: EfColors.grayText,
                        ),
                      ),
                      TextSpan(
                        text:
                            ' - ${controller.currentSerial.color}${controller.currentSerial.size}',
                        style: const TextStyle(
                          fontSize: 15,
                          color: EfColors.grayTextLight,
                        ),
                      ),
                    ],
                  ),
                  maxLines: 4,
                  overflow: TextOverflow.ellipsis,
                  softWrap: true,
                );
              }),
            ],
          ),
        ),
      );
      yield const SizedBox(width: 20);
    }

    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _body() {
    Iterable<Widget> children() sync* {
      yield _spacer();
      yield _info();
      yield _spacer();
      yield const Divider(height: 1);
      yield _spacer();
      yield Obx(() => _colorPicker());
      yield _spacer();
      yield const Divider(height: 1);
      yield* _sizePicker();
      // yield _spacer();
      yield const Divider(height: 1);
      yield _spacer();
      yield _quantityPicker();
      // yield Obx(() {
      //   return Visibility(
      //     visible: controller.currentSerial.isAvailableForSale,
      //     child: _quantityPicker(),
      //   );
      // });
      yield _spacer();
      // yield Obx(() => _addCart());
    }

    return ColoredBox(
      color: Colors.white,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: children().toList(growable: false),
        ),
      ),
    );
  }

  Widget _spacer() {
    // return const Spacer();
    return const SizedBox(height: 16);
  }

  Future<void> _submitAddCart() async {
    try {
      Get.showLoading();
      await controller.addCart();
      Get.back();
      // Get.showToast('加入購物車成功');
      final id = controller.currentSerial.id ?? 0;
      // 回傳選擇的商品編號
      Get.back(result: id);
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
  }

  Future<void> _submitPreordes() async {
    try {
      await getLoginRes();
      Get.showLoading();
      await controller.addPreorders();
      Get.back();
      Get.showToast('加入貨到通知成功');
      Get.back();
    } catch (e) {
      Get.back();
      if (e is ErrorRes && e.type == 'ContextDuplicateError') {
        Get.showToast('加入貨到通知成功');
        Get.back();
      } else {
        Get.showAlert(e.toString());
      }
    }
  }

  void _submit() {
    if (controller.currentSerial.isAvailableForSale) {
      _submitAddCart();
    } else {
      _submitPreordes();
    }
  }

  Widget _addCart() {
    Iterable<Widget> children() sync* {
      yield Text(
        // '確認',
        controller.currentSerial.displayText,
        style: const TextStyle(
          fontSize: 18,
          color: Colors.white,
          letterSpacing: 5,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      );
      if (controller.currentSerial.isOutOfStock) {
        yield SizedBox(width: 8.dw);
        yield SvgPicture.asset(
          'assets/images/finger.svg',
          width: 32.dw,
          height: 24.dh,
        );
      }
    }

    return SizedBox(
      height: 55,
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: controller.currentSerial.displayColor,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
          ),
        ),
        onPressed: controller.currentSerial.isAvailable ? _submit : null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: children().toList(growable: false),
        ),
      ),
    );
  }

  Widget _quantityPicker() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(width: 14);
      yield const Text(
        '購買數量',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.grayTextDark,
        ),
        textAlign: TextAlign.start,
        softWrap: false,
      ).expanded();
      yield const SizedBox(width: 4);
      yield IconButton(
        iconSize: 32,
        padding: EdgeInsets.zero,
        onPressed: () {
          if (controller.currentSerial.isAvailableForSale) {
            controller.removeQuantity();
          }
        },
        icon: Container(
          width: 40.dw,
          height: 40.dh,
          decoration: const BoxDecoration(
            color: EfColors.grayF5,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(6.0),
              bottomLeft: Radius.circular(6.0),
            ),
          ),
          child: const Icon(
            Icons.remove,
            color: EfColors.grayText,
          ),
        ),
      );
      yield ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: 56.dw,
          minHeight: 40.dh,
        ),
        child: Center(
          child: Obx(() {
            return Text(
              // '1',
              controller.quantity.decimalStyle,
              style: const TextStyle(
                fontSize: 18,
                color: EfColors.gray1B,
              ),
              textAlign: TextAlign.center,
              softWrap: false,
              maxLines: 1,
            );
          }),
        ),
      );
      yield IconButton(
        iconSize: 32,
        onPressed: () {
          if (controller.currentSerial.isAvailableForSale) {
            controller.addQuantity();
          }
        },
        padding: EdgeInsets.zero,
        icon: Container(
          width: 40.dw,
          height: 40.dh,
          decoration: const BoxDecoration(
            color: EfColors.grayF5,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(6.0),
              bottomRight: Radius.circular(6.0),
            ),
          ),
          child: const Icon(
            Icons.add,
            color: EfColors.grayText,
          ),
        ),
      );
      yield const SizedBox(width: 14);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Iterable<Widget> _sizePicker() sync* {
    yield Transform.translate(
      offset: const Offset(0, -8),
      child: Align(
        alignment: Alignment.centerRight,
        child: TextButton(
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
          ),
          onPressed: () {
            Get.toNamed(Routes.EF_WEB, parameters: <String, String>{
              Keys.url: controller.data.htmlString,
              Keys.title: '尺寸',
            });
          },
          child: const Text(
            '尺寸說明',
            style: TextStyle(
              fontSize: 12,
              color: EfColors.grayTextLight,
            ),
            softWrap: false,
            textAlign: TextAlign.end,
          ),
        ).paddingSymmetric(horizontal: 14.dw),
      ),
    );
    yield Transform.translate(
      offset: const Offset(0, -20),
      child: Obx(() {
        Iterable<Widget> children() sync* {
          for (final element in controller.sizes) {
            yield SizeItem(
              element,
              selected: element.size == controller.currentSerial.size,
              onTap: () => controller.currentSerial = element,
            );
          }
        }

        return Wrap(
          spacing: 8.dw,
          runSpacing: 8.dh,
          children: children().toList(),
        ).paddingSymmetric(horizontal: 14);
      }),
    );
  }

  Widget _colorPicker() {
    Iterable<Widget> children() sync* {
      for (final element in controller.colors) {
        yield ColorItem(
          element,
          selected: element.color == controller.currentSerial.color,
          onTap: () {
            controller.currentSerial = element;
            final it = controller.sizes;
            if (it.isNotEmpty) {
              controller.currentSerial = it.first;
            }
          },
        );
      }
    }

    return Wrap(
      spacing: 8.dw,
      runSpacing: 8.dh,
      children: children().toList(growable: false),
    ).paddingSymmetric(horizontal: 14);
  }

  Widget _thumbnail() {
    final data = controller.data;
    return SizedBox(
      width: 120.dw,
      child: AspectRatio(
        aspectRatio: data.thumbnail?.aspectRatio ?? 1,
        child: ThumbnailImage.url(controller.currentSerial.mainImage),
      ),
    );
  }
}
