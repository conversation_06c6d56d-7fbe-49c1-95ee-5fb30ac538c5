import 'dart:async';
import 'dart:math';

import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/models/product_detail.dart';
import 'package:efshop/app/models/product_series_res.dart';
import 'package:efshop/app/models/products_preorder_post_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:stream_transform/stream_transform.dart';

class ColorPickerController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final WabowProvider wabowProvider;
  Logger get logger => wabowProvider.logger;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  final _data = ProductDetail().obs;
  ProductDetail get data => _data.value;
  final _id = ''.obs;
  String get id => _id.value;
  set id(String value) => _id.value = value;

  final serials = <ProductSeriesRes>[].obs;
  final _currentSerial = ProductSeriesRes().obs;
  ProductSeriesRes get currentSerial => _currentSerial.value;
  set currentSerial(ProductSeriesRes value) => _currentSerial.value = value;

  final ids = <int?>[].obs;
  final _index = 0.obs;
  set index(int value) => _index.value = value;

  final _quantity = 1.obs;
  int get quantity => _quantity.value;
  // set quantity(int value) => _quantity.value = value;

  void addQuantity() {
    _quantity.value = min(quantity + 1, currentSerial.maxQuantity ?? 1);
  }

  void removeQuantity() {
    _quantity.value = max(quantity - 1, 1);
  }

  // distinct with color
  List<ProductSeriesRes> get colors {
    return serials.fold<List<ProductSeriesRes>>(<ProductSeriesRes>[],
        (previousValue, element) {
      if (ids.contains(element.id)) {
        previousValue.add(element);
      }
      return previousValue;
    });
  }

  Iterable<ProductSeriesRes> get sizes {
    final it = serials.where((element) => element.color == currentSerial.color);
    // special case
    if (it.any((element) => element.size == '3XL')) {
      final ls = it.toList();
      final index = ls.indexWhere((element) => element.size == '3XL');
      // move to last
      final element = ls.removeAt(index);
      ls.add(element);
      return ls;
    }
    return it;
  }

  String get productNameWithColor {
    return '${data.name} - ${currentSerial.color}';
  }

  ColorPickerController({required this.wabowProvider});

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
    _currentSerial.stream.takeUntil(_disposable.future).listen((event) {
      _quantity.value = min(max(quantity, 1), currentSerial.maxQuantity ?? 1);
    });
    // _fetchParameters();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  // void _fetchParameters() {
  //   if (Get.parameters.containsKey(Keys.id)) {
  //     id = Get.parameters[Keys.id]!;
  //   }
  // }

  // TODO: use iterable map
  Iterable<ProductSeriesRes> loadFromLocalStorage() sync* {
    final box = boxProvider.getGsBox(Boxes.productSeries.name);
    final it = box.read(id);
    for (final item in it) {
      yield ProductSeriesRes.fromJson(item);
    }
  }

  Future<void> onRefresh() async {
    try {
      final box = boxProvider.getGsBox(Boxes.productDetail.name);
      final json = box.read(id);
      _data.value = ProductDetail.fromJson(json);
      //
      final it = loadFromLocalStorage();
      serials.assignAll(it);
      // 設定預設值
      if (_index.value >= 0 && _index.value < colors.length) {
        _currentSerial.value = colors.elementAt(_index.value);
        // _currentSerial.value = colors.firstWhere(
        //     (element) => element.id == currentId,
        //     orElse: () => colors.first);
      }
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<MessageRes> addCart() async {
    final id = currentSerial.id ?? 0;
    await wabowProvider.getProducts('$id');
    return wabowProvider.postCart('$id', quantity);
  }

  // 貨到通知，需要 email
  Future<ProductsPreorderPostRes> addPreorders() {
    final email = prefProvider.loginRes?.payload?.email ?? '';
    final id = currentSerial.id ?? 0;
    return wabowProvider.postMemberPreorders('$id', email);
  }
}
