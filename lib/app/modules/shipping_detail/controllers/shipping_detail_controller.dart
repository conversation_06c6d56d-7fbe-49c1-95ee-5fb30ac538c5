import 'package:efshop/app/models/members_orders_message.dart';
import 'package:efshop/app/models/members_orders_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';

class ShippingDetailController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  final _id = ''.obs;
  String get id => _id.value;
  final data = <MembersOrdersMessage>[].obs;
  final _order = MembersOrdersRes().obs;
  MembersOrdersRes get order => _order.value;

  ShippingDetailController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .debounce(const Duration(milliseconds: 500))
        .asyncMap((event) => onRefresh())
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id]!;
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final box = boxProvider.getGsBox(Boxes.order.name);
      final json = box.read(id);
      _order.value = MembersOrdersRes.fromJson(json);
      final res = await wabowProvider.getMembersOrdersShips(id);
      data.assignAll(res);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
