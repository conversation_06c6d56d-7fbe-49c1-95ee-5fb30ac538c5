import 'package:efshop/app/components/vertical_processing.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/shipping_detail_controller.dart';

class ShippingDetailView extends GetView<ShippingDetailController> {
  const ShippingDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('物流詳情'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) {
          return _body();
        },
        onEmpty: const Center(
          child: Text('沒有資料'),
        ),
        onError: (error) => Center(
          child: Text(error ?? 'Error'),
        ),
      ),
    );
  }

  Widget _body() {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _children().toList(growable: false),
      ).paddingSymmetric(horizontal: 20),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: 10);
    yield Text(
      '配送方式：${controller.order.shippingName}',
      style: const TextStyle(
        fontSize: 13,
        color: EfColors.gray,
      ),
      softWrap: false,
    );
    yield const SizedBox(height: 8);
    yield Text(
      '配送編號：${controller.order.shippingNumber}',
      style: const TextStyle(
        fontSize: 13,
        color: EfColors.gray,
      ),
      softWrap: false,
    );
    yield const SizedBox(height: 4);
    yield const Divider();
    yield Obx(() {
      final it = controller.data;
      return ListView.separated(
        itemBuilder: (context, index) {
          final data = it.elementAt(index);
          return VerticalProcessing(
            index,
            it.length,
            data,
          );
        },
        separatorBuilder: (context, index) {
          return const Divider(
            indent: 20,
            height: 1,
            thickness: 1,
            color: EfColors.grayDD,
          );
        },
        itemCount: it.length,
      );
    }).expanded();
  }
}
