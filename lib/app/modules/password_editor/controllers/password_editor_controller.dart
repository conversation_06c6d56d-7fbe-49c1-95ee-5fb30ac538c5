import 'dart:async';

import 'package:efshop/app/models/members_password_patch_req.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:stream_transform/stream_transform.dart';

class PasswordEditorController extends GetxController {
  final WabowProvider wabowProvider;
  Logger get logger => wabowProvider.logger;
  final _disposable = Completer();
  final _draft = MembersPasswordPatchReq().obs;
  MembersPasswordPatchReq get draft => _draft.value;
  final oldPasswordEditing = TextEditingController();
  final newPasswordEditing = TextEditingController();
  final confirmPasswordEditing = TextEditingController();
  final oldPasswordFocusNode = FocusNode();
  final newPasswordFocusNode = FocusNode();
  final confirmPasswordFocusNode = FocusNode();

  PasswordEditorController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    oldPasswordEditing.watch().takeUntil(_disposable.future).listen((event) {
      draft.password = oldPasswordEditing.text;
    });
    newPasswordEditing.watch().takeUntil(_disposable.future).listen((event) {
      draft.newPassword = newPasswordEditing.text;
    });
    oldPasswordFocusNode.watch().takeUntil(_disposable.future).listen((event) {
      refreshDraft();
    });
    newPasswordFocusNode.watch().takeUntil(_disposable.future).listen((event) {
      refreshDraft();
    });
    confirmPasswordFocusNode
        .watch()
        .takeUntil(_disposable.future)
        .listen((event) {
      refreshDraft();
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    _disposable.complete();
    oldPasswordEditing.dispose();
    newPasswordEditing.dispose();
    confirmPasswordEditing.dispose();
    oldPasswordFocusNode.dispose();
    newPasswordFocusNode.dispose();
    confirmPasswordFocusNode.dispose();
    super.onClose();
  }

  Future<MessageRes> submit() async {
    draft.validate();
    if (confirmPasswordEditing.text != newPasswordEditing.text) {
      throw '新密碼與確認密碼不一致';
    }
    return wabowProvider.patchMembersPassword(draft);
  }

  void refreshDraft() {
    _draft.refresh();
  }
}
