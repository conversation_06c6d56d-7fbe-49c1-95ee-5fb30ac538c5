import 'dart:async';

import 'package:efshop/app/models/members_orders_comments_post_req.dart';
import 'package:efshop/app/models/members_orders_res.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/models/product.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';

class LeaveCommentController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  final _id = ''.obs;
  String get id => _id.value;
  final _disposable = Completer();
  final _order = MembersOrdersRes().obs;
  MembersOrdersRes get order => _order.value;
  final editing = <String, TextEditingController>{}.obs;
  Iterable<Product> get commentableProducts =>
      order.normalProducts.where((e) => e.isCommentable);

  LeaveCommentController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .tap((e) {
          change('', status: RxStatus.loading());
        })
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id]!;
    }
    // onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final box = boxProvider.getGsBox(Boxes.order.name);
      final json = box.read(id);
      _order.value = MembersOrdersRes.fromJson(json);
      if (commentableProducts.isEmpty) {
        throw '您已經都評價完囉';
      }
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  TextEditingController getEditingWithProductId(String productId) {
    if (!editing.containsKey(productId)) {
      editing[productId] = TextEditingController();
    }
    return editing[productId]!;
  }

  Iterable<MembersOrdersCommentsPostReq> _comments() sync* {
    for (var element in editing.entries) {
      final text = element.value.text;
      if (text.isEmpty) continue;
      final productId = element.key;
      yield MembersOrdersCommentsPostReq(
        productId: productId,
        comment: text,
      );
    }
  }

  Future<MessageRes> submit() async {
    final it = _comments();
    if (it.isEmpty) {
      throw '評價內容為必填項目';
    }
    return wabowProvider.postMembersOrdersComments(id, it);
  }
}
