import 'package:efshop/app/components/comment_item.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/leave_comment_controller.dart';

class LeaveCommentView extends GetView<LeaveCommentController> {
  const LeaveCommentView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('評價'),
        centerTitle: true,
        actions: _actions().toList(growable: false),
      ),
      body: controller.obx(
        (state) => _body(),
        onError: _onError,
      ),
    );
  }

  Widget _onError(String? error) {
    Future(() => Get.showAlert(error ?? '').then((value) => Get.back()));
    return const SizedBox();
  }

  Iterable<Widget> _actions() sync* {
    yield TextButton(
      onPressed: () async {
        Get.showLoading();
        try {
          await controller.submit();
          Get.back();
          await Get.showAlert('評價送出成功');
          Get.back();
        } catch (e) {
          Get.back();
          Get.showAlert(e.toString());
        }
      },
      child: const Text(
        '送出',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray98,
        ),
        softWrap: false,
      ),
    );
  }

  Widget _body() {
    final it = controller.commentableProducts;
    return ListView.separated(
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return CommentItem(
          data,
          controller: controller.getEditingWithProductId(data.productId ?? ''),
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 1);
      },
      itemCount: it.length,
    );
  }
}
