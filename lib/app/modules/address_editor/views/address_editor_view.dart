import 'package:efshop/app/models/address_req.dart';
import 'package:efshop/app/models/error_res.dart';
import 'package:efshop/app/modules/address_picker/views/address_picker_view.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/address_editor_controller.dart';

class AddressEditorView extends GetView<AddressEditorController> {
  const AddressEditorView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // title: const Text('新增7-11門市'),
        title: Text(controller.title),
        centerTitle: true,
        actions: _actions().toList(growable: false),
      ),
      body: Theme(
        data: ThemeData(
          inputDecorationTheme: const InputDecorationTheme(
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: _children().toList(growable: false),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _actions() sync* {
    yield TextButton(
      onPressed: _submit,
      child: const Text(
        '儲存',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray98,
        ),
        softWrap: false,
      ),
    );
  }

  Future<void> _submit() async {
    try {
      Get.showLoading();
      final msg = await controller.submit();
      await controller.addressProvider.fetchAddress();
      Get.back();
      // 顯示成功訊息
      // await Get.showAlert(msg.message ?? '');
      Get.back(result: msg);
    } on ErrorRes catch (e) {
      Get.back();
      Get.showAlert(e.error ?? '');
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
  }

  Future<void> _onPressed() async {
    final req = AddressReq(
      // action: CrudType.create.index,
      type: controller.addressType.index,
    );
    Get.parameters = {
      Keys.data: req.toRawJson(),
    };
    final res = await SizedBox(
      height: 494.dh,
      child: const AddressPickerView(),
    ).sheet(
      enableDrag: true,
      ignoreSafeArea: true,
    );
    if (res != null) {
      controller.draftCopyWith(res);
    }
  }

  Iterable<Widget> _children() sync* {
    yield const Divider(
      thickness: 1,
      height: 1,
      color: EfColors.grayEF,
    );
    yield ListTile(
      onTap: controller.receiverNameEnabled
          ? null
          : () => Get.showAlert('無法修改收件人姓名'),
      tileColor: Colors.white,
      title: TextFormField(
        enabled: controller.receiverNameEnabled,
        initialValue: controller.data.receiverName ?? '',
        onChanged: (value) => controller.draft.receiverName = value,
        // controller: controller.nameController,
        decoration: const InputDecoration(
          contentPadding: EdgeInsets.zero,
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          hintText: '收件人姓名',
          hintStyle: TextStyle(
            fontSize: 15,
            color: EfColors.grayD5,
          ),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '必填欄位';
          }
          return null;
        },
      ),
    );
    yield const Divider(
      thickness: 1,
      height: 1,
      color: EfColors.grayEF,
    );
    yield ListTile(
      onTap: _onPressed,
      tileColor: Colors.white,
      title: Obx(() {
        return Text(
          // '選擇7-11取件門市',
          // '選擇${controller.addressType.display}取件門市',
          controller.displayStoreName,
          style: const TextStyle(
            fontSize: 15,
            color: EfColors.gray6B,
          ),
          softWrap: false,
          overflow: TextOverflow.ellipsis,
        );
      }),
      trailing: const Icon(
        Icons.chevron_right,
        color: EfColors.gray98,
      ),
    );
    if (superMarketList.contains(controller.addressType)) {
      yield const Text(
        '若常用門市沒有在列表中，可能是該門市已暫停提供取貨服務。',
        style: TextStyle(
          fontSize: 12,
          color: EfColors.gray94,
        ),
        softWrap: false,
      ).paddingAll(14);
    }
    if ([AddressType.home].contains(controller.addressType)) {
      yield const Divider(
        thickness: 1,
        height: 1,
        color: EfColors.grayEF,
      );
      yield Container(
        constraints: BoxConstraints(
          minHeight: 142.dh,
        ),
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: TextFormField(
          // initialValue: controller.data.address ?? '',
          initialValue: controller.data.addressTail,
          decoration: const InputDecoration(
            fillColor: Colors.white,
            filled: true,
            contentPadding: EdgeInsets.symmetric(
              horizontal: 14,
              vertical: 14,
            ),
            border: InputBorder.none,
            focusedBorder: InputBorder.none,
            hintText: '詳細地址',
            hintStyle: TextStyle(
              fontSize: 15,
              color: EfColors.grayD5,
            ),
          ),
          onChanged: (value) {
            controller.draft.address = value;
          },
        ),
      );
    }
  }
}
