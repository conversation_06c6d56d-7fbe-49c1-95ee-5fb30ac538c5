import 'package:efshop/app/models/address_req.dart';
import 'package:efshop/app/models/members_address_res.dart';
import 'package:efshop/app/models/members_addresses_post_req.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/address_provider.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';

class AddressEditorController extends GetxController with StateMixin<String> {
  final AddressProvider addressProvider;
  WabowProvider get wabowProvider => addressProvider.wabowProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  //
  final _addressType = AddressType.sevenEleven.obs;
  AddressType get addressType => _addressType.value;
  //
  final _action = CrudType.create.obs;
  CrudType get action => _action.value;
  //
  final _draft = MembersAddressesPostReq().obs;
  MembersAddressesPostReq get draft => _draft.value;
  //
  final _data = MembersAddressRes().obs;
  MembersAddressRes get data => _data.value;
  //
  final _id = ''.obs;
  String get id => _id.value;
  //
  String get title {
    Iterable<String> children() sync* {
      yield action.display;
      yield addressType.title;
    }

    return children().join();
  }

  String get displayStoreName {
    if (draft.isValid) {
      if (addressType == AddressType.home) {
        final res = addressProvider.getCityTown(draft.zipcode ?? '');
        return '${res.key}${res.value}';
      }
      return draft.display;
    }
    if (data.isValid) {
      if (addressType == AddressType.home) {
        return '${data.city}${data.town}';
      }
      return data.display;
    }
    if (addressType == AddressType.home) {
      return '縣市、市區鄉鎮';
    }
    // return '請選擇門市';
    return '選擇${addressType.display}取件門市';
  }

  bool get receiverNameEnabled {
    // 名字不可包含數字
    // 宅配不可修改收件人姓名
    // if (addressType == AddressType.home && action == CrudType.update) {
    //   return false;
    // }
    // if (superMarketList.contains(addressType)) {
    //   return false;
    // }
    return true;
  }

  AddressEditorController({
    required this.addressProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.data)) {
      final req = AddressReq.fromRawJson(Get.parameters[Keys.data] ?? '{}');
      _addressType.value = AddressType.values.elementAt(req.type ?? 0);
      _id.value = req.id ?? '';
    }
    if (id.isNotEmpty) {
      _action.value = CrudType.update;
      final box = boxProvider.getGsBox(Boxes.address.name);
      final json = box.read(id);
      _data.value = MembersAddressRes.fromJson(json);
      _draft.value = data.toPostReq();
    } else {
      _action.value = CrudType.create;
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<MessageRes> submit() async {
    draft.validate(addressType);
    // 如果沒有預設地址，就把目前設為預設
    final it = addressProvider.getAddressList((e) {
      return e.type == addressType.value && e.isDefault == true;
    });
    if (it.isEmpty) {
      draft.isDefault = true;
    }
    // 整理地址
    final res = addressProvider.getCityTown(draft.zipcode ?? '');
    final prefix = '${res.key ?? ''}${res.value ?? ''}';
    draft.address ??= '';
    if (draft.address!.contains(prefix) == false) {
      draft.address = prefix + draft.address!;
    }
    if (action == CrudType.create) {
      return wabowProvider.postMembersAddresses(addressType, draft);
    } else if (action == CrudType.update) {
      return wabowProvider.patchMembersAddresses(addressType, id, draft);
    }
    throw UnimplementedError();
  }

  void draftCopyWith(MembersAddressesPostReq other) {
    // 解析地址
    if (other.isAddressLeadingOnly) {
      other.address = other.addressLeading + draft.addressTail;
    }
    // final res = addressProvider.getCityTown(draft.zipcode ?? '');
    // final prefix = '${res.key ?? ''}${res.value ?? ''}';
    // final address = draft.address ?? '';
    // // 如果地址有前綴，就把前綴去掉
    // if (address.contains(prefix)) {
    //   final trail = address.replaceFirst(prefix, '');
    //   other.address = (other.address ?? '') + trail;
    // }
    _draft.value = draft.copyWith(
      address: other.address,
      isDefault: other.isDefault,
      receiverName: other.receiverName,
      storeId: other.storeId,
      storeName: other.storeName,
      zipcode: other.zipcode,
    );
  }
}
