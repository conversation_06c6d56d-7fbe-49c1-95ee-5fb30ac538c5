import 'package:efshop/app/models/faq_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';

class FaqChildrenController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  final _id = ''.obs;
  String get id => _id.value;
  final data = <FaqRes>[].obs;

  FaqChildrenController({required this.wabowProvider});

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id] ?? '';
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final box = boxProvider.getGsBox(Boxes.faq.name);
      if (box.hasData(id)) {
        final it = List.from(box.read(id)).map((e) => FaqRes.fromJson(e));
        data.assignAll(it);
      }
      if (data.isNotEmpty) {
        change('', status: RxStatus.success());
      } else {
        change('', status: RxStatus.empty());
      }
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
