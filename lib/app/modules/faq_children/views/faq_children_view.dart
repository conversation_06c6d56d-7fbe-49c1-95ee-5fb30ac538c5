import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/faq_children_controller.dart';

class FaqChildrenView extends GetView<FaqChildrenController> {
  const FaqChildrenView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(controller.id),
        centerTitle: true,
      ),
      body: controller.obx((state) => Obx(() => _body())),
    );
  }

  Widget _body() {
    final it = controller.data;
    return ListView.separated(
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          title: Text(
            data.name ?? '',
            style: const TextStyle(
              fontSize: 15,
              color: EfColors.gray6B,
            ),
            softWrap: false,
          ),
          onTap: () {
            Get.toNamed(Routes.FAQ_DETAIL, parameters: {
              Keys.data: data.toRawJson(),
            });
          },
          trailing: const Icon(
            Icons.chevron_right,
          ),
        );
      },
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemCount: it.length,
    );
  }
}
