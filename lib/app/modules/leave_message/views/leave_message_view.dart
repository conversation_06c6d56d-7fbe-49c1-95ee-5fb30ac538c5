import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/leave_message_controller.dart';

class LeaveMessageView extends GetView<LeaveMessageController> {
  const LeaveMessageView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('客服訊息'),
        centerTitle: true,
        actions: _actions().toList(growable: false),
      ),
      body: controller.obx(
        (state) => _body(),
        onError: (error) {
          return ErrorButton(
            error,
            onTap: controller.onRefresh,
          );
        },
      ),
    );
  }

  Iterable<Widget> _actions() sync* {
    yield TextButton(
      onPressed: () {
        Get.toNamed(Routes.MESSAGE_HISTORY, parameters: {
          Keys.id: controller.id,
        });
      },
      child: const Text(
        '紀錄',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray98,
        ),
        softWrap: false,
      ),
    );
  }

  Future<void> _submit() async {
    Get.showLoading();
    try {
      await controller.submit();
      Get.back();
      await Get.showAlert('留言送出成功');
      Get.back();
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
  }

  Widget _body() {
    return Background(
      alignment: Alignment.topCenter,
      background: SingleChildScrollView(
        padding: const EdgeInsets.only(
          bottom: 8,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: _children().toList(growable: false),
        ),
      ),
      child: SafeArea(
        child: Align(
          alignment: Alignment.bottomCenter,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: EfColors.primary,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero,
              ),
              minimumSize: const Size.fromHeight(54),
            ),
            onPressed: _submit,
            child: const Text(
              '送出',
              style: TextStyle(
                fontSize: 15,
                color: Colors.white,
              ),
              softWrap: false,
            ),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield ListTile(
      onTap: _showPicker,
      title: Obx(() {
        return Text(
          // '問題類型',
          controller.title,
          style: const TextStyle(
            fontSize: 15,
            color: EfColors.gray6B,
          ),
          softWrap: false,
        );
      }),
      trailing: const Icon(
        Icons.chevron_right,
      ),
    );
    yield const Divider(height: 1);
    yield Container(
      padding: const EdgeInsets.all(14),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      constraints: const BoxConstraints(
        minHeight: 142,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          TextFormField(
            controller: controller.editing,
            decoration: const InputDecoration(
              contentPadding: EdgeInsets.zero,
              hintText: '問題內容',
              hintStyle: TextStyle(
                fontSize: 13,
                color: EfColors.grayD5,
              ),
              border: InputBorder.none,
              focusedBorder: InputBorder.none,
              enabledBorder: InputBorder.none,
              // suffix: IconButton(
              //   alignment: Alignment.topCenter,
              //   padding: EdgeInsets.zero,
              //   iconSize: 32.dw,
              //   onPressed: () {
              //     controller.editing.text = '';
              //   },
              //   icon: Icon(
              //     Icons.cancel,
              //     color: EfColors.grayD5,
              //     size: 22.dw,
              //   ),
              // ),
              // suffixIcon: IconButton(
              //   alignment: Alignment.topCenter,
              //   padding: EdgeInsets.zero,
              //   iconSize: 32,
              //   onPressed: () {
              //     controller.editing.text = '';
              //   },
              //   icon: const Icon(
              //     Icons.cancel,
              //     color: EfColors.grayD5,
              //     size: 16,
              //   ),
              // ),
            ),
            style: const TextStyle(
              fontSize: 15,
              color: EfColors.gray6B,
            ),
            maxLines: 5,
            minLines: 5,
          ).expanded(),
          IconButton(
            // alignment: Alignment.center,
            padding: EdgeInsets.zero,
            // iconSize: 32,
            onPressed: () {
              controller.editing.text = '';
            },
            icon: const Icon(
              Icons.cancel,
              color: EfColors.gray,
              // size: 22,
            ),
          ),
        ],
      ),
    );
    yield const SizedBox(height: 14);
    yield const Text(
      Constants.servicePhone,
      style: TextStyle(
        fontSize: 12,
        color: EfColors.gray94,
      ),
      softWrap: false,
    ).paddingSymmetric(horizontal: 14);
    yield const SizedBox(height: 4);
    yield const Text(
      Constants.serviceTime,
      style: TextStyle(
        fontSize: 12,
        color: EfColors.gray94,
      ),
      softWrap: false,
    ).paddingSymmetric(horizontal: 14);
  }

  Future<void> _showPicker() async {
    final children = controller.questionTypes
        .where((element) => element != QuestionType.exchange)
        .map((element) {
      return ListTile(
        title: Text(element.display),
        onTap: () {
          Get.back(result: element);
        },
      );
    });

    try {
      final res = await Column(
        mainAxisSize: MainAxisSize.min,
        children: children.toList(growable: false),
      ).sheet<QuestionType>();
      if (res != null) {
        controller.title = res.display;
        controller.questionType = res;
      }
    } catch (e) {
      Get.showAlert(e.toString());
    }
  }
}
