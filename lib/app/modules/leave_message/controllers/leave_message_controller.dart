import 'dart:async';

import 'package:efshop/app/models/members_orders_questions_post_req.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';

class LeaveMessageController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;

  final _title = '問題類型'.obs;
  String get title => _title.value;
  set title(String value) => _title.value = value;

  final _id = ''.obs;
  String get id => _id.value;
  final _disposable = Completer();

  final questionTypes = QuestionType.values.obs;
  final _questionType = QuestionType.other.obs;
  QuestionType get questionType => _questionType.value;
  set questionType(QuestionType value) => _questionType.value = value;
  final editing = TextEditingController();

  LeaveMessageController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id]!;
    }
    // onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  // 客服紀錄 getMembersMessagesQuestions
  Future<MessageRes> submit() {
    if (editing.text.isEmpty) {
      throw '請填寫您的問題';
    }
    return wabowProvider.postMembersOrdersQuestions(
      id,
      MembersOrdersQuestionsPostReq(
        typeId: questionType.value,
        question: editing.text,
      ),
    );
  }
}
