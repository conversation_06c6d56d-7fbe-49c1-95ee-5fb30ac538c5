import 'package:efshop/app/components/error_button.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../controllers/chat_controller.dart';

class ChatView extends GetView<ChatController> {
  final String? tag;

  ChatView({super.key}) : tag = '${DateTime.now().hashCode}' {
    Get.lazyPut<ChatController>(
      () => ChatController(
        prefProvider: Get.find(),
      ),
      tag: tag,
      fenix: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChatController>(
      init: Get.find<ChatController>(tag: tag),
      tag: tag,
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('客服'),
            centerTitle: true,
          ),
          body: controller.obx(
            (state) {
              return _body();
            },
            onError: (error) {
              return ErrorButton(
                error,
                onTap: controller.onRefresh,
              );
            },
          ),
        );
      },
    );
  }

  Widget _body() {
    return SafeArea(
      bottom: true,
      child: WebViewWidget(controller: controller.webViewController),
    );
  }
}
