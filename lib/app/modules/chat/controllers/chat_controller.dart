import 'dart:async';
import 'dart:io';

import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/keys.dart';
import 'package:efshop/utility.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:webview_cookie_manager/webview_cookie_manager.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class ChatController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final PrefProvider prefProvider;
  late final WebViewController webViewController;
  Logger get logger => prefProvider.logger;

  // url
  final _url = ''.obs;
  String get url => _url.value;
  set url(String value) => _url.value = value;

  final cookieManager = WebviewCookieManager();

  ChatController({
    required this.prefProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _initWebViewController();
  }

  // void _initPeriodicSaveCookies() {
  //   Stream.periodic(10.seconds)
  //       .asyncMap((event) => _saveCookies())
  //       .takeUntil(_disposable.future)
  //       .listen((event) {});
  // }

  void _initWebViewController() {
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }
    webViewController = WebViewController.fromPlatformCreationParams(params);
    if (webViewController.platform is AndroidWebViewController) {
      // AndroidWebViewController.enableDebugging(true);
      // final onShowFileSelector = (handler) async {
      //   final file = await prefProvider.pickFile();
      //   if (file != null) {
      //     handler(file);
      //   }
      // };
      final androidWebViewController =
          webViewController.platform as AndroidWebViewController;
      androidWebViewController.setMediaPlaybackRequiresUserGesture(false);
      androidWebViewController.setOnShowFileSelector(_onShowFileSelector);
    }
    webViewController.setJavaScriptMode(JavaScriptMode.unrestricted);
    
    // 添加 console message 處理器來顯示 JavaScript console.log
    if (webViewController.platform is AndroidWebViewController) {
      final androidWebViewController = webViewController.platform as AndroidWebViewController;
      androidWebViewController.setOnConsoleMessage((JavaScriptConsoleMessage message) {
        logger.d('WebView Console [${message.level.name}]: ${message.message}');
      });
    } else if (webViewController.platform is WebKitWebViewController) {
      // iOS WebKit 也可以通過 JavaScript 注入的方式來捕獲 console.log
      // 但需要在頁面載入完成後注入 JavaScript 代碼
    }
    
    webViewController.setNavigationDelegate(NavigationDelegate(
      onPageFinished: (url) {
        logger.d('onPageFinished: $url');
        _saveCookies();
        // _injectConsoleLogger(); // 為 iOS 注入 console.log 捕獲代碼
        // _triggerWelcomeMessage();
      },
    ));
  }

  Future<List<String>> _onShowFileSelector(FileSelectorParams params) async {
    if (params.acceptTypes.isNotEmpty) {
      final firstType = params.acceptTypes.first;
      final imageSource =
          firstType.isEmpty ? ImageSource.gallery : ImageSource.camera;
      final photo = await getImage(imageSource);
      if (photo.isNotEmpty) {
        // return [photo];
        final file = File(photo);
        // read bytes
        final bytes = await file.readAsBytes();
        final tempDir = await getTemporaryDirectory();
        final newName = 'image_${DateTime.now().microsecondsSinceEpoch}.jpg';
        final filePath = tempDir.uri.resolve('./$newName');
        final newFile = await File.fromUri(filePath).create(recursive: true);
        await newFile.writeAsBytes(bytes, flush: true);
        return [file.uri.toString()];
      }
    }
    return [];
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final queryParameters = {...Constants.uriWebChat.queryParameters};
      if (prefProvider.isLogin) {
        queryParameters[Keys.bbCode] = prefProvider.appierAuthCode;
      } else {
        await _loadCookies();
      }
      final cookies1 = await _getCookies();
      for (var element in cookies1) {
        logger.d('get cookie1: $element');
      }
      // https://chat.botbonnie.com?standalone=true&appId=page-3aa88108fbb24ad2a0b259b4&bb_code=crm-auth-4998bfba4942eb763f81e10f27d5235cd5ae1b724f26661321a1c35c0dba0858
      final uri =
          Constants.uriWebChat.replace(queryParameters: queryParameters);
      await webViewController.loadRequest(uri);
      change('', status: RxStatus.success());
      final cookies2 = await _getCookies();
      for (var element in cookies2) {
        logger.d('get cookie2: $element');
      }
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  ///
  /// 讀取 cookie
  ///
  Future<void> _loadCookies() async {
    logger.d('load cookies');
    final cookies = prefProvider.cookies.map((e) {
      logger.d('load cookie: $e');
      return Cookie.fromSetCookieValue(e);
    });
    final currentUrl = Constants.uriWebChat.origin;
    await cookieManager.setCookies(cookies.toList(), origin: currentUrl);
  }

  Future<List<Cookie>> _getCookies() {
    final currentUrl = Constants.uriWebChat.origin;
    return cookieManager.getCookies(currentUrl);
  }

  ///
  /// 儲存 cookie
  ///
  Future<void> _saveCookies() async {
    await 1.seconds.delay();
    logger.d('save cookies');
    final currentUrl = Constants.uriWebChat.origin;
    final gotCookies = await cookieManager.getCookies(currentUrl);
    for (var element in gotCookies) {
      logger.d('save cookie: $element');
    }
    prefProvider.cookies = gotCookies.map((e) => e.toString());
  }

  ///
  /// 觸發歡迎訊息
  ///
  Future<void> _triggerWelcomeMessage() async {
    try {
      if (!prefProvider.shouldTriggerWelcomeMessage()) {
        logger.d('歡迎訊息今天已經觸發過，跳過觸發');
        return;
      }

      logger.d('開始觸發歡迎訊息');
      
      // JavaScript 程式碼，等待 BB 物件可用後觸發歡迎訊息
      final jsCode = '''
(function waitForBB() {
    console.log("等待 BB 物件...");
    if (typeof BB !== "undefined" && BB.get) {
        console.log("BB 物件已就緒，觸發歡迎訊息");
        // 獲取 userId 並觸發歡迎訊息
        BB.get("userId", function(userId) {
            console.log("webchatId:", userId);
            BB.trigger("messaging", {
                userId: userId,
                moduleId: "${Constants.welcomeModuleId}",
                source: "webchat"
            });
        });
        console.log("歡迎訊息已觸發");
    } else {
        setTimeout(waitForBB, 300);
    }
})();
''';

      await webViewController.runJavaScript(jsCode);
      
      // 記錄今天已經觸發過歡迎訊息
      prefProvider.setWelcomeMessageTriggered();
      
      logger.d('歡迎訊息觸發完成');
    } catch (e) {
      logger.e('觸發歡迎訊息時發生錯誤: $e');
    }
  }

  ///
  /// 注入 JavaScript 代碼來捕獲 console.log (主要用於 iOS)
  ///
  Future<void> _injectConsoleLogger() async {
    if (webViewController.platform is! AndroidWebViewController) {
      try {
        // 先添加 JavaScript Channel
        webViewController.addJavaScriptChannel(
          'FlutterConsole',
          onMessageReceived: (JavaScriptMessage message) {
            logger.d('WebView Console: ${message.message}');
          },
        );
        
        final jsCode = '''
(function() {
    var originalLog = console.log;
    var originalWarn = console.warn;
    var originalError = console.error;
    var originalInfo = console.info;
    
    console.log = function() {
        var message = Array.prototype.slice.call(arguments).join(' ');
        if (window.FlutterConsole && window.FlutterConsole.postMessage) {
            window.FlutterConsole.postMessage('log: ' + message);
        }
        originalLog.apply(console, arguments);
    };
    
    console.warn = function() {
        var message = Array.prototype.slice.call(arguments).join(' ');
        if (window.FlutterConsole && window.FlutterConsole.postMessage) {
            window.FlutterConsole.postMessage('warn: ' + message);
        }
        originalWarn.apply(console, arguments);
    };
    
    console.error = function() {
        var message = Array.prototype.slice.call(arguments).join(' ');
        if (window.FlutterConsole && window.FlutterConsole.postMessage) {
            window.FlutterConsole.postMessage('error: ' + message);
        }
        originalError.apply(console, arguments);
    };
    
    console.info = function() {
        var message = Array.prototype.slice.call(arguments).join(' ');
        if (window.FlutterConsole && window.FlutterConsole.postMessage) {
            window.FlutterConsole.postMessage('info: ' + message);
        }
        originalInfo.apply(console, arguments);
    };
})();
''';
        await webViewController.runJavaScript(jsCode);
      } catch (e) {
        logger.e('注入 console logger 失敗: $e');
      }
    }
  }

  ///
  /// 清空 cookies
  ///
  // Future<void> _removeCookie() async {
  //   try {
  //     prefProvider.cookies = [];
  //     final currentUrl = Constants.uriWebChat.origin;
  //     await cookieManager.removeCookie(currentUrl);
  //   } catch (e) {
  //     logger.e(e);
  //   }
  // }
}
