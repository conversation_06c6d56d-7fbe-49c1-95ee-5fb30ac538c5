import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../controllers/ef_web_controller.dart';

class EfWebView extends GetView<EfWebController> {
  final String? tag;
  final PreferredSizeWidget? appBar;

  EfWebView({
    super.key,
    this.appBar,
  }) : tag = '${DateTime.now().hashCode}' {
    Get.lazyPut<EfWebController>(
      () => EfWebController(
        logger: Get.find(),
      ),
      tag: tag,
      fenix: true,
    );
    initializeController().then((value) {
      controller.fetchParameters();
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EfWebController>(
      init: Get.find<EfWebController>(tag: tag),
      tag: tag,
      builder: (controller) {
        return Scaffold(
          appBar: appBar ??
              AppBar(
                title: Obx(() => Text(controller.title)),
                centerTitle: true,
              ),
          body: SafeArea(
            child: controller.obx(
              (state) => _body(),
              // onEmpty: _empty(),
            ),
          ),
        );
      },
    );
  }

  Widget _body() {
    return WebViewWidget(controller: controller.webViewController);
  }
}
