import 'package:efshop/app/components/address_item.dart';
import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/models/address_req.dart';
import 'package:efshop/app/models/error_res.dart';
import 'package:efshop/app/models/members_address_res.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/address_controller.dart';

class AddressView extends GetView<AddressController> {
  const AddressView({super.key});
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: controller.tabs.length,
      child: Scaffold(
        appBar: AppBar(
          actions: _actions().toList(growable: false),
          title: const Text('管理超商或收貨地址'),
          centerTitle: true,
          bottom: TabBar(
            tabAlignment: TabAlignment.fill,
            tabs: _tabs().toList(growable: false),
            indicatorColor: EfColors.primary,
            onTap: (value) {
              controller.currentTab = controller.tabs.elementAt(value);
            },
          ),
        ),
        // 多個
        body: Obx(() => _body()),
        // 單個
        // body: controller.obx((state) {
        //   return RefreshIndicator(
        //     onRefresh: controller.onRefresh,
        //     child: Obx(() => _list(controller.data)),
        //   );
        // }),
      ),
    );
  }

  Future<void> _createPressed() async {
    if (controller.canCreate) {
      final req = AddressReq(
        type: controller.currentTab.index,
      );
      final res = await Get.toNamed(Routes.ADDRESS_EDIT, parameters: {
        Keys.data: req.toRawJson(),
      });
      if (res is MessageRes && res.status == true) {
        // await controller.onRefresh();
      }
    } else {
      Get.showAlert('超商或收貨地址已達上限');
    }
  }

  Iterable<Widget> _actions() sync* {
    yield TextButton(
      onPressed: _createPressed,
      child: const Text(
        '新增',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray98,
        ),
        softWrap: false,
      ),
    );
  }

  Iterable<Widget> _tabs() {
    return controller.tabs.map((element) => Tab(text: element.display));
  }

  Widget _body() {
    controller.currentTab;
    return TabBarView(
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    for (final tab in controller.tabs) {
      final it = controller.getAddressList(tab.value);
      yield RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: _list(it),
      );
    }
  }

  Widget _list(Iterable<MembersAddressRes> it) {
    // final it = controller.data;
    if (it.isNotEmpty) {
      return ListView.separated(
        itemCount: it.length,
        separatorBuilder: (context, index) {
          return const SizedBox(height: 10);
        },
        itemBuilder: (context, index) {
          final data = it.elementAt(index);
          return AddressItem(
            data,
            onDefaultPressed: () async {
              try {
                Get.showLoading();
                final res = await controller.makeDefaultAddress(data);
                Get.back();
              } catch (e) {
                Get.back();
                Get.showAlert(e.toString());
              }
            },
            onEditPressed: () {
              final req = AddressReq(
                type: controller.currentTab.index,
                id: data.id,
              );
              Get.toNamed(Routes.ADDRESS_EDIT, parameters: {
                Keys.data: req.toRawJson(),
              });
            },
            onDeletePressed: () async {
              final select = await Get.showConfirm('即將刪除地址', textConfirm: '刪除');
              if (select == Button.confirm) {
                try {
                  Get.showLoading();
                  final res = await controller.deleteAddress(data);
                  Get.back();
                  // Get.showAlert(res.message ?? '');
                } on ErrorRes catch (e) {
                  throw e.error ?? '';
                } catch (e) {
                  Get.back();
                  Get.showAlert(e.toString());
                }
              }
            },
          );
        },
      );
    }
    return Background(
      background: ListView(),
      child: const Center(
        child: Text(
          '目前沒有資料',
          style: TextStyle(
            fontSize: 20,
            color: EfColors.gray,
          ),
        ),
      ),
    );
  }
}
