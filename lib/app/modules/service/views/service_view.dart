import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/service_controller.dart';

class ServiceView extends GetView<ServiceController> {
  const ServiceView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('客服訊息'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) => _body(),
        onError: (error) {
          return ErrorButton(
            error,
            onTap: controller.onRefresh,
          );
        },
      ),
    );
  }

  Widget _body() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(
        bottom: 8,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield _header();
    yield const SizedBox(height: 8);
    yield ListTile(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16,
      ),
      title: const Text(
        '所有問題',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray6B,
        ),
        softWrap: false,
      ),
      onTap: () => Get.toNamed(Routes.FAQ),
      trailing: const Icon(
        Icons.chevron_right,
      ),
    );
    yield const SizedBox(height: 8);
    yield const ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: 16,
      ),
      title: Text(
        '常見問題',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray6B,
          fontWeight: FontWeight.w600,
        ),
        softWrap: false,
      ),
    );
    // yield const Divider(height: 1);
    yield const SizedBox(height: 1);
    // final it = controller.getFaqWithSubject('常見問題');
    final it = controller.getFlatFaq().where((element) => element.pin == 'y');
    yield ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return ListTile(
          onTap: () {
            Get.toNamed(Routes.FAQ_DETAIL, parameters: {
              Keys.data: data.toRawJson(),
            });
          },
          title: Text(
            data.name ?? '',
            style: const TextStyle(
              fontSize: 13,
              color: EfColors.gray6B,
            ),
            softWrap: false,
          ),
          trailing: const Icon(
            Icons.chevron_right,
          ),
        );
      },
      // separatorBuilder: (context, index) => const Divider(height: 1),
      separatorBuilder: (context, index) => const SizedBox(height: 1),
      itemCount: it.length,
    );
  }

  Widget _header() {
    Iterable<Widget> children() sync* {
      yield const Text(
        '客服聯繫方式\n請您於下單後至【訂單詳情】點選【留言】',
        style: TextStyle(
          fontSize: 13,
          color: EfColors.gray6B,
          height: 1.5,
        ),
        textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      );
      yield const SizedBox(height: 4);
      yield const Text(
        Constants.servicePhone,
        style: TextStyle(
          fontSize: 12,
          color: EfColors.gray94,
        ),
        softWrap: false,
      );
      yield const SizedBox(height: 2);
      yield const Text(
        Constants.serviceTime,
        style: TextStyle(
          fontSize: 12,
          color: EfColors.gray94,
        ),
        softWrap: false,
      );
      // yield const Text.rich(
      //   TextSpan(
      //     text: '客服時段：09:00~17:30',
      //     style: TextStyle(
      //       fontSize: 12,
      //       color: EfColors.gray94,
      //     ),
      //     children: [
      //       TextSpan(
      //         text: '(週六日及國定假日除外)',
      //         style: TextStyle(
      //           color: EfColors.gray94,
      //         ),
      //       ),
      //     ],
      //   ),
      // );
    }

    return Container(
      width: double.infinity,
      // height: 110.dh,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }
}
