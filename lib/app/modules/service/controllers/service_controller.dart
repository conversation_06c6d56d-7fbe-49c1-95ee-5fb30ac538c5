import 'package:efshop/app/models/faq_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:get/get.dart';

class ServiceController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;

  ServiceController({required this.wabowProvider});

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final box = boxProvider.getGsBox(Boxes.faq.name);
      final res = await wabowProvider.getFaq();
      box.erase();
      for (final it in res) {
        final key = it.first.subject ?? '';
        final value = List<FaqRes>.from(it)
            .map((e) => e.toJson())
            .toList(growable: false);
        box.write(key, value);
      }
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  // Iterable<FaqRes> getFaqWithSubject(String subject) sync* {
  //   final box = boxProvider.getGsBox(Boxes.faq.name);
  //   if (box.hasData(subject)) {
  //     for (final element in box.read(subject)) {
  //       yield FaqRes.fromJson(element);
  //     }
  //   }
  // }

  // TODO: use iterable map
  Iterable<FaqRes> getFlatFaq() sync* {
    final box = boxProvider.getGsBox(Boxes.faq.name);
    for (final it in box.getValues<Iterable>()) {
      for (final element in it) {
        yield FaqRes.fromJson(element);
      }
    }
    // for (var key in box.getKeys<Iterable<String>>()) {
    //   for (var element in box.read(key)) {
    //     yield FaqRes.fromJson(element);
    //   }
    // }
  }
}
