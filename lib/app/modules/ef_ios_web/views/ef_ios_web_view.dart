import 'package:efshop/constants.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:webview_flutter_platform_interface/webview_flutter_platform_interface.dart';

import '../controllers/ef_ios_web_controller.dart';

class EfIosWebView extends GetView<EfIosWebController> {
  EfIosWebView({super.key}) {
    Get.lazyPut<EfIosWebController>(
      () => EfIosWebController(
        wabowProvider: Get.find(),
      ),
      fenix: true,
    );
    initializeController().then((value) {
      final controller = Get.find<EfIosWebController>();
      controller.url = Constants.uriCart.toString();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('EfIosWebView'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) {
          return PlatformWebViewWidget(
            PlatformWebViewWidgetCreationParams(
                controller: controller.controller),
          ).build(context);
        },
        onError: (error) {
          return Center(
            child: Text(
              error.toString(),
              style: const TextStyle(color: Colors.red),
            ),
          );
        },
      ),
    );
  }
}
