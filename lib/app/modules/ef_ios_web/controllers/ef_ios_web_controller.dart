import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:webview_flutter_platform_interface/webview_flutter_platform_interface.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class EfIosWebController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final WabowProvider wabowProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  late final PlatformWebViewController controller;
  // url
  final _url = ''.obs;
  String get url => _url.value;
  set url(String value) => _url.value = value;

  EfIosWebController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _initWebViewController();
    _url.stream
        .where((event) => event.isNotEmpty)
        .asyncMap((event) => _loadFromUrl())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<void> _loadFromUrl() async {
    await controller.loadRequest(LoadRequestParams(
      uri: Uri.parse(url),
      // 購物車頁面需要的 header
      headers: <String, String>{
        'Authorization': 'Bearer ${prefProvider.token}',
        'device_type': Platform.operatingSystem,
        'app_version': prefProvider.packageInfo.version,
      },
    ));
  }

  void _initWebViewController() {
    controller = PlatformWebViewController(
      WebKitWebViewControllerCreationParams(allowsInlineMediaPlayback: true),
    );
    controller.setJavaScriptMode(JavaScriptMode.unrestricted);
    controller.setBackgroundColor(const Color(0x80000000));
    controller.setPlatformNavigationDelegate(
      PlatformNavigationDelegate(
        const PlatformNavigationDelegateCreationParams(),
      )
        ..setOnProgress((int progress) {
          debugPrint('WebView is loading (progress : $progress%)');
        })
        ..setOnPageStarted((String url) {
          debugPrint('Page started loading: $url');
        })
        ..setOnPageFinished((String url) {
          debugPrint('Page finished loading: $url');
        })
        ..setOnHttpError((HttpResponseError error) {
          debugPrint('Error occurred on page: ${error.response?.statusCode}');
        })
        ..setOnWebResourceError((WebResourceError error) {
          debugPrint('''
Page resource error:
  code: ${error.errorCode}
  description: ${error.description}
  errorType: ${error.errorType}
  isForMainFrame: ${error.isForMainFrame}
  url: ${error.url}
          ''');
        })
        ..setOnNavigationRequest((NavigationRequest request) {
          if (request.url.startsWith('https://www.youtube.com/')) {
            debugPrint('blocking navigation to ${request.url}');
            return NavigationDecision.prevent;
          }
          debugPrint('allowing navigation to ${request.url}');
          return NavigationDecision.navigate;
        })
        ..setOnUrlChange((UrlChange change) {
          debugPrint('url change to ${change.url}');
        })
        ..setOnHttpAuthRequest((HttpAuthRequest request) {
          // openDialog(request);
        }),
    );
    controller.addJavaScriptChannel(JavaScriptChannelParams(
      name: 'Toaster',
      onMessageReceived: (JavaScriptMessage message) {
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(content: Text(message.message)),
        // );
      },
    ));
    controller.setOnPlatformPermissionRequest(
      (PlatformWebViewPermissionRequest request) {
        debugPrint(
          'requesting permissions for ${request.types.map((WebViewPermissionResourceType type) => type.name)}',
        );
        request.grant();
      },
    );
    // controller.loadRequest(LoadRequestParams(
    //   uri: Uri.parse('https://flutter.dev'),
    // ));
    controller
        .setOnScrollPositionChange((ScrollPositionChange scrollPositionChange) {
      debugPrint(
        'Scroll position change to x = ${scrollPositionChange.x}, y = ${scrollPositionChange.y}',
      );
    });
  }
}
