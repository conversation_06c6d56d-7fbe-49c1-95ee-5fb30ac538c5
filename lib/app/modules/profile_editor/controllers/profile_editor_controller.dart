import 'dart:async';

import 'package:efshop/app/models/members_profile_patch_req.dart';
import 'package:efshop/app/models/members_profile_patch_res.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:stream_transform/stream_transform.dart';

class ProfileEditorController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  Logger get logger => wabowProvider.logger;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  // disposable
  final _disposable = Completer();
  // draft
  final _draft = MembersProfilePatchReq().obs;
  MembersProfilePatchReq get draft => _draft.value;
  // editing controller
  final nameEditing = TextEditingController();
  // focus node
  final nameFocusNode = FocusNode();

  String get email => prefProvider.profile.email ?? '';

  ProfileEditorController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // 忽略第一次
    nameEditing.watch().skip(1).takeUntil(_disposable.future).listen((event) {
      draft.fullname = nameEditing.text;
    });
    nameFocusNode.watch().takeUntil(_disposable.future).listen((event) {
      refreshDraft();
    });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    nameEditing.dispose();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final profile = prefProvider.profile;
      nameEditing.text = profile.fullname ?? '';
      _draft.value = profile.toDraft();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<MembersProfilePatchRes> submit() async {
    draft.validate();
    final res = await wabowProvider.patchMembersProfile(draft);
    // save to local
    if (res.profile != null) {
      prefProvider.profile = res.profile!;
    }
    return res;
  }

  void refreshDraft() {
    _draft.refresh();
  }
}
