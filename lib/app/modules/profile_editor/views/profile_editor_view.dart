import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/profile_editor_controller.dart';

class ProfileEditorView extends GetView<ProfileEditorController> {
  const ProfileEditorView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('個人資訊'),
        centerTitle: true,
        actions: _actions().toList(growable: false),
      ),
      body: controller.obx((state) => _body()),
    );
  }

  Iterable<Widget> _actions() sync* {
    yield TextButton(
      onPressed: _submit,
      child: const Text(
        '儲存',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray,
        ),
        softWrap: false,
      ),
    );
  }

  Future<void> _submit() async {
    try {
      Get.showLoading();
      final res = await controller.submit();
      // hide loading
      Get.back();
      // back to previous page
      Get.back();
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
  }

  Widget _body() {
    return SingleChildScrollView(
      child: Column(
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield ListTile(
      leading: const Text('姓名'),
      title: TextFormField(
        focusNode: controller.nameFocusNode,
        controller: controller.nameEditing,
        // initialValue: controller.data.fullname,
        decoration: const InputDecoration(
          contentPadding: EdgeInsets.zero,
          hintText: '請輸入姓名',
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
          errorBorder: InputBorder.none,
        ),
        style: const TextStyle(
          fontSize: 16,
          color: EfColors.gray,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '姓名是必填欄位';
          }
          return null;
        },
        onChanged: (value) {
          controller.logger.i('onChanged: $value');
          // 監控使用者輸入的文字
          controller.draft.fullname = value;
          controller.refreshDraft();
        },
        onSaved: (value) {
          controller.logger.i('onSaved: $value');
          // 將使用者輸入的文字保存到資料庫
        },
        onEditingComplete: () {
          controller.logger.i('onEditingComplete');
          // 提交表單
        },
        onFieldSubmitted: (value) {
          controller.logger.i('onFieldSubmitted: $value');
          // 移動到下一個輸入欄位
        },
      ),
      trailing: Obx(() {
        final fullname = controller.draft.fullname;
        if (fullname == null || fullname.isEmpty) {
          return const SizedBox.shrink();
        }
        if (!controller.nameFocusNode.hasFocus) {
          return const SizedBox.shrink();
        }
        return IconButton(
          padding: EdgeInsets.zero,
          onPressed: () {
            controller.logger.i('onPressed');
            // 清除輸入欄位的文字
            controller.nameEditing.clear();
            controller.refreshDraft();
          },
          icon: const Icon(Icons.cancel),
        );
      }),
      // trailing: IconButton(
      //   padding: EdgeInsets.zero,
      //   onPressed: () {
      //     controller.logger.i('onPressed');
      //     // 清除輸入欄位的文字
      //     controller.nameEditing.clear();
      //   },
      //   icon: const Icon(Icons.cancel),
      // ),
      // trailing: const Icon(Icons.chevron_right),
      // onTap: () {},
    );
    yield const Divider(height: 1);
    yield ListTile(
      leading: const Text('E-mail'),
      title: Text(controller.email),
      // trailing: const Icon(Icons.chevron_right),
      // onTap: () {},
    );
    yield const Divider(height: 1);
    yield Obx(() {
      return ListTile(
        leading: const Text('生日'),
        title: Text(controller.draft.birthdayAsString),
        onTap: () async {
          try {
            final res = await showDatePicker(
              context: Get.context!,
              initialDate: DateTime.now(),
              firstDate: DateTime(1900),
              lastDate: DateTime.now(),
            );
            if (res != null) {
              controller.draft.birthday = res.toDateString();
              controller.refreshDraft();
            }
          } catch (e) {
            controller.logger.e(e);
          }
        },
      );
    });
    yield const Divider(height: 1);
    yield Obx(() {
      return SwitchListTile.adaptive(
        activeColor: EfColors.primary,
        title: const Text('衣芙優惠電子報'),
        value: controller.draft.isSubscribeEpaper,
        onChanged: (value) {
          controller.draft.isSubscribeEpaper = value;
          controller.refreshDraft();
        },
      );
    });
    yield const Divider(height: 1);
    yield ListTile(
      leading: const Text('變更密碼'),
      // subtitle: const Text('請輸入密碼'),
      trailing: const Icon(Icons.chevron_right),
      onTap: () {
        Get.toNamed(Routes.PASSWORD_EDITOR);
      },
    );
  }
}
