import 'package:efshop/app/components/background.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/splash_controller.dart';

class SplashView extends GetView<SplashController> {
  const SplashView({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(
      //   title: const Text('SplashView'),
      //   centerTitle: true,
      // ),
      body: SafeArea(
        child: Background(
          background: Container(
            alignment: Alignment.bottomCenter,
            padding: EdgeInsets.symmetric(
              vertical: 14.dh,
              horizontal: 20.dw,
            ),
            child: _background(),
          ),
          child: SvgPicture.asset(
            'assets/images/splash_logo.svg',
            fit: BoxFit.contain,
            width: 220.dw,
            height: 86.dh,
          ),
          // child: Image.asset(
          //   'assets/images/splash_logo.png',
          //   fit: BoxFit.contain,
          //   width: 220.dw,
          //   height: 86.dh,
          // ),
        ),
      ),
    );
  }

  Widget _background() {
    return Row(
      children: [
        const Spacer(),
        Text(
          Constants.officialWebsite,
          style: TextStyle(
            fontSize: 10.dsp,
            color: EfColors.grayTextLight,
          ),
          softWrap: false,
        ),
        Expanded(
          child: Text(
            '正 v${controller.version}',
            style: TextStyle(
              fontSize: 12.dsp,
              color: EfColors.grayTextLight,
            ),
            textAlign: TextAlign.right,
            softWrap: false,
          ),
        ),
      ],
    );
  }
}
