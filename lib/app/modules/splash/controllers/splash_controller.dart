import 'package:efshop/app/models/jwt_res.dart';
import 'package:efshop/app/models/login_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:talker_flutter/talker_flutter.dart';

class SplashController extends GetxController {
  final WabowProvider wabowProvider;
  Logger get logger => wabowProvider.logger;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Talker get talker => wabowProvider.talker;
  String get version => prefProvider.packageInfo.version;

  SplashController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh().then((value) => _forceThrowException());
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    await 400.milliseconds.delay();
    final jwt = LoginRes(token: prefProvider.token).jwtRes ?? JwtRes();
    final userid = jwt.userid ?? '';
    logger.i('userid: $userid');
    boxProvider.domain = prefProvider.host;
    for (var box in Boxes.values) {
      await boxProvider.initGsBox(box.name);
    }
    // 把 Map<String, String?> 转换为 Map<String, String>
    final parameters = <String, String>{};
    for (var entry in Get.parameters.entries) {
      parameters[entry.key] = entry.value ?? '';
    }
    Get.offNamed(Routes.INDEX, parameters: parameters);
  }

  void _forceThrowException() {
    // logger.d('logger: force throw exception');
    // talker.debug('talker: force throw exception');
    // throw Exception('test crash');
  }
}
