import 'package:efshop/app/models/members_messages_preference_res.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/enums.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:webview_cookie_manager/webview_cookie_manager.dart';

class SettingsController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  Logger get logger => prefProvider.logger;
  String get displayVersion => prefProvider.packageInfo.version;
  final _isAllowNotification = SwitchStatus.off.obs;
  SwitchStatus get isAllowNotification => _isAllowNotification.value;

  SettingsController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final profile = prefProvider.profile;
      final switchOn = profile.isAllowNotification == SwitchStatus.on.index;
      _isAllowNotification.value =
          switchOn ? SwitchStatus.on : SwitchStatus.off;
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  ///
  /// 登出，無視錯誤清除所有資料
  ///
  Future<void> signOut() async {
    try {
      await wabowProvider.logout();
    } catch (e) {
      logger.e(e);
      // return Future.error(e);
    }
  }

  ///
  /// facebook 登出
  ///
  Future<void> facebookLogout() async {
    try {
      await FacebookAuth.instance.logOut();
    } catch (e) {
      logger.e(e);
    }
  }

  Future<void> removeCookie() async {
    try {
      prefProvider.cookies = [];
      // final currentUrl = Constants.uriWebChat.origin;
      // await WebviewCookieManager().removeCookie(currentUrl);
      await WebviewCookieManager().clearCookies();
    } catch (e) {
      logger.e(e);
    }
  }

  Future<MembersMessagesPreferenceRes> updateNotification(bool value) async {
    final next = value == true ? SwitchStatus.on : SwitchStatus.off;
    final res = await wabowProvider.postMembersMessagesPreference(next);
    if (res.status == true) {
      _isAllowNotification.value = next;
      final profile = prefProvider.profile;
      profile.isAllowNotification = next.index;
      prefProvider.profile = profile;
    }
    return res;
  }
}
