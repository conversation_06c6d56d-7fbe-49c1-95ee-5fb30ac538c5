import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/not_found_controller.dart';

class NotFoundView extends GetView<NotFoundController> {
  const NotFoundView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NotFoundController>(
      init: Get.find<NotFoundController>(),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('查無頁面'),
            centerTitle: true,
          ),
          body: controller.obx(
            (state) => Obx(() => _empty()),
            onEmpty: Obx(() => _empty()),
            onError: (e) => _empty(),
          ),
        );
      },
    );
  }

  Widget _empty() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(height: 20);
      yield ListTile(
        tileColor: Colors.transparent,
        title: Text(
          '《查無結果，推薦你》',
          style: TextStyle(
            fontSize: 19,
            color: EfColors.grayAA,
          ),
        ),
      );
      yield ListTile(
        contentPadding: EdgeInsets.only(left: 36.dw, top: 0, bottom: 0),
        horizontalTitleGap: 24,
        visualDensity: VisualDensity.compact,
        tileColor: Colors.transparent,
        leading: SvgPicture.asset(
          'assets/images/search_001.svg',
          width: 20,
          height: 20,
          colorFilter: ColorFilter.mode(EfColors.grayAA, BlendMode.srcIn),
          fit: BoxFit.contain,
        ),
        title: Text(
          '熱門搜尋',
          style: TextStyle(
            fontSize: 19,
            color: EfColors.grayAA,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
      for (var element in controller.keywords) {
        final text = element.name ?? '';
        if (text.isEmpty) {
          continue;
        }
        yield ListTile(
          contentPadding: EdgeInsets.only(left: 36.dw, top: 0, bottom: 0),
          dense: true,
          horizontalTitleGap: 24,
          visualDensity: VisualDensity.compact,
          minVerticalPadding: 0,
          tileColor: Colors.transparent,
          leading: SizedBox(),
          onTap: () {
            final url = element.url;
            if (url != null) {
              Get.launchUrl(url.uri);
            }
          },
          title: Text(
            text,
            style: const TextStyle(
              fontSize: 17,
              color: EfColors.gray93,
            ),
          ),
        );
      }
    }

    return ListView(
      children: children().toList(growable: false),
    );
  }
}
