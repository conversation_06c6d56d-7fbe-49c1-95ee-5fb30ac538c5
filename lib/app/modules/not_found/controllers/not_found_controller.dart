import 'dart:async';

import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:get/get.dart';

class NotFoundController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final WabowProvider wabowProvider;
  final _query = ''.obs;
  String get query => _query.value;
  set query(String value) {
    if (_query.value != value) {
      _query.value = value;
      change('', status: RxStatus.loading());
      onRefresh();
    }
  }

  final keywords = <Category>[].obs;

  NotFoundController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final res = await wabowProvider.getHotKeyword();
      keywords.assignAll(res);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
