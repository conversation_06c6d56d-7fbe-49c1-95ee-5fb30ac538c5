import 'package:efshop/app/components/ef_center.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/components/order_item.dart';
import 'package:efshop/app/models/members_orders_res.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/orders_controller.dart';

class OrdersView extends GetView<OrdersController> {
  const OrdersView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('訂單查詢'),
        centerTitle: true,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: <PERSON><PERSON><PERSON><PERSON>(
            height: 48,
            child: Obx(() => _tabBar()),
          ),
        ),
      ),
      body: controller.obx(
        // (state) => Obx(() => _body()),
        (state) => Obx(() => _body1()),
        onEmpty: _empty(),
        onError: (error) {
          return ErrorButton(
            error,
            onTap: controller.onRefresh,
          );
        },
      ),
    );
  }

  Widget _empty() {
    Iterable<Widget> children() sync* {
      yield SvgPicture.asset(
        'assets/images/empty_orders.svg',
        width: 48.dw,
        height: 44.dh,
        fit: BoxFit.contain,
      );
      yield const SizedBox(height: 20);
      yield const Text(
        '還沒有相關的訂單',
        style: TextStyle(
          fontSize: 14,
          color: EfColors.gray,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      );
      yield const SizedBox(height: 20);
    }

    return EfCenter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _tabBar() {
    return Align(
      alignment: Alignment.centerLeft,
      child: TabBar(
        isScrollable: true,
        controller: controller.tabController,
        tabs: _tabs().toList(growable: false),
        padding: EdgeInsets.zero,
        labelPadding: EdgeInsets.zero,
        onTap: (value) {
          // 检查PageController是否已经附加到PageView
          if (controller.pageController.hasClients &&
              controller.pageController.positions.isNotEmpty) {
            controller.pageController.jumpToPage(value);
          } else {
            // 如果PageController还未准备好，延迟执行
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (controller.pageController.hasClients &&
                  controller.pageController.positions.isNotEmpty) {
                controller.pageController.jumpToPage(value);
              }
            });
          }
        },
      ),
    );
  }

  // TODO: use iterable map
  Iterable<Widget> _tabs() sync* {
    for (var entry in controller.tabs.entries) {
      yield Tab(
        iconMargin: const EdgeInsets.only(
          bottom: 2,
        ),
        icon: Text(
          entry.key.display,
          style: const TextStyle(
            fontSize: 13,
            // color: EfColors.gray,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
          softWrap: false,
          overflow: TextOverflow.visible,
        ),
        child: Text(
          entry.value < 0 ? '-' : entry.value.decimalStyle,
          style: const TextStyle(
            fontSize: 13,
            // color: const Color(0xff757575),
          ),
          softWrap: false,
          textAlign: TextAlign.center,
          overflow: TextOverflow.visible,
        ),
      ).sizedBox(width: 78.dw);
    }
  }

  Widget _body1() {
    return PageView.builder(
      controller: controller.newPageController,
      itemCount: controller.tabs.length,
      onPageChanged: (value) {
        controller.logger.i('onPageChanged: $value');
        controller.tabController.animateTo(value);
      },
      itemBuilder: (BuildContext context, int index) {
        controller.logger.i('itemBuilder: $index');
        final tab = controller.tabs.keys.elementAt(index);
        final orders = controller.getOrdersWithFilter((e) {
          if (tab == OrderStatus.all) {
            return true;
          } else {
            return tab == e.orderStatus;
          }
        });
        if (orders.isEmpty) {
          return _empty();
        }
        return _listView(orders);
      },
    );
  }

  Widget _listView(Iterable<MembersOrdersRes> it) {
    return ListView.separated(
      itemCount: it.length,
      itemBuilder: (context, index) {
        final order = it.elementAt(index);
        return OrderItem(
          order,
          onRefresh: () => controller.onRefresh(),
          onTapCancel: () => _onCancelTapped(order),
          onTapReorder: () => _onTapReorder(order),
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 14);
      },
    );
  }

  Widget _body() => _listView(controller.data);

  Future<void> _onTapReorder(MembersOrdersRes order) async {
    Get.showLoading();
    try {
      // 再買一次
      await controller.wabowProvider.postCartBuyAgain(order.id ?? '');
      Get.back();
      // 回到購物車
      await Get.offAllNamed(
        Routes.LOADING,
        parameters: <String, String>{Keys.id: '${IndexTab.cart.index}'},
      );
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
  }

  Future<void> _onCancelTapped(MembersOrdersRes order) async {
    final res = await Get.showConfirm(
      '即將取消訂單',
      textConfirm: '取消訂單',
      textCancel: '不取消',
    );
    if (res == Button.confirm) {
      Get.showLoading();
      try {
        await controller.wabowProvider.postMembersOrdersCancel(order.id ?? '');
        await controller.onRefresh();
        Get.back();
        Get.showAlert('取消訂單成功');
      } catch (e) {
        Get.back();
        Get.showAlert(e.toString());
      }
    }
  }
}
