import 'dart:async';

import 'package:appier_flutter/appier_flutter.dart';
import 'package:efshop/app/models/appier_notification.dart';
import 'package:efshop/app/models/members_messages_activity.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/message_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

class ActivityController extends GetxController with StateMixin<String> {
  final MessageProvider messageProvider;
  WabowProvider get wabowProvider => messageProvider.wabowProvider;
  Logger get logger => wabowProvider.logger;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  final data = <MembersMessagesActivity>[].obs;
  final _disposable = Completer();

  ActivityController({required this.messageProvider});

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      messageProvider.markAsRead(MessageType.activities);
      final future = prefProvider.isLogin
          ? wabowProvider.getMembersMessagesActivities
          : wabowProvider.getMembersMessagesActivitiesNologin;
      final it = await future();
      // _saveToLocalStorage(it);
      // final it = _loadFromStorage();
      data.assignAll(it);
      // await _fetchAppierNotifications();
      change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<void> _fetchAppierNotifications() async {
    try {
      final ls = await AppierFlutter.getStoredNotifications();
      final it = ls.map(
          (e) => AppierNotification.fromJson(e).toMembersMessagesActivity());
      data.addAll(it);
    } catch (e) {
      logger.e(e);
    }
  }

  // Iterable<MembersMessagesActivity> _loadFromStorage() sync* {
  //   final box = boxProvider.getGsBox(Boxes.activity.name);
  //   for (dynamic json in box.getValues()) {
  //     yield MembersMessagesActivity.fromJson(json);
  //   }
  // }

  // void _saveToLocalStorage(Iterable<MembersMessagesActivity> it) {
  //   final box = boxProvider.getGsBox(Boxes.activity.name);
  //   box.erase();
  //   for (final e in it) {
  //     box.write(e.title ?? '', e.toJson());
  //   }
  // }
}
