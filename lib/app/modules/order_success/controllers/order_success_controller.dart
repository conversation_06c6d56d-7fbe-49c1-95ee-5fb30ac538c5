import 'dart:async';

import 'package:efshop/app/models/appier_checkout_completed_req.dart';
import 'package:efshop/app/models/appier_payment_info_added_req.dart';
import 'package:efshop/app/models/appier_product_purchased_req.dart';
import 'package:efshop/app/models/members_orders_res.dart';
import 'package:efshop/app/models/product_detail.dart';
import 'package:efshop/app/providers/appier_provider.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

class OrderSuccessController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  Logger get logger => wabowProvider.logger;
  AppierProvider get appierProvider => wabowProvider.appierProvider;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  final _disposable = Completer();
  final _orderId = ''.obs;
  String get orderId => _orderId.value;
  set orderId(String value) => _orderId.value = value;
  final _order = MembersOrdersRes().obs;
  MembersOrdersRes get order => _order.value;
  final _leading = false.obs;
  bool get leading => _leading.value;

  OrderSuccessController({
    required this.wabowProvider,
    String orderId = '',
  }) {
    if (orderId.isNotEmpty) {
      this.orderId = orderId;
    }
  }

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.orderId)) {
      orderId = Get.parameters[Keys.orderId] ?? '';
    }
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    logger.d('[OrderSuccessController] onRefresh: orderId($orderId)');
    try {
      _order.value = await wabowProvider.getMembersOrdersWithId(orderId);
      // 付款資訊
      _paymentInfoAdded(order);
      // 完成產品購買
      _productPurchased(order);
      // 完成結帳
      _checkoutCompleted(order);
      // log purchase
      _logPurchase(order);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
      _leading.value = true;
    }
  }

  ///
  /// 付款資訊
  ///
  Future<void> _paymentInfoAdded(MembersOrdersRes order) async {
    try {
      // appier request
      final req = AppierPaymentInfoAddedReq(
        paymentMethod: order.paymentName,
        store: order.storeName,
        addressReceive: order.displayAddress,
      );
      // 使用點數
      final points = order.getDiscountProducts([DiscountProduct.point.value]);
      if (points.isNotEmpty) {
        final pointUsed = points.fold<num>(
          0,
          (previousValue, element) => previousValue + (element.subtotal ?? 0),
        );
        req.pointsUsed = pointUsed.abs();
      }
      await appierProvider.paymentInfoAdded(req, order.total ?? 0);
    } catch (e) {
      logger.e(e);
    }
  }

  ///
  /// 完成產品購買
  ///
  Future<void> _productPurchased(MembersOrdersRes order) async {
    final box = boxProvider.getGsBox(Boxes.productDetail.name);
    for (var product in order.normalProducts) {
      try {
        final json = box.read(product.productId ?? '');
        final productDetail = ProductDetail.fromJson(json);
        await appierProvider.productPurchased(AppierProductPurchasedReq(
          categoryName: productDetail.topCategoryName,
          typeName: productDetail.categoryName,
          productId: product.productId,
          productName: product.productName,
          productImageUrl: product.thumbnail?.src,
          productUrl: product.shareUrlWithScheme,
          productPrice: product.subtotal,
        ));
      } catch (e) {
        logger.e(e);
      }
    }
  }

  ///
  /// GA: log purchase
  ///
  Future<void> _logPurchase(MembersOrdersRes order) async {
    try {
      await FirebaseAnalytics.instance.logPurchase(
        currency: 'TWD',
        // coupon: order.couponCode,
        value: order.subtotal?.toDouble(),
        // items: children().toList(growable: false),
        items: order.normalProducts
            .map((e) => e.toAnalyticsEventItem())
            .toList(growable: false),
        // tax: order.tax?.toDouble(),
        shipping: order.shippingFee?.toDouble(),
        transactionId: order.number,
        affiliation: order.affiliateOrderType,
        // parameters:
        // callOptions: // web only
      );
    } catch (e) {
      logger.e(e);
    }
  }

  ///
  /// 完成結帳
  ///
  Future<void> _checkoutCompleted(MembersOrdersRes order) async {
    try {
      await appierProvider.checkoutCompleted(AppierCheckoutCompletedReq(
        orderId: order.id,
        numberOfProducts: order.quantity,
        orderAmount: order.total,
      ));
    } catch (e) {
      logger.e(e);
    }
  }
}
