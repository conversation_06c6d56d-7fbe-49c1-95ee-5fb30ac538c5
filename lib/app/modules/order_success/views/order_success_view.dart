import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/order_success_controller.dart';

class OrderSuccessView extends GetView<OrderSuccessController> {
  final String? tag;
  final AsyncValueSetter<IndexTab>? changeTab;

  OrderSuccessView({
    super.key,
    String orderId = '',
    this.changeTab,
  }) : tag = orderId {
    Get.lazyPut<OrderSuccessController>(
      () => OrderSuccessController(
        wabowProvider: Get.find(),
        orderId: orderId,
      ),
      tag: tag,
      fenix: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderSuccessController>(
      init: Get.find<OrderSuccessController>(tag: tag),
      tag: tag,
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('訂購成功'),
            centerTitle: true,
            leading: Obx(() {
              return Visibility(
                visible: controller.leading,
                child: BackButton(
                  onPressed: () {
                    changeTab?.call(IndexTab.category);
                  },
                ),
              );
            }),
          ),
          body: controller.obx(
            (state) {
              return _body();
            },
            onError: (error) {
              return ErrorButton(
                error,
                onTap: controller.onRefresh,
              );
            },
          ),
        );
      },
    );
  }

  Widget _body() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield _status();
    yield SizedBox(height: 10.dh);
    yield _info();
    yield SizedBox(height: 8.dh);
    yield _continueButton();
    yield SizedBox(height: 1.dh);
    yield _orderDetailButton();
  }

  Widget _continueButton() {
    return SizedBox(
      width: double.infinity,
      height: 46.dh,
      child: TextButton.icon(
        style: TextButton.styleFrom(
          backgroundColor: Colors.white,
          // fixedSize: Size.fromHeight(46.dh),
        ),
        onPressed: () {
          // 跳轉到分類
          changeTab?.call(IndexTab.category);
        },
        icon: SvgPicture.asset('assets/images/icon_cart.svg'),
        label: const Text(
          '繼續購物',
          style: TextStyle(
            fontSize: 15,
            color: EfColors.gray6B,
          ),
          softWrap: false,
        ),
      ),
    );
  }

  Widget _orderDetailButton() {
    return SizedBox(
      width: double.infinity,
      height: 46.dh,
      child: TextButton.icon(
        style: TextButton.styleFrom(
          backgroundColor: Colors.white,
          // fixedSize: Size.fromHeight(48.dh),
        ),
        onPressed: () {
          // 跳轉到會員頁面
          changeTab?.call(IndexTab.profile);
          // 跳轉到訂單查詢 - 待出貨
          Get.toNamed(Routes.ORDERS, parameters: {
            Keys.id: '${OrderStatus.padding.index}',
          });
          // 跳轉到訂單詳情
          // 會造成鬼打牆
          // Get.toNamed(Routes.ORDER_DETAIL, parameters: {
          //   Keys.id: controller.orderId,
          // });
        },
        icon: SizedBox.square(
          dimension: 20.dw,
          child: SvgPicture.asset('assets/images/icon_order_check.svg'),
        ),
        label: const Text(
          '訂單查詢',
          style: TextStyle(
            fontSize: 15,
            color: EfColors.gray6B,
          ),
          softWrap: false,
        ),
      ),
    );
  }

  Widget _status() {
    Iterable<Widget> children() sync* {
      yield SizedBox(width: double.infinity, height: 28.dh);
      yield const Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.check_circle,
            color: EfColors.primary,
            size: 22,
          ),
          SizedBox(width: 8),
          Text(
            '訂購成功',
            style: TextStyle(
              fontSize: 13,
              color: EfColors.grayTextLight,
            ),
            textAlign: TextAlign.center,
            softWrap: false,
          )
        ],
      );
      yield SizedBox(height: 16.dh);
      yield Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            '訂單編號：',
            style: TextStyle(
              fontSize: 13,
              color: EfColors.grayTextLight,
              fontWeight: FontWeight.w500,
            ),
            softWrap: false,
          ),
          // 訂單編號
          Text(
            // '69944071',
            controller.order.number ?? '',
            style: const TextStyle(
              fontSize: 13,
              color: EfColors.primary,
              fontWeight: FontWeight.w500,
            ),
            softWrap: false,
          ),
        ],
      );
      yield SizedBox(height: 28.dh);
    }

    return ColoredBox(
      color: Colors.white,
      child: Column(
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _info() {
    Iterable<Widget> children() sync* {
      final order = controller.order;
      yield SizedBox(height: 24.dh);
      yield Text(
        // '收件人：王*明',
        '收件人：${order.receiverName ?? ''}',
        style: const TextStyle(
          fontSize: 12,
          color: EfColors.grayTextDark,
        ),
      );
      yield SizedBox(height: 12.dh);
      yield Text(
        // '付款方式：全家超商',
        '付款方式：${order.paymentName ?? ''}',
        style: const TextStyle(
          fontSize: 12,
          color: EfColors.grayTextDark,
        ),
      );
      yield SizedBox(height: 12.dh);
      yield Text(
        // '取貨門市：全家深坑昇高店',
        '${order.shippingName}：${order.displayAddress}',
        style: const TextStyle(
          fontSize: 12,
          color: EfColors.grayTextDark,
        ),
      );
      yield SizedBox(height: 12.dh);
      yield Text(
        // '發票類型：捐贈發票',
        '發票類型：${order.displayInvoiceTypeWithCarrier}',
        style: const TextStyle(
          fontSize: 12,
          color: EfColors.grayTextDark,
        ),
      );
      // yield Row(
      //   mainAxisSize: MainAxisSize.min,
      //   // mainAxisAlignment: MainAxisAlignment.center,
      //   children: [
      //     Text(
      //       // '發票類型：捐贈發票',
      //       '發票類型：${order.invoiceType.display}',
      //       style: const TextStyle(
      //         fontSize: 12,
      //         color: EfColors.grayTextDark,
      //       ),
      //     ),
      //     // TODO:
      //     // SizedBox(width: 4.dh),
      //     // const Text(
      //     //   '財團法人「創世福利基金會」',
      //     //   style: TextStyle(
      //     //     fontSize: 12,
      //     //     color: EfColors.gray93,
      //     //   ),
      //     //   softWrap: false,
      //     // ),
      //   ],
      // );
      yield SizedBox(height: 12.dh);
      yield Text(
        // '訂單備註：請幫我在下週到貨，我這週不在取貨點',
        '訂單備註：${order.comment ?? ''}',
        style: const TextStyle(
          fontSize: 12,
          color: EfColors.grayTextDark,
        ),
      );
      yield SizedBox(height: 24.dh);
    }

    return ColoredBox(
      color: Colors.white,
      child: SizedBox(
        width: double.infinity,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children().toList(growable: false),
          ),
        ),
      ),
    );
  }
}
