import 'package:get/get.dart';

class DeleteAccountController extends GetxController {
  final htmlString = '''
親愛的顧客 您好:
若您需要刪除會員帳號，
請務必詳細閱讀並了解以下事項：

1. 將無法再使用該帳號於平台登入。
2. 訂單紀錄將完全清除，並停止所有相關服務（含紅利點數）。
3. 會員載具發票將無法查詢、下載或寄出（含中獎發票、公司統編發票）。
4. 請務必確認【退款餘額】已結清。
5. 經提交刪除申請，將無法以任何理由要求恢復、轉移或合併帳號資訊。

提醒：一旦申請刪除會員帳號，需7-12個工作天驗證您的身分後，方能將您的會員帳號刪除。

若您確定刪除帳號，即表示同意放棄上述權益： 請點選下方【聯絡客服】按鈕，我們將協助您申請。
''';
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
