import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/delete_account_controller.dart';

class DeleteAccountView extends GetView<DeleteAccountController> {
  const DeleteAccountView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('刪除會員帳號'),
        centerTitle: true,
      ),
      body: Background(
        alignment: Alignment.bottomCenter,
        background: _body(),
        child: ColoredBox(
          color: Colors.white,
          child: Safe<PERSON>rea(
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.zero,
                ),
                minimumSize: const Size(double.infinity, 50),
              ),
              onPressed: () {
                Get.toNamed(Routes.EF_WEB, parameters: {
                  Keys.title: '聯絡客服',
                  Keys.url: Constants.uriRevoke.toString(),
                });
              },
              child: const Text(
                '聯絡客服',
                style: TextStyle(
                  fontSize: 15,
                  color: EfColors.gray6B,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _body() {
    return SizedBox(
      height: double.infinity,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        child: RichText(
          text: TextSpan(
            text: controller.htmlString,
            style: const TextStyle(
              fontSize: 15,
              color: EfColors.gray6B,
            ),
          ),
        ),
      ),
    );
  }
}
