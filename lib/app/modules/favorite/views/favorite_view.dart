import 'package:efshop/app/components/ef_center.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/components/favorite_item.dart';
import 'package:efshop/app/models/members_my_favorite_res.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/favorite_controller.dart';

class FavoriteView extends GetView<FavoriteController> {
  const FavoriteView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: FavoriteController(
        wabowProvider: Get.find(),
      ),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            actions: _actions().toList(growable: false),
            title: const Text('收藏清單'),
            centerTitle: true,
          ),
          body: controller.obx(
            (state) => Obx(() => _body()),
            // (state) => _empty(),
            onEmpty: _empty(),
            onError: (error) {
              return ErrorButton(
                error,
                onTap: controller.onRefresh,
              );
            },
          ),
        );
      },
    );
  }

  Future<void> _clearFavorite() async {
    final res = await Get.showConfirm('即將清空收藏', textConfirm: '清空');
    if (res == Button.confirm) {
      Get.showLoading();
      try {
        await controller.clearFavorite();
        Get.back();
      } catch (e) {
        Get.back();
        Get.showAlert(e.toString());
      }
    }
  }

  Iterable<Widget> _actions() sync* {
    yield Obx(() {
      return Visibility(
        visible: controller.data.isNotEmpty,
        child: TextButton(
          onPressed: _clearFavorite,
          child: const Text(
            '清空',
            style: TextStyle(
              fontSize: 15,
              color: EfColors.gray98,
            ),
            softWrap: false,
          ),
        ),
      );
    });
  }

  Widget _body() {
    return ListView.separated(
      itemBuilder: (context, index) {
        final data = controller.data.elementAt(index);
        return FavoriteItem(
          data.toFavoriteData(),
          isLogin: controller.prefProvider.isLogin,
          onPressed: () {
            Get.toNamed(Routes.PRODUCT, parameters: {
              Keys.id: data.id ?? '',
            });
          },
          onSizePressed: () {
            _showSizePicker(data);
          },
          onCartPressed: data.isAvailable
              ? () async {
                  Get.showLoading();
                  try {
                    if (data.isInStock) {
                      await controller.addToCart(data);
                      Get.back();
                      Get.showToast('已加入購物車');
                    } else {
                      await controller.addToPreorder(data);
                      Get.back();
                      Get.showToast('已加入貨到通知');
                    }
                  } catch (e) {
                    Get.back();
                    Get.showAlert(e.toString());
                  }
                }
              : null,
          onRemovePressed: () async {
            final res = await Get.showConfirm('即將移除收藏', textConfirm: '移除');
            if (res == Button.confirm) {
              Get.showLoading();
              try {
                await controller.removeFromFavorite(data);
                Get.back();
              } catch (e) {
                Get.back();
                Get.showAlert(e.toString());
              }
            }
          },
        );
      },
      separatorBuilder: (context, index) {
        return const Divider(height: 1);
      },
      itemCount: controller.data.length,
    );
  }

  Future<void> _showSizePicker(MembersMyFavorite data) async {
    // ["F", "XS", "S", "M", "L", "XL", "XXL", "3XL", "4XL", "5XL", "加大"]
    // 已登入的儲存內容是 parentId, 未登入的儲存內容是 number
    // final id = controller.prefProvider.isLogin ? data.parentId : data.number;
    final id = data.parentId;
    final it = await controller.getChildrenWithParentId(id ?? '');
    final list = it
        .where((element) => element.size != null && element.size!.isNotEmpty)
        .toList(growable: false);
    list.sort((a, b) {
      final aIndex = Constants.sizeOrder[a.size] ?? 0;
      final bIndex = Constants.sizeOrder[b.size] ?? 0;
      return aIndex.compareTo(bIndex);
    });
    final picker = list
        .map((e) => ListTile(
              title: Text(e.size ?? ''),
              onTap: () {
                Get.back(result: e);
              },
            ))
        .column()
        .sheet();
    try {
      final res = await picker;
      if (res != null) {
        final index =
            controller.data.indexWhere((element) => element.id == data.id);
        if (index >= 0 && index < controller.data.length) {
          controller.data[index] = res;
        }
      }
    } catch (e) {
      Get.showAlert(e.toString());
    }
  }

  Widget _empty() {
    Iterable<Widget> children() sync* {
      yield SvgPicture.asset(
        'assets/images/empty_favorite.svg',
        width: 40.dw,
        height: 36.dh,
        fit: BoxFit.contain,
      );
      yield const SizedBox(height: 18);
      yield const Text(
        '還沒有收藏',
        style: TextStyle(
          fontSize: 14,
          color: EfColors.gray,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      );
      yield ElevatedButton(
        onPressed: () {
          Get.back(result: IndexTab.category);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: EfColors.primary,
          shape: const RoundedRectangleBorder(
            borderRadius: Constants.buttonBorderRadius,
          ),
          minimumSize: const Size(80, 26),
        ),
        child: const Text(
          '繼續購物',
          style: TextStyle(
            fontSize: 13,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      );
      yield const SizedBox(height: 20);
    }

    return EfCenter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }
}
