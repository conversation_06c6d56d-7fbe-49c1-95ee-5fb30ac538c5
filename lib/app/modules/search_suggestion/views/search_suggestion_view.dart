import 'package:efshop/app/models/category.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/search_suggestion_controller.dart';

class SearchSuggestionView extends GetView<SearchSuggestionController> {
  final SearchSuggestionController? ctrl;
  final ValueSetter<Category>? onTap;

  SearchSuggestionView({
    super.key,
    this.onTap,
    this.ctrl,
  }) {
    Get.lazyPut(
      () =>
          ctrl ??
          SearchSuggestionController(
            wabowProvider: Get.find(),
          ),
      fenix: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SearchSuggestionController>(
      init: Get.find<SearchSuggestionController>(),
      // init: ctrl,
      // init: SearchSuggestionController(
      //   wabowProvider: Get.find(),
      // ),
      builder: (controller) {
        return Scaffold(
          // appBar: AppBar(
          //   title: const Text('SearchSuggestionView'),
          //   centerTitle: true,
          // ),
          body: controller.obx((state) => Obx(() => _body()),
              onEmpty: Obx(() => _onEmpty()), onError: (err) {
            return ErrorWidget(Exception());
          }),
        );
      },
    );
  }

  Widget _body() {
    final it = controller.suggestions;
    if (it.isEmpty) {
      return _onEmpty();
    }
    return ListView.separated(
      itemBuilder: (context, index) {
        final text = it.elementAt(index);
        return ListTile(
          onTap: () {
            onTap?.call(Category(name: text));
          },
          title: Text(text),
          titleTextStyle: const TextStyle(
            fontSize: 14,
            color: EfColors.gray47,
          ),
        );
      },
      separatorBuilder: (context, index) {
        return const Divider(
          height: 1,
        );
      },
      itemCount: it.length,
    );
  }

  Widget _onEmpty() {
    Iterable<Widget> children() sync* {
      if (controller.history.isNotEmpty) {
        yield ListTile(
          contentPadding: const EdgeInsets.only(
            left: 16,
          ),
          titleTextStyle: const TextStyle(
            fontSize: 13,
            color: EfColors.gray93,
          ),
          title: const Text(
            '歷史紀錄',
            softWrap: false,
          ),
          trailing: IconButton(
            onPressed: () async {
              final res = await Get.showConfirm(
                '即將刪除所有紀錄',
                textConfirm: '刪除',
              );
              if (res == Button.confirm) {
                Get.showLoading();
                try {
                  await controller.deleteAllHistory();
                  Get.back();
                } catch (e) {
                  Get.back();
                  // Get.showAlert(e.toString());
                }
              }
            },
            icon: const Icon(Icons.delete),
          ),
        );

        yield SizedBox(
          width: double.infinity,
          child: Wrap(
            spacing: 8,
            // runSpacing: 10,
            children: _history().toList(growable: false),
          ),
        ).paddingSymmetric(horizontal: 14);

        yield const SizedBox(height: 14);

        yield const Divider(
          height: 0,
          thickness: 10,
          color: EfColors.grayF6,
        );
      }
      yield const ListTile(
        contentPadding: EdgeInsets.only(
          left: 16,
        ),
        titleTextStyle: TextStyle(
          fontSize: 13,
          color: EfColors.gray93,
        ),
        title: Text(
          '熱門搜尋',
          softWrap: false,
        ),
      );

      yield SizedBox(
        width: double.infinity,
        child: Wrap(
          spacing: 8,
          // runSpacing: 10,
          children: _keywords().toList(growable: false),
        ),
      ).paddingSymmetric(horizontal: 14);
    }

    return ColoredBox(
      color: Colors.white,
      child: SizedBox.expand(
        child: SingleChildScrollView(
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: children().toList(growable: false),
            ),
          ),
        ),
      ),
    );
  }

  // TODO: use iterable map
  Iterable<Widget> _keywords() sync* {
    for (final category in controller.keywords) {
      final text = category.name ?? '';
      if (text.isEmpty) {
        continue;
      }
      yield TextButton(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: 8,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4.0),
          ),
          backgroundColor: EfColors.grayF4,
        ),
        onPressed: () {
          onTap?.call(category);
        },
        child: Text(
          // '褲襪',
          text,
          style: const TextStyle(
            fontSize: 13,
            color: EfColors.gray93,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
  }

  // TODO: use iterable map
  Iterable<Widget> _history() sync* {
    for (final text in controller.history) {
      if (text.isEmpty) {
        continue;
      }
      yield TextButton(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: 8,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4.0),
          ),
          backgroundColor: EfColors.grayF4,
        ),
        onPressed: () {
          onTap?.call(Category(name: text));
        },
        child: Text(
          // '褲襪',
          text,
          style: const TextStyle(
            fontSize: 13,
            color: EfColors.gray93,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
  }
}
