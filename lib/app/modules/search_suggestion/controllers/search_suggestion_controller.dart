import 'dart:async';

import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

class SearchSuggestionController extends GetxController
    with StateMixin<String> {
  final _disposable = Completer();
  final WabowProvider wabowProvider;
  Logger get logger => wabowProvider.logger;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  final history = <String>[].obs;
  final keywords = <Category>[].obs;
  final _suggestions = <String>[].obs;
  final _query = ''.obs;
  String get query => _query.value;
  set query(String value) {
    if (_query.value != value) {
      _query.value = value;
      if (query.isEmpty) {
        change('', status: RxStatus.loading());
        onRefresh();
      } else {
        change('', status: RxStatus.success());
      }
    }
  }

  Iterable<String> get suggestions =>
      _suggestions.where((element) => element.contains(query));

  SearchSuggestionController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // _query.stream
    //     .distinct()
    //     .debounce(const Duration(milliseconds: 500))
    //     .takeUntil(_disposable.future)
    //     .listen((event) {
    //   if (event.isEmpty) {
    //     change('', status: RxStatus.empty());
    //   } else {
    //     logger.i('search: $event');
    //     change('', status: RxStatus.success());
    //   }
    // });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<Iterable<String>> _getHistoryFromRemote() async {
    try {
      return await wabowProvider.getMembersSearchHistories();
    } catch (e) {
      return <String>[];
    }
  }

  Future<Iterable<String>> _getHistory() async {
    final box = boxProvider.getGsBox(Boxes.setting.name, withNamespace: false);
    final ls = List<String>.from(box.read(Keys.history) ?? []);
    ls.addAll(await _getHistoryFromRemote());
    final res = ls.toSet().toList(growable: false);
    box.write(Keys.history, res);
    return res;
  }

  Future<void> onRefresh() async {
    try {
      final res = await wabowProvider.getSuggestions();
      _suggestions.assignAll(res);
    } catch (e) {
      logger.e(e);
    }
    try {
      final res = await _getHistory();
      history.assignAll(res);
    } catch (e) {
      logger.e(e);
    }
    try {
      final res = await wabowProvider.getHotKeyword();
      keywords.assignAll(res);
      // change('', status: RxStatus.success());
      change('', status: RxStatus.empty());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<MessageRes> deleteAllHistory() {
    // 刪除本地歷史紀錄
    final box = boxProvider.getGsBox(Boxes.setting.name, withNamespace: false);
    box.remove(Keys.history);
    history.clear();
    return wabowProvider.deleteMembersSearchHistories();
  }
}
