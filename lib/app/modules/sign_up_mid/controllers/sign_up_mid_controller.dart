import 'package:efshop/app/models/login_res.dart';
import 'package:efshop/app/models/register_line_req.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';

class SignUpMidController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  // 註冊資料
  final _draft = RegisterLineReq().obs;
  RegisterLineReq get draft => _draft.value;
  // 確認密碼
  final _confirmPassword = ''.obs;
  String get confirmPassword => _confirmPassword.value;
  set confirmPassword(String value) => _confirmPassword.value = value;
  // 同意條款
  final _agree = false.obs;
  bool get agree => _agree.value;
  set agree(bool value) => _agree.value = value;
  // 標題
  final _title = ''.obs;
  String get title => _title.value;

  SignUpMidController({required this.wabowProvider});

  ///
  /// init
  ///
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.title)) {
      _title.value = Get.parameters[Keys.title] ?? '';
    }
    if (Get.parameters.containsKey(Keys.mid)) {
      draft.mid = Get.parameters[Keys.mid];
    }
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  void refreshDraft() {
    _draft.refresh();
  }

  bool isValidate() {
    try {
      draft.validate();
      draft.validatePassword(confirmPassword);
      if (agree == false) {
        throw '請勾選同意隱私權政策';
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<LoginRes> signUp() {
    return wabowProvider.registerLine(draft);
  }
}
