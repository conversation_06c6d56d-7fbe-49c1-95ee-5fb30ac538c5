import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/sign_up_mid_controller.dart';

class SignUpMidView extends GetView<SignUpMidController> {
  const SignUpMidView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        elevation: 0,
        title: Obx(
          () => Text(
            controller.title,
            style: TextStyle(
              fontSize: 16.dsp,
              color: EfColors.gray,
            ),
          ),
        ),
        centerTitle: true,
      ),
      body: controller.obx((state) {
        return _body();
      }),
    );
  }

  Widget _body() {
    return Padding(
      padding: EdgeInsets.symmetric(
        // vertical: 14.dh,
        horizontal: 24.dw,
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: _children().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    // 姓名
    yield SizedBox(height: 16.dh);
    yield Background(
      background: Align(
        alignment: Alignment.centerRight,
        child: Obx(() {
          return Visibility(
            visible: controller.draft.fullname?.isEmpty ?? true,
            child: Text(
              '姓名',
              style: TextStyle(
                fontSize: Constants.buttonFontSize.dsp,
                color: EfColors.grayTextLight,
              ),
              textAlign: TextAlign.right,
            ).paddingOnly(right: 16.dw),
          );
        }),
      ),
      child: TextFormField(
        onChanged: (value) {
          controller.draft.fullname = value;
          controller.refreshDraft();
        },
        // decoration: const InputDecoration(
        //   labelText: '密碼',
        //   hintText: '姓名',
        //   suffixIcon: IconButton(
        //     onPressed: () {},
        //     icon: SvgPicture.asset(
        //       'assets/images/eye.svg',
        //       width: 20.dw,
        //       height: 20.dh,
        //     ),
        //   ),
        // ),
      ).sizedBox(height: Constants.buttonHeight.dh),
    );
    // 電子信箱
    yield SizedBox(height: 16.dh);
    yield Background(
      background: Align(
        alignment: Alignment.centerRight,
        child: Obx(() {
          return Visibility(
            visible: controller.draft.email?.isEmpty ?? true,
            child: Text(
              'E-mail',
              style: TextStyle(
                fontSize: Constants.buttonFontSize.dsp,
                color: EfColors.grayTextLight,
              ),
            ).paddingOnly(right: 16.dw),
          );
        }),
      ),
      child: TextFormField(
        keyboardType: TextInputType.emailAddress,
        initialValue: controller.draft.email,
        onChanged: (value) {
          controller.draft.email = value;
          controller.refreshDraft();
        },
      ).sizedBox(height: Constants.buttonHeight.dh),
    );
    // 密碼
    yield SizedBox(height: 16.dh);
    yield Background(
      background: Align(
        alignment: Alignment.centerRight,
        child: Obx(() {
          return Visibility(
            visible: controller.draft.password?.isEmpty ?? true,
            child: Text(
              '密碼',
              style: TextStyle(
                fontSize: Constants.buttonFontSize.dsp,
                color: EfColors.grayTextLight,
              ),
              textAlign: TextAlign.right,
            ).paddingOnly(right: 16.dw),
          );
        }),
      ),
      child: TextFormField(
        obscureText: true,
        onChanged: (value) {
          controller.draft.password = value;
          controller.refreshDraft();
        },
        // decoration: const InputDecoration(
        //   labelText: '密碼',
        //   hintText: '密碼',
        //   suffixIcon: IconButton(
        //     onPressed: () {},
        //     icon: SvgPicture.asset(
        //       'assets/images/eye.svg',
        //       width: 20.dw,
        //       height: 20.dh,
        //     ),
        //   ),
        // ),
      ).sizedBox(height: Constants.buttonHeight.dh),
    );
    // 再次輸入密碼
    yield SizedBox(height: 16.dh);
    yield Background(
      background: Align(
        alignment: Alignment.centerRight,
        child: Obx(() {
          return Visibility(
            visible: controller.confirmPassword?.isEmpty ?? true,
            child: Text(
              '再次輸入密碼',
              style: TextStyle(
                fontSize: Constants.buttonFontSize.dsp,
                color: EfColors.grayTextLight,
              ),
              textAlign: TextAlign.right,
            ).paddingOnly(right: 16.dw),
          );
        }),
      ),
      child: TextFormField(
        obscureText: true,
        onChanged: (value) {
          controller.confirmPassword = value;
          controller.refreshDraft();
        },
        // decoration: const InputDecoration(
        //   labelText: '密碼',
        //   hintText: '再次輸入密碼',
        //   suffixIcon: IconButton(
        //     onPressed: () {},
        //     icon: SvgPicture.asset(
        //       'assets/images/eye.svg',
        //       width: 20.dw,
        //       height: 20.dh,
        //     ),
        //   ),
        // ),
      ).sizedBox(height: Constants.buttonHeight.dh),
    );
    // 會員條款
    yield Row(
      children: [
        Transform.translate(
          offset: Offset(-12.dw, 0),
          child: Obx(() {
            return Checkbox(
              value: controller.agree,
              onChanged: (value) => controller.agree = value ?? false,
            );
          }),
        ),
        TextButton(
          onPressed: () {
            // 前往會員條款
            Get.toNamed(Routes.EF_WEB, parameters: {
              Keys.url: Constants.uriPrivacy.toString(),
              Keys.title: '會員條款',
            });
          },
          child: const Text(
            '已閱讀並同意衣芙會員服務條款',
            style: TextStyle(
              fontSize: 14,
              color: EfColors.gray,
            ),
            softWrap: false,
          ),
        ),
      ],
    );
    yield SizedBox(height: 12.dh);
    yield Obx(() {
      return ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.zero,
          backgroundColor: EfColors.primary,
          disabledBackgroundColor: EfColors.grayD5,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(4.0),
            ),
          ),
        ),
        onPressed: controller.isValidate() ? _onNextPressed : null,
        child: Text(
          '送出',
          style: TextStyle(
            fontSize: Constants.buttonFontSize.dsp,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ).sizedBox(width: double.infinity, height: Constants.buttonHeight.dh);
    });
  }

  Future<void> _onNextPressed() async {
    Get.showLoading();
    try {
      // 本站註冊使用者 (綁定 Line mid)
      final res = await controller.signUp();
      // 成功，關閉 Loading
      Get.back();
      // 關閉所頁面，返回參數
      Get.back(
        result: res,
        id: SubRouteType.signIn.index,
      );
    } catch (e) {
      Get.back();
      // Get.showAlert(e.toString());
      const errorMessage = '密碼未符合規則\n[1]至少6位數\n[2]必須同時包含英數字\n[3]不得出現在帳號之中';
      Get.showAlert(errorMessage);
    }
  }
}
