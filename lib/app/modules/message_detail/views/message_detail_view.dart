import 'package:efshop/ef_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/message_detail_controller.dart';

class MessageDetailView extends GetView<MessageDetailController> {
  const MessageDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('客服訊息'),
        centerTitle: true,
      ),
      body: _body(),
    );
  }

  Widget _body() {
    return SingleChildScrollView(
      // padding: const EdgeInsets.only(
      //   bottom: 8,
      // ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield ListTile(
      horizontalTitleGap: 0,
      leading: Container(
        width: 26,
        height: 26,
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          color: EfColors.primary,
          shape: BoxShape.circle,
        ),
        child: SvgPicture.asset(
          'assets/images/q.svg',
          width: 14,
          height: 16,
          fit: BoxFit.contain,
        ),
      ),
      title: const Text(
        '下單後多久可收到',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray6B,
        ),
        softWrap: false,
      ),
    );
    yield const Divider(height: 1);
    yield Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      padding: const EdgeInsets.all(16),
      child: const Text(
        '超商取貨：\n訂購日+ 3 - 4 個工作日配達指定門市\n\n信用卡付款、貨到付款：\n訂購日 + 2 個工作日，以黑貓宅急便配送\n（如為外島訂單，將會以郵局”批次“寄送，預計5-7日）',
        style: TextStyle(
          fontSize: 13,
          color: EfColors.gray6B,
          height: 1.5,
        ),
        textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      ),
    );
  }
}
