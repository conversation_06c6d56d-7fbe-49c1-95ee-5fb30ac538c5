import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:get/get.dart';

class MessageDetailController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;

  MessageDetailController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
