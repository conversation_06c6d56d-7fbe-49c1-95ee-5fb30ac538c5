import 'package:efshop/app/components/action_button.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/models/menu_res.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../controllers/ef_menu_controller.dart';

class EfMenuView extends GetView<EfMenuController> {
  EfMenuView({super.key}) {
    Get.lazyPut(
      () => EfMenuController(
        wabowProvider: Get.find(),
      ),
      fenix: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EfMenuController>(
      init: Get.find<EfMenuController>(),
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            elevation: 0,
            automaticallyImplyLeading: false,
            // title: const Text('EfMenuView'),
            centerTitle: true,
            actions: _action().toList(growable: false),
          ),
          body: _body(),
        );
      },
    );
  }

  Iterable<Widget> _action() sync* {
    yield IconButton(
      onPressed: () {
        Get.back();
      },
      icon: SvgPicture.asset(
        'assets/images/close.svg',
        width: 30.dw,
        height: 30.dh,
      ),
    );
  }

  Widget _body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield ColoredBox(
      color: Colors.white,
      child: Row(
        children: _header().toList(growable: false),
      ),
    );
    yield const Divider(thickness: 1, height: 1);
    yield Expanded(
        child: controller.obx(
      (state) {
        return Obx(() => _menus());
      },
      onError: (error) => ErrorButton(
        error,
        onTap: () {
          controller.onRefresh();
        },
      ),
    ));
    yield _footer();
  }

  Widget _footer() {
    return Obx(() {
      return SizedBox(
        width: double.infinity,
        height: controller.scrollHeight,
        child: WebViewWidget(
          controller: controller.webViewController,
        ),
      );
    });
  }

  Widget _menus() {
    final it = controller.menus;
    return ListView.separated(
      itemCount: it.length,
      itemBuilder: (context, index) {
        final element = it.elementAt(index);
        return ListTile(
          tileColor: element.tileColor,
          contentPadding: EdgeInsets.symmetric(horizontal: 20.dw, vertical: 0),
          title: Text(
            // '關於衣芙',
            element.name ?? '',
            style: element.style,
            softWrap: false,
          ),
          trailing: element.trailing,
          onTap: () async {
            var authorized = true;
            // final needAuthorization = Routes.FAVORITE == '/${data.path}';
            // if (needAuthorization) {
            //   try {
            //     final res = await getLoginRes();
            //     if (res == null || res.isExpired == true) {
            //       authorized = false;
            //     }
            //   } catch (e) {
            //     controller.logger.e(e);
            //     authorized = false;
            //   }
            // }
            if (authorized) {
              element.onTap();
              controller.refreshMenus();
            }
          },
        );
      },
      separatorBuilder: (context, index) {
        return const Divider(thickness: 1, height: 1);
      },
    );
  }

  Iterable<Widget> _header() sync* {
    yield Obx(() {
      return ActionButton.menu(
        badgeOffset: const Offset(-6, 6),
        onPressed: () {
          Get.back();
          Get.toNamed(Routes.MESSAGE);
        },
        icon: 'assets/images/menu_message.svg',
        text: '會員訊息',
        badgeCount: controller.prefProvider.unreadRes.allUnreadCount,
      ).expanded();
    });
    yield ActionButton.menu(
      badgeOffset: const Offset(-6, 6),
      onPressed: () => Get.back(result: IndexTab.category),
      icon: 'assets/images/menu_category.svg',
      text: '商品分類',
    ).expanded();
    yield ActionButton.menu(
      badgeOffset: const Offset(-6, 6),
      onPressed: () => Get.back(result: IndexTab.service),
      icon: 'assets/images/menu_service.svg',
      text: '線上客服',
    ).expanded();
    yield Expanded(
      child: Obx(() {
        return ActionButton.menu(
          badgeOffset: const Offset(-6, 6),
          onPressed: () => Get.back(result: IndexTab.profile),
          icon: 'assets/images/menu_profile.svg',
          text: controller.isLogin ? '會員中心' : '登入註冊',
        );
      }),
    );
  }
}

extension MenuResX on MenuRes {
  TextStyle get style {
    if (hasParent) {
      return const TextStyle(
        fontSize: 18,
        color: EfColors.gray6C,
        fontWeight: FontWeight.w300,
      );
    }
    return const TextStyle(
      fontSize: 17,
      color: EfColors.grayTextDark,
      letterSpacing: 1.02,
    );
  }

  Widget? get trailing {
    if (hasChildren) {
      return Icon(
        expand == true ? Icons.expand_less : Icons.expand_more,
      );
    }
    return null;
  }

  Future<void> onTap() async {
    if (hasChildren) {
      toggleExpand();
      return;
    }
    if (url != null && url!.isURL) {
      Get.back();
      await Get.toNamed(Routes.EF_WEB, parameters: {
        Keys.url: url ?? '',
        Keys.title: name ?? '',
      });
      return;
    }
    // final path = this.path ?? '';
    // if (path.isNotEmpty) {
    //   // 可能的路徑
    //   // content\/1026\/display
    //   // my_favorite
    //   // category\/176
    //   // category\/469\/1
    //   // promotion\/172
    //   if (GetUtils.hasMatch(path, '^content')) {
    //     final uri = Uri.https(Constants.efAuthority, path);
    //     await Get.toNamed(Routes.CONTENT, parameters: {
    //       Keys.url: uri.toString(),
    //       Keys.title: name ?? '',
    //     });
    //     return;
    //   }
    //   await Get.toNamed('/$path', parameters: <String, String>{
    //     Keys.title: name ?? '',
    //   });
    // }
    final uri = Uri.parse('/$path');
    if (Get.canLaunchUrl(uri)) {
      Get.back();
      Get.launchUrl(uri);
    }
  }

  Color? get tileColor {
    if (hasParent) {
      return EfColors.grayF0;
    }
    return null;
  }
}
