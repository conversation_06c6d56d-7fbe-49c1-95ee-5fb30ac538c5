import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/models/error_res.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/password_input_controller.dart';

class PasswordInputView extends GetView<PasswordInputController> {
  const PasswordInputView({super.key});
  @override
  Widget build(BuildContext context) {
    return GetBuilder<PasswordInputController>(
      init: PasswordInputController(wabowProvider: Get.find()),
      builder: (_) {
        return Scaffold(
          backgroundColor: Colors.white,
          resizeToAvoidBottomInset: false,
          appBar: AppBar(
            elevation: 0,
            title: Text(
              '會員登入',
              style: TextStyle(
                fontSize: 16.dsp,
                color: EfColors.gray,
              ),
              textAlign: TextAlign.center,
            ),
            centerTitle: true,
            leading: IconButton(
              onPressed: () {
                Get.back(id: SubRouteType.signIn.index);
              },
              // icon: const Icon(Icons.close),
              icon: SvgPicture.asset(
                'assets/images/close.svg',
                width: 30.dw,
                height: 30.dh,
              ),
            ),
          ),
          body: Padding(
            padding: EdgeInsets.symmetric(
              // vertical: 14.dh,
              horizontal: 24.dw,
            ),
            child: SingleChildScrollView(
              child: Column(
                children: _children().toList(growable: false),
              ),
            ),
          ),
        );
      },
    );
  }

  Iterable<Widget> _children() sync* {
    yield SizedBox(height: 30.dh);
    yield Background(
      background: Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: EdgeInsets.only(right: 24.dw),
          child: Obx(() {
            return Visibility(
              visible: controller.password.isEmpty,
              child: Text(
                '輸入密碼',
                style: TextStyle(
                  fontSize: Constants.buttonFontSize.dsp,
                  color: EfColors.grayTextLight,
                ),
                textAlign: TextAlign.right,
              ).paddingOnly(right: 16.dw),
            );
          }),
        ),
      ),
      child: Obx(() {
        return TextFormField(
          obscureText: controller.isPasswordVisible ? false : true,
          onChanged: (value) => controller.password = value,
          // decoration: const InputDecoration(
          //   labelText: '密碼',
          //   hintText: '會員密碼',
          //   suffixIcon: IconButton(
          //     onPressed: () {},
          //     icon: SvgPicture.asset(
          //       'assets/images/eye.svg',
          //       width: 20.dw,
          //       height: 20.dh,
          //     ),
          //   ),
          // ),
          decoration: InputDecoration(
            suffixIcon: IconButton(
              onPressed: () => controller.togglePasswordVisibility(),
              icon: Icon(
                controller.isPasswordVisible
                    ? Icons.visibility
                    : Icons.visibility_off,
                color: EfColors.gray60,
              ),
            ),
          ),
        ).sizedBox(height: Constants.buttonHeight.dh);
      }),
    );

    yield Align(
      alignment: Alignment.centerRight,
      child: TextButton.icon(
        style: TextButton.styleFrom(
          padding: EdgeInsets.zero,
        ),
        onPressed: _onForgetPasswordPressed,
        icon: const Icon(
          Icons.help,
          color: EfColors.gray60,
        ),
        label: Text(
          '忘記密碼',
          style: TextStyle(
            fontSize: 14.dsp,
            color: EfColors.gray60,
          ),
          textAlign: TextAlign.right,
          softWrap: false,
        ),
      ),
    );
    yield SizedBox(height: 20.dh);
    yield Obx(() {
      return ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.zero,
          backgroundColor: EfColors.primary,
          disabledBackgroundColor: EfColors.grayD5,
          shape: const RoundedRectangleBorder(
            borderRadius: Constants.buttonBorderRadius,
          ),
        ),
        onPressed: controller.password.isNotEmpty ? _onNextPressed : null,
        child: Text(
          '下一步',
          style: TextStyle(
            fontSize: Constants.buttonFontSize.dsp,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ).sizedBox(width: double.infinity, height: Constants.buttonHeight.dh);
    });
    yield SizedBox(height: 20.dh);
    yield Text(
      // '<EMAIL>',
      controller.email,
      style: TextStyle(
        fontSize: 16.dsp,
        color: EfColors.grayD5,
      ),
      textAlign: TextAlign.right,
      softWrap: false,
    );
  }

  // Future<void> _onNextPressed() async {
  //   Get.back(
  //     result: controller.password,
  //     id: SubRouteType.signIn.index,
  //   );
  // }

  Future<void> _onNextPressed() async {
    Get.showLoading();
    try {
      final res = await controller.signIn();
      // 成功，關閉 Loading
      Get.back();
      // 關閉所頁面，返回參數
      Get.back(
        result: res,
        id: SubRouteType.signIn.index,
      );
    } on ErrorRes catch (e) {
      Get.back();
      Get.showAlert('$e');
    }
  }

  Future<void> _onForgetPasswordPressed() async {
    try {
      Get.showLoading();
      final res = await controller.forgetPassword();
      Get.back();
      if (res.status == true) {
        Get.showAlert('密碼通知函寄送成功！');
        // Get.showAlert(res.message ?? '已寄出重設密碼信件，請至信箱收取');
      } else {
        throw res.message ?? '發生錯誤';
      }
    } catch (e) {
      Get.back();
      Get.showAlert('$e');
    }
  }
}
