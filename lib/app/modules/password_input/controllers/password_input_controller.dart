import 'package:efshop/app/models/login_req.dart';
import 'package:efshop/app/models/login_res.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

class PasswordInputController extends GetxController {
  final WabowProvider wabowProvider;
  Logger get logger => wabowProvider.logger;
  final _draft = LoginReq().obs;
  String get email => _draft.value.email ?? '';
  set password(String value) {
    _draft.value.password = value;
    _draft.refresh();
  }

  String get password => _draft.value.password ?? '';

  final _passwordVisible = false.obs;
  bool get isPasswordVisible => _passwordVisible.value;
  void togglePasswordVisibility() {
    _passwordVisible.value = !_passwordVisible.value;
  }

  PasswordInputController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.email)) {
      _draft.value.email = Get.parameters[Keys.email];
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<LoginRes> signIn() async {
    // GA: log login
    await _logLogin();
    // final req = LoginReq(email: "<EMAIL>", password: "vufWCR4V");
    return wabowProvider.login(_draft.value);
  }

  Future<MessageRes> forgetPassword() {
    return wabowProvider.forgetPassword(email);
  }

  ///
  /// GA: log login
  ///
  Future<void> _logLogin() async {
    try {
      await FirebaseAnalytics.instance.logLogin(
        loginMethod: 'email',
        parameters: {
          'email': email,
        },
      );
    } catch (e) {
      logger.e(e);
    }
  }
}
