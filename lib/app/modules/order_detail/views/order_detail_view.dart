import 'package:efshop/app/components/colored_divider.dart';
import 'package:efshop/app/components/horizontal_progressing.dart';
import 'package:efshop/app/components/price.dart';
import 'package:efshop/app/components/product_item.dart';
import 'package:efshop/app/models/members_orders_res.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/order_detail_controller.dart';

class OrderDetailView extends GetView<OrderDetailController> {
  const OrderDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('訂單詳情'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) => Obx(() => _body()),
        onError: _onError,
      ),
    );
  }

  Widget _onError(String? error) {
    Future(() =>
        Get.showAlert('訂單(${controller.id})查詢失敗').then((value) => Get.back()));
    // return Center(child: Text(error.toString()));
    return const SizedBox();
  }

  Widget _body() {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: _children().toList(growable: false),
              ),
            ),
          ),
        ),
        _bottom(),
      ],
    );
    // return Background(
    //   // alignment: Alignment.bottomCenter,
    //   background: Expanded(
    //     child: SingleChildScrollView(
    //       padding: const EdgeInsets.only(bottom: 48),
    //       child: SafeArea(
    //         child: Column(
    //           mainAxisSize: MainAxisSize.min,
    //           children: _children().toList(growable: false),
    //         ),
    //       ),
    //     ),
    //   ),
    //   child: _bottom(),
    // );
  }

  Widget _bottom() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Color(0x29555555),
              offset: Offset(0, -2),
              blurRadius: 2,
            ),
          ],
        ),
        child: SafeArea(
          child: SizedBox(
            width: double.infinity,
            height: 48,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              reverse: true,
              padding: const EdgeInsets.symmetric(horizontal: 14),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: _buttons().toList(growable: false),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _buttons() sync* {
    final data = controller.data;
    if (Constants.shippingButton.contains(data.status)) {
      yield const SizedBox(width: 8);
      yield OutlinedButton(
        onPressed: () {
          Get.toNamed(Routes.SHIPPING_DETAIL, parameters: {
            Keys.id: data.id ?? '',
          });
        },
        child: const Text(
          '物流詳情',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
    // if (Constants.refundButton.contains(data.status)) {
    if (data.isRefund == true) {
      yield const SizedBox(width: 8);
      yield OutlinedButton(
        onPressed: () {
          Get.toNamed(Routes.REFUND_DETAIL, parameters: {
            Keys.id: data.id ?? '',
          });
        },
        child: const Text(
          '退貨詳情',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
    // if (['已取貨', '處理中', '已出貨', '待評價'].contains(data.status)) {
    if (data.isRefundAble ?? false) {
      yield const SizedBox(width: 8);
      yield OutlinedButton(
        onPressed: () {
          Get.toNamed(Routes.REFUND, parameters: {
            Keys.id: data.id ?? '',
          });
        },
        child: const Text(
          '申請退貨',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
    if (Constants.invoiceButton.contains(data.status)) {
      yield const SizedBox(width: 8);
      yield OutlinedButton(
        onPressed: () {
          Get.toNamed(Routes.INVOICE, parameters: {
            Keys.id: data.id ?? '',
          });
        },
        child: const Text(
          '查看發票',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
    // if (Constants.ratingButton.contains(data.status)) {
    if (data.isProductComment != true) {
      yield const SizedBox(width: 8);
      yield OutlinedButton(
        onPressed: () {
          Get.toNamed(Routes.LEAVE_COMMENT, parameters: {
            Keys.id: data.id ?? '',
          });
        },
        child: const Text(
          '評價商品',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
    // 取消訂單按鈕
    if (data.isCancelAble ?? false) {
      yield const SizedBox(width: 8);
      yield OutlinedButton(
        onPressed: () {
          _onCancelTapped(data);
        },
        child: const Text(
          '取消訂單',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
  }

  Future<void> _onCancelTapped(MembersOrdersRes order) async {
    final res = await Get.showConfirm(
      '即將取消訂單',
      textConfirm: '取消訂單',
      textCancel: '不取消',
    );
    if (res == Button.confirm) {
      Get.showLoading();
      try {
        await controller.wabowProvider.postMembersOrdersCancel(order.id ?? '');
        Get.back();
        Get.showAlert('取消訂單成功');
        await controller.onRefresh();
      } catch (e) {
        Get.back();
        Get.showAlert(e.toString());
      }
    }
  }

  Iterable<Widget> _children() sync* {
    final order = controller.data;
    yield _progressing().sizedBox(height: 80);
    yield Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 16),
      // height: 104,
      width: double.infinity,
      alignment: Alignment.centerLeft,
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            // '王*明',
            order.receiverName ?? '',
            style: const TextStyle(
              fontSize: 14,
              color: EfColors.gray,
            ),
            softWrap: false,
          ),
          Visibility(
            visible: order.containsStoreId,
            child: Text(
              // '全家深坑昇高店',
              order.storeName ?? '',
              style: const TextStyle(
                fontSize: 14,
                color: EfColors.gray,
              ),
              softWrap: false,
            ),
          ),
          Text(
            // '新北市深坑區埔新街59號',
            order.displayAddress,
            style: const TextStyle(
              fontSize: 14,
              color: EfColors.gray,
            ),
            softWrap: true,
          )
        ],
      ),
    );
    yield const SizedBox(height: 6);
    yield Container(
      padding: const EdgeInsets.symmetric(horizontal: 14),
      alignment: Alignment.centerLeft,
      width: double.infinity,
      height: 70,
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                // '訂單編號：43654345',
                '訂單編號：${order.number ?? ''}',
                style: const TextStyle(
                  fontSize: 13,
                  color: EfColors.gray,
                ),
                softWrap: false,
              ).expanded(),
              Text(
                order.displayStatus,
                style: const TextStyle(
                  fontSize: 13,
                  color: EfColors.gray94,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            // '下單時間：2021-04-01 14:43:23',
            '下單時間：${order.createDatetime ?? ''}',
            style: const TextStyle(
              fontSize: 13,
              color: EfColors.gray,
            ),
            softWrap: false,
          ),
        ],
      ),
    );
    yield const ColoredDivider();
    final products = order.normalProducts;
    yield ColoredBox(
      color: Colors.white,
      child: ListView.separated(
        itemCount: products.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final product = products.elementAt(index);
          return ProductItem(product);
        },
        separatorBuilder: (context, index) {
          return const ColoredDivider();
        },
      ),
    );
    yield const ColoredDivider();
    yield _conclusion();
    if (controller.data.hasComment) {
      yield const SizedBox(height: 6);
      yield ListTile(
        title: const Text('訂單備註：已填寫'),
        trailing: const Icon(Icons.chevron_right),
        onTap: () {
          final text = controller.data.comment ?? '';
          Get.showAlert(text);
        },
      );
    }
    yield const SizedBox(height: 6);
    yield _invoice();
    yield const SizedBox(height: 6);
    yield _comment();
  }

  void _showInvoiceDetail() {
    final order = controller.data;
    if (order.hasInvoiceNumber) {
      Get.toNamed(Routes.INVOICE, parameters: {
        Keys.id: order.id ?? '',
      });
    } else {
      const cancelMessage = '此單已完成退款，無發票';
      const paddingMessage = '待鑑賞期後，提供發票';
      final message =
          controller.data.isCanceled ? cancelMessage : paddingMessage;
      Get.showAlert(message);
    }
  }

  Widget _invoice() {
    final order = controller.data;
    final invoiceType = order.invoiceType;
    if (InvoiceType.company == invoiceType) {
      return ListTile(
        title: const Text('發票類型：公司-統一編號'),
        trailing: Text(
          order.vatNumber ?? '',
          softWrap: false,
          overflow: TextOverflow.ellipsis,
        ),
        onTap: _showInvoiceDetail,
      );
    }
    if (InvoiceType.loveCode == invoiceType) {
      return ListTile(
        title: const Text('發票類型：捐贈發票'),
        trailing: Text(
          order.displayInvoiceVehicle,
          softWrap: false,
          overflow: TextOverflow.ellipsis,
        ),
      );
    }
    if (InvoiceType.carrier == invoiceType) {
      return ListTile(
        title: const Text('發票類型：個人-手機條碼'),
        trailing: Text(
          order.displayInvoiceVehicle,
          softWrap: false,
          overflow: TextOverflow.ellipsis,
        ),
        onTap: _showInvoiceDetail,
      );
    }
    // personal
    if (InvoiceType.personal == invoiceType) {
      return ListTile(
        title: const Text('發票類型：個人-電子發票'),
        // trailing: Text(order.displayInvoiceNumber),
        onTap: _showInvoiceDetail,
      );
    }
    return ListTile(
      title: const Text('發票類型：個人-電子發票'),
      // trailing: Text(order.displayInvoiceNumber),
      onTap: _showInvoiceDetail,
    );
  }

  Widget _conclusion() {
    Iterable<TableRow> children() sync* {
      final order = controller.data;
      yield TableRow(
        children: [
          // const SizedBox(height: 40),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              '共${order.quantity}件商品',
              style: const TextStyle(
                fontSize: 12,
                color: EfColors.gray94,
              ),
              softWrap: false,
            ),
          ).sizedBox(height: 40),
          const Align(
            alignment: Alignment.centerRight,
            child: Text(
              '金額',
              style: TextStyle(
                fontSize: 12,
                color: EfColors.gray,
              ),
              softWrap: false,
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Price(order.normalProductsAmount),
          ),
        ],
      );
      yield TableRow(
        children: [
          const SizedBox(height: 20),
          const Align(
            alignment: Alignment.centerRight,
            child: Text(
              '優惠折扣',
              style: TextStyle(
                fontSize: 12,
                color: EfColors.gray,
              ),
              textAlign: TextAlign.right,
              softWrap: false,
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Price(order.discountProductsAmount),
          ),
        ],
      );
      yield TableRow(
        children: [
          const SizedBox(height: 40),
          const Align(
            alignment: Alignment.centerRight,
            child: Text(
              '運費',
              style: TextStyle(
                fontSize: 12,
                color: EfColors.gray,
              ),
              softWrap: false,
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Price(order.shippingFee ?? 0),
          ),
        ],
      );
      yield const TableRow(
        children: [
          Divider(height: 1),
          Divider(height: 1),
          Divider(height: 1),
        ],
      );
      yield TableRow(
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              // '全家取貨付款',
              controller.data.paymentName ?? '',
              style: const TextStyle(
                fontSize: 12,
                color: EfColors.gray94,
              ),
              softWrap: false,
            ),
          ),
          const SizedBox(height: 50),
          Align(
            alignment: Alignment.centerRight,
            child: Price(
              // 918,
              controller.data.total ?? 0,
              color: EfColors.primary,
            ),
          ),
        ],
      );
    }

    return ColoredBox(
      color: Colors.white,
      child: Row(
        children: [
          const Spacer(),
          SizedBox(
            width: 255.dw,
            child: Table(
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              border: TableBorder.all(color: Colors.transparent),
              children: children().toList(growable: false),
            ),
          ),
          const SizedBox(width: 14),
        ],
      ),
    );
  }

  Widget _comment() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(height: 15);
      yield const Text(
        Constants.servicePhone,
        style: TextStyle(
          fontSize: 13,
          color: EfColors.gray94,
        ),
        softWrap: false,
      );
      yield const SizedBox(height: 4);
      yield const Text(
        Constants.serviceTime,
        style: TextStyle(
          fontSize: 13,
          color: EfColors.gray94,
        ),
        softWrap: false,
      );
      yield const SizedBox(height: 15);
      yield Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          OutlinedButton(
            onPressed: () {
              Get.toNamed(Routes.SERVICE);
            },
            child: const Text(
              '詢問',
              style: TextStyle(
                fontSize: 13,
                color: EfColors.gray,
              ),
              textAlign: TextAlign.center,
            ),
          ).expanded(),
          const SizedBox(width: 14),
          OutlinedButton(
            onPressed: () {
              Get.toNamed(Routes.LEAVE_MESSAGE, parameters: {
                Keys.id: controller.id,
              });
            },
            child: const Text(
              '留言',
              style: TextStyle(
                fontSize: 13,
                color: EfColors.gray,
              ),
              textAlign: TextAlign.center,
            ),
          ).expanded(),
        ],
      ).sizedBox(height: 30);
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14),
      color: Colors.white,
      width: double.infinity,
      height: 124,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _cancelProgressing() sync* {
    yield const Spacer();
    yield Flexible(
      child: HorizontalProgressing(
        beginPoint: true,
        name: OrderProcess.created.display,
        leftLine: false,
      ),
    );
    yield Flexible(
      child: HorizontalProgressing(
        endPoint: true,
        name: OrderProcess.cancel.display,
        rightLine: false,
      ),
    );
    yield const Spacer();
  }

  Iterable<Widget> _normalProgressing() sync* {
    yield Flexible(
      child: HorizontalProgressing(
        beginPoint: true,
        name: OrderProcess.created.display, // 訂單成立
        leftLine: false,
        rightLine: controller.data.containsProcessing,
      ),
    );
    yield Flexible(
      child: HorizontalProgressing(
        name: OrderProcess.processing.display, // 處理中
        leftLine: controller.data.containsProcessing,
        rightLine: controller.data.containsShipped,
      ),
    );
    yield Flexible(
      child: HorizontalProgressing(
        name: OrderProcess.shipped.display, // 已出貨
        leftLine: controller.data.containsShipped,
        rightLine: controller.data.containsDelivering,
      ),
    );
    yield Flexible(
      child: HorizontalProgressing(
        name: OrderProcess.delivering.display, // 派送中
        leftLine: controller.data.containsDelivering,
        rightLine: controller.data.containsEndPoint,
      ),
    );
    yield Flexible(
      child: HorizontalProgressing(
        endPoint: true,
        // name: OrderProcess.success.display,
        // name: OrderProcess.arrived.display,
        name: controller.data.displayEndPoint, // 送達門市(超取) or 派送成功(宅配)
        leftLine: controller.data.containsEndPoint,
        rightLine: false,
      ),
    );
  }

  // 進度條
  Widget _progressing() {
    final ls =
        controller.data.isCanceled ? _cancelProgressing : _normalProgressing;
    controller.data.status;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: ls().toList(growable: false),
    );
  }
}
