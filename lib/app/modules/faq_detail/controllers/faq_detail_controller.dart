import 'package:efshop/app/models/faq_res.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class FaqDetailController extends GetxController with StateMixin<String> {
  final _data = FaqRes().obs;
  FaqRes get data => _data.value;
  final webViewController = WebViewController();
  static const _htmlHeader =
      '<head><meta name="viewport" content="width=device-width, initial-scale=1"></head>';

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.data)) {
      final jsonString = Get.parameters[Keys.data] ?? '{}';
      _data.value = FaqRes.fromRawJson(jsonString);
    }
    webViewController.setJavaScriptMode(JavaScriptMode.unrestricted);
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      if (data.name != null && data.name!.isNotEmpty) {
        final htmlString = '$_htmlHeader${data.description}';
        await webViewController.loadHtmlString(htmlString);
        change('', status: RxStatus.success());
      } else {
        change('', status: RxStatus.empty());
      }
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
