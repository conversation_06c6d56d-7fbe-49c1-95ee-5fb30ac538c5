import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../controllers/faq_detail_controller.dart';

class FaqDetailView extends GetView<FaqDetailController> {
  const FaqDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('客服訊息'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) => _body(),
        onError: (error) {
          return ErrorButton(
            error,
            onTap: controller.onRefresh,
          );
        },
      ),
    );
  }

  Widget _body() {
    return _children().toList().column();
  }

  Iterable<Widget> _children() sync* {
    yield Obx(() {
      return ListTile(
        // horizontalTitleGap: 0,
        leading: Container(
          width: 26,
          height: 26,
          alignment: Alignment.center,
          decoration: const BoxDecoration(
            color: EfColors.primary,
            shape: BoxShape.circle,
          ),
          child: SvgPicture.asset(
            'assets/images/q.svg',
            width: 14,
            height: 16,
            fit: BoxFit.contain,
          ),
        ),
        title: Text(
          // '下單後多久可收到',
          controller.data.name ?? '',
          style: const TextStyle(
            fontSize: 15,
            color: EfColors.gray6B,
          ),
          softWrap: false,
        ),
      );
    });
    yield const Divider(height: 1);
    yield Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.dw),
        child: WebViewWidget(
          controller: controller.webViewController,
        ),
      ),
    );
  }
}
