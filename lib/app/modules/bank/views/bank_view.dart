import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/components/input.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/bank_controller.dart';

class BankView extends GetView<BankController> {
  const BankView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('填寫退款帳號'),
        centerTitle: true,
      ),
      body: controller.obx((state) {
        return Background(
          background: SizedBox.expand(
            child: _body(),
          ),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: ColoredBox(
              color: Colors.white,
              child: Safe<PERSON><PERSON>(
                child: _bottom(),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _bottom() {
    Iterable<Widget> children() sync* {
      yield ColoredBox(
        color: Colors.white,
        child: Obx(() {
          return CheckboxListTile(
            activeColor: EfColors.primary,
            contentPadding: EdgeInsets.symmetric(horizontal: 12.dw),
            shape: const CircleBorder(),
            value: controller.agreement,
            onChanged: (value) => controller.agreement = value ?? false,
            title: Transform.translate(
              offset: Offset(-16.dw, 0),
              child: Text.rich(
                TextSpan(
                  style: const TextStyle(
                    fontSize: 14,
                    color: EfColors.grayTextDark,
                  ),
                  children: [
                    const TextSpan(
                      text: '已詳閱',
                    ),
                    TextSpan(
                      text: '「退貨注意事項說明」',
                      style: const TextStyle(
                        color: EfColors.gray93,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Get.toNamed(Routes.EF_WEB, parameters: {
                            Keys.title: '退貨注意事項說明',
                            Keys.url: Constants.uriRefund.toString(),
                          });
                        },
                    ),
                  ],
                ),
                textHeightBehavior:
                    const TextHeightBehavior(applyHeightToFirstAscent: false),
                softWrap: false,
              ),
            ),
            controlAffinity: ListTileControlAffinity.leading,
          );
        }),
      ).expanded();
      yield SizedBox(
        width: 114.dw,
        height: 55.dh,
        child: Obx(() {
          return ElevatedButton(
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              // padding: EdgeInsets.symmetric(vertical: 8.dh, horizontal: 16.dw),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero,
              ),
              textStyle: const TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
            ),
            onPressed: controller.agreement ? _submit : null,
            child: const Text(
              '確認退貨',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          );
        }),
      );
    }

    return SizedBox(
      height: 55.dh,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Future<void> _submit() async {
    try {
      // 顯示確認視窗
      final res = await Get.showConfirm<Button>(
        '即將申請退貨',
        textConfirm: '退貨',
      );
      if (res == Button.confirm) {
        await _submitImpl();
      }
    } catch (e) {
      Get.showAlert(e.toString());
    }
  }

  Future<void> _submitImpl() async {
    Get.showLoading();
    try {
      final res = await controller.submit();
      Get.back(); // 關閉 Loading
      await _trySaveBank();
      if (res.status == true) {
        // 關閉前兩個頁面
        Get.back();
        Get.back();
        Get.toNamed(Routes.REFUND_SUCCESS, parameters: {
          Keys.id: controller.id,
        });
      } else {
        throw res.message ?? '退貨失敗';
      }
    } catch (e) {
      Get.back(); // 關閉 Loading
      Get.showAlert(e.toString());
    }
  }

  Future<void> _trySaveBank() async {
    final bankId = controller.draft.userRefundId;
    if (bankId == null || bankId.isEmpty) {
      // 使用者自行輸入銀行資訊，詢問是否記錄
      final res = await Get.showConfirm<Button>(
        '是否紀錄此退款帳號',
        textConfirm: '是',
        textCancel: '否',
      );
      if (res == Button.confirm) {
        Get.showLoading();
        try {
          // 紀錄銀行資訊
          await controller.saveBank();
          // 關閉 Loading
          Get.back();
        } catch (e) {
          // 不顯示錯誤訊息
          // await Get.showAlert(e.toString());
        }
      }
    }
  }

  Widget _body() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        // crossAxisAlignment: CrossAxisAlignment.start,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield Input(
      keyboardType: TextInputType.number,
      controller: controller.bankCodeEditing,
      hintText: '銀行代碼',
      onChanged: (value) {
        controller.draft.bankCode = value;
        controller.draft.userRefundId = null;
      },
      onClear: () {
        controller.bankCodeEditing.clear();
        controller.draft.bankCode = '';
        controller.draft.userRefundId = null;
      },
    );
    yield const Divider(height: 1);
    yield Input(
      controller: controller.bankBranchEditing,
      hintText: '銀行名稱',
      onChanged: (value) {
        controller.draft.bankBranch = value;
        controller.draft.userRefundId = null;
      },
      onClear: () {
        controller.bankBranchEditing.clear();
        controller.draft.bankBranch = '';
        controller.draft.userRefundId = null;
      },
    );
    yield const Divider(height: 1);
    yield Input(
      controller: controller.accountNameEditing,
      hintText: '帳號戶名',
      onChanged: (value) {
        controller.draft.accountName = value;
        controller.draft.userRefundId = null;
      },
      onClear: () {
        controller.accountNameEditing.clear();
        controller.draft.accountName = '';
        controller.draft.userRefundId = null;
      },
    );
    yield const Divider(height: 1);
    yield Input(
      keyboardType: TextInputType.number,
      controller: controller.accountNumberEditing,
      hintText: '帳號(14碼內半形數字, 不含符號、空格)',
      onChanged: (value) {
        controller.draft.accountNumber = value;
        controller.draft.userRefundId = null;
      },
      onClear: () {
        controller.accountNumberEditing.clear();
        controller.draft.accountNumber = '';
        controller.draft.userRefundId = null;
      },
    );
    yield SizedBox(height: 40.dh);
    yield OutlinedButton(
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.symmetric(vertical: 8.dh, horizontal: 16.dw),
        side: BorderSide(
          color: EfColors.gray93,
          width: 1.dw,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(2),
        ),
      ),
      onPressed: () async {
        final res = await Get.toNamed(Routes.BANK_MANAGER);
        if (res != null && res is String) {
          // 把點選資料帶回
          controller.bankId = res;
        }
        // await Get.toNamed(Routes.BANK_MANAGER);
        // controller.tryUpdateBank();
      },
      child: const Text(
        '選擇退款帳號',
        style: TextStyle(
          fontSize: 13,
          color: EfColors.gray93,
        ),
        textAlign: TextAlign.center,
      ),
    );
    yield SizedBox(height: 80.dh);
  }
}
