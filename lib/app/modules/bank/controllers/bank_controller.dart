import 'dart:async';

import 'package:efshop/app/models/members_orders_refund_post_req.dart';
import 'package:efshop/app/models/members_orders_res.dart';
import 'package:efshop/app/models/members_refund_res.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/bank_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

class BankController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final BankProvider bankProvider;
  WabowProvider get wabowProvider => bankProvider.wabowProvider;
  Logger get logger => wabowProvider.logger;
  final _agreement = false.obs;
  bool get agreement => _agreement.value;
  set agreement(bool value) => _agreement.value = value;
  final TextEditingController accountNumberEditing = TextEditingController();
  final TextEditingController accountNameEditing = TextEditingController();
  final TextEditingController bankCodeEditing = TextEditingController();
  final TextEditingController bankBranchEditing = TextEditingController();
  final _draft = MembersOrdersRefundPostReq().obs;
  MembersOrdersRefundPostReq get draft => _draft.value;
  final _id = ''.obs;
  String get id => _id.value;
  final _data = MembersOrdersRes().obs;
  MembersOrdersRes get data => _data.value;

  BankController({
    required this.bankProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // 監聽預設銀行 id
    // bankProvider.defaultBankIdStream
    //     .takeUntil(_disposable.future)
    //     .listen((event) {
    //   bankId = event;
    // });
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id]!;
    }
    if (Get.parameters.containsKey(Keys.data)) {
      draft.products = Get.parameters[Keys.data];
    }
  }

  Future<void> _fetchOrder() async {
    try {
      _data.value = await wabowProvider.getMembersOrdersWithId(id);
    } catch (e) {
      logger.e(e.toString());
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final res = await bankProvider.getBanks();
      bank = res.firstWhere(
        (element) => element.isDefault == true,
        orElse: () => MembersRefundRes(),
      );
      await _fetchOrder();
      // 如果是信用卡或LINE Pay 則不需填寫銀行資訊
      if (data.needBankInfoWhenRefund) {
        change('', status: RxStatus.success());
      } else {
        await _refundWithoutBankInfo();
      }
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  // 如果是信用卡或LINE Pay 則不需填寫銀行資訊
  Future<void> _refundWithoutBankInfo() async {
    final res = await wabowProvider.postMembersOrdersRefund(id, draft);
    if (res.status == true) {
      // 關閉前兩個頁面
      Get.back();
      Get.back();
      Get.toNamed(Routes.REFUND_SUCCESS, parameters: {
        Keys.id: id,
      });
    } else {
      throw res.message ?? '退款失敗';
    }
  }

  Future<MessageRes> submit() {
    // 如果有選擇銀行，就不需要填寫銀行資訊
    if (draft.userRefundId != null && draft.userRefundId!.isNotEmpty) {
      draft.accountName = null;
      draft.accountNumber = null;
      draft.bankCode = null;
      draft.bankBranch = null;
    }
    draft.validate();
    return wabowProvider.postMembersOrdersRefund(id, draft);
  }

  set bank(MembersRefundRes bank) {
    accountNumberEditing.text = bank.accountNumber ?? '';
    accountNameEditing.text = bank.accountName ?? '';
    bankCodeEditing.text = bank.bankCode ?? '';
    bankBranchEditing.text = bank.bankBranch ?? '';
    // draft.accountName = bank.accountName;
    // draft.accountNumber = bank.accountNumber;
    // draft.bankCode = bank.bankCode;
    // draft.bankBranch = bank.bankBranch;
    draft.userRefundId = bank.id;
  }

  set bankId(String id) => bank = bankProvider.getBank(id);

  // void tryUpdateBank() {
  //   if (bankProvider.defaultBankId.isEmpty) {
  //     // 刪除預設銀行且有銀行 id，清空銀行資訊
  //     if (draft.userRefundId!.isNotEmpty) {
  //       // 清空銀行資訊
  //       bank = MembersRefundRes();
  //     }
  //   } else {
  //     // 變更預設銀行
  //     if (draft.userRefundId != bankProvider.defaultBankId) {
  //       bankId = bankProvider.defaultBankId;
  //     }
  //   }
  // }

  Future<MessageRes> saveBank() async {
    return bankProvider.create(draft.toMembersRefundPostReq());
  }
}
