import 'package:efshop/app/models/members_orders_refund_res.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';

class RefundSuccessController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  final _id = ''.obs;
  String get id => _id.value;
  final _data = MembersOrdersRefundRes().obs;
  MembersOrdersRefundRes get data => _data.value;

  final refundDoc = [
    {
      'title_text': '1. 申請後記下退貨便代碼',
      'icon_path': 'assets/images/refund_icon01.png',
    },
    {
      'title_text': '2. 妥善包裝退貨商品',
      'icon_path': 'assets/images/refund_icon02.png',
    },
    {
      'title_text': '3. 超商機台列印「退貨單」',
      'icon_path': 'assets/images/refund_icon03.png',
    },
    {
      'title_text': '4. 交付櫃檯完成退貨',
      'icon_path': 'assets/images/refund_icon04.png',
    },
  ].obs;

  RefundSuccessController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id]!;
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      _data.value = await wabowProvider.getMembersOrdersRefundMessages(id);
      await _logRefund();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  ///
  /// GA: log refund
  ///
  Future<void> _logRefund() async {
    try {
      await FirebaseAnalytics.instance.logRefund(
        currency: 'TWD',
        // coupon: data.coupon,
        value: data.amountOfRefund.toDouble(),
        // tax: data.otherMoney?.toDouble(),
        shipping: data.shippingFee?.toDouble(),
        transactionId: data.orderNumber,
        // affiliation: data.affiliation,
        items: data.products
            ?.map((e) => e.toAnalyticsEventItem())
            .toList(growable: false),
      );
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
