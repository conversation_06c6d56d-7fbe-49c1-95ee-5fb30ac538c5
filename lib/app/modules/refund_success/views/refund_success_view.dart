import 'package:efshop/app/components/icon_text.dart';
import 'package:efshop/app/components/refund_code.dart';
import 'package:efshop/app/models/message_data.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/refund_success_controller.dart';

class RefundSuccessView extends GetView<RefundSuccessController> {
  const RefundSuccessView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('申請退貨'),
        centerTitle: true,
      ),
      body: controller.obx((state) {
        return Obx(() => _body());
      }),
    );
  }

  Widget _body() {
    return SingleChildScrollView(
      child: Safe<PERSON>rea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: _children().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _superMarket() sync* {
    yield SizedBox(height: 20.dh);
    yield RefundCode(
      type: controller.data.shippingType,
      // code: 'A55159390535',
      code: controller.data.shippingNumber ?? '',
    );
    yield SizedBox(height: 20.dh);
  }

  Iterable<Widget> _children() sync* {
    yield Container(
      height: 40.dh,
      alignment: Alignment.center,
      decoration: const BoxDecoration(
        color: EfColors.grayF6,
      ),
      child: const Text(
        '因系統作業時間，請於30分鐘後，再前往超商辦理退貨。',
        style: TextStyle(
          fontSize: 12,
          color: EfColors.gray47,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      ),
    );
    yield* _superMarket();
    // yield _step();
    yield Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _steps().toList(growable: false),
    );
    yield SizedBox(height: 30.dh);
    yield OutlinedButton(
      style: OutlinedButton.styleFrom(
        // padding: EdgeInsets.symmetric(vertical: 8.dh, horizontal: 16.dw),
        side: const BorderSide(
          width: 1,
          color: EfColors.grayE0,
        ),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(2)),
        ),
        fixedSize: Size(117.dw, 30.dh),
      ),
      onPressed: () {
        Get.toNamed(Routes.REFUND_DETAIL, parameters: {
          Keys.id: controller.id,
        });
      },
      child: const Text(
        '查看退貨詳情',
        style: TextStyle(
          fontSize: 13,
          color: EfColors.gray,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Iterable<Widget> _steps() sync* {
    for (final json in controller.refundDoc) {
      yield IconText(MessageData.fromJson(json));
      yield SizedBox(height: 8.dh);
    }
  }

  // Widget _step() {
  //   final it = controller.refundDoc;
  //   return ListView.separated(
  //     physics: const NeverScrollableScrollPhysics(),
  //     shrinkWrap: true,
  //     itemBuilder: (context, index) {
  //       final data = MessageData.fromJson(it.elementAt(index));
  //       return _Item(data);
  //     },
  //     separatorBuilder: (context, index) => SizedBox(height: 8.dh),
  //     itemCount: it.length,
  //   );
  // }
}
