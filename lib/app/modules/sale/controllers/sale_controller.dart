import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:talker_flutter/talker_flutter.dart';

class SaleController extends GetxController
    with StateMixin<String>, ScrollMixin, GetSingleTickerProviderStateMixin {
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  Logger get logger => wabowProvider.logger;
  Talker get talker => wabowProvider.talker;
  // tabs
  final _tabs = <Category>[].obs;
  Iterable<Category> get tabs => _tabs;
  // tab controller
  final _tabController = Rx<TabController?>(null);
  TabController? get tabController {
    if (_tabController.value == null) {
      final tabCount = tabs.isEmpty ? 1 : tabs.length;
      _tabController.value = TabController(
        vsync: this,
        length: tabCount,
      );
    }
    return _tabController.value;
  }

  // page controller
  final _pageController = Rx<PageController?>(null);
  PageController get pageController {
    final currentIndex = tabs.isEmpty ? 0 : (tabController?.index ?? 0);
    _pageController.value ??= PageController(
      keepPage: false,
      viewportFraction: 1,
      initialPage: currentIndex,
    );
    final res = _pageController.value!;
    if (res.initialPage != currentIndex) {
      _safeJumpToPage(currentIndex);
    }
    return res;
  }

  SaleController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _pageController.value?.dispose();
    _tabController.value?.dispose();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final res = await wabowProvider.getPromotionsLimitedNMGiftMix();
      _tabs.assignAll(res.where((element) => element.isVisible));
      for (var element in _tabs) {
        element.name = element.name?.replaceAll('‧', '\n');
      }
      
      // Recreate TabController when tabs change
      _tabController.value?.dispose();
      _tabController.value = null;
      
      final status = tabs.isEmpty ? RxStatus.empty() : RxStatus.success();
      change('', status: status);
    } catch (e) {
      change(e.toString(), status: RxStatus.error());
    }
  }

  /// Safely jump to specified page to avoid "Bad state: No element" error
  void _safeJumpToPage(int index) {
    final controller = _pageController.value;
    if (controller == null || tabs.isEmpty) return;
    
    // Use addPostFrameCallback to ensure execution after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // Check if PageController has valid position
        if (controller.hasClients && controller.positions.isNotEmpty) {
          final safeIndex = index.clamp(0, tabs.length - 1);
          controller.jumpToPage(safeIndex);
        } else {
          // If no clients yet, retry with delay
          Future.delayed(const Duration(milliseconds: 100), () {
            _safeJumpToPage(index);
          });
        }
      } catch (e, s) {
        talker.error('Failed to jump to page $index: $e', e, s);
      }
    });
  }

  @override
  Future<void> onEndScroll() async {}

  @override
  Future<void> onTopScroll() async {}
}
