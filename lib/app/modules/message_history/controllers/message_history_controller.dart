import 'dart:async';

import 'package:efshop/app/models/members_orders_questions_res.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';

class MessageHistoryController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  final _disposable = Completer();
  final _id = ''.obs;
  String get id => _id.value;
  final data = <MembersOrdersQuestionsRes>[].obs;

  MessageHistoryController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id]!;
    }
    // onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final res = await wabowProvider.getMembersOrdersQuestions(id);
      data.assignAll(res);
      change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
