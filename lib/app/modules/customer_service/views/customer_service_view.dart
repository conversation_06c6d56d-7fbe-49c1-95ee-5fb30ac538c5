import 'package:efshop/app/components/ef_center.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/components/message_item.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/customer_service_controller.dart';

class CustomerServiceView extends GetView<CustomerServiceController> {
  const CustomerServiceView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('客服紀錄'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) => SafeArea(
          child: Obx(() {
            return _body();
          }),
        ),
        onEmpty: _empty(),
        onError: (error) {
          return ErrorButton(
            error,
            onTap: controller.onRefresh,
          );
        },
      ),
    );
  }

  Widget _empty() {
    Iterable<Widget> children() sync* {
      yield SvgPicture.asset(
        'assets/images/message.svg',
        fit: BoxFit.contain,
        height: 46.dh,
        width: 56.dw,
      );
      yield SizedBox(height: 20.dh);
      yield Text(
        '目前沒有留言紀錄',
        style: TextStyle(
          fontSize: 13.dsp,
          color: EfColors.gray93,
        ),
        softWrap: false,
      );
    }

    return EfCenter(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _body() {
    final it = controller.data;
    return ListView.separated(
      padding: const EdgeInsets.symmetric(
        horizontal: 18,
        vertical: 14,
      ),
      itemBuilder: (context, index) {
        final data = it.elementAt(index).toMessageData();
        return MessageItem(
          data,
          onTap: () {
            Get.toNamed(Routes.SERVICE_DETAIL, parameters: {
              Keys.data: data.toRawJson(),
            });
          },
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 14);
      },
      itemCount: it.length,
    );
  }
}
