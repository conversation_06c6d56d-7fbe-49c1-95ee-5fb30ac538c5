import 'dart:async';

import 'package:efshop/app/models/members_messages_question.dart';
import 'package:efshop/app/providers/message_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/error_type.dart';
import 'package:get/get.dart';

class CustomerServiceController extends GetxController with StateMixin<String> {
  final MessageProvider messageProvider;
  WabowProvider get wabowProvider => messageProvider.wabowProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  final _disposable = Completer();
  final data = <MembersMessagesQuestion>[].obs;

  CustomerServiceController({
    required this.messageProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      if (prefProvider.isNotLogin) {
        throw ErrorType.unauthorized;
      }
      messageProvider.markAsRead(MessageType.questions);
      final res = await wabowProvider.getMembersMessagesQuestions();
      data.assignAll(res);
      change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
