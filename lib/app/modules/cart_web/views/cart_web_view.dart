import 'package:efshop/constants.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../controllers/cart_web_controller.dart';

class CartWebView extends GetView<CartWebController> {
  final String? tag;
  CartWebView({
    super.key,
    AsyncValueSetter<IndexTab>? changeTab,
  }) : tag = '${DateTime.now().hashCode}' {
    Get.lazyPut<CartWebController>(
      () => CartWebController(
        wabowProvider: Get.find(),
        changeTab: changeTab,
      ),
      tag: tag,
      fenix: true,
    );
    initializeController().then((value) {
      final controller = Get.find<CartWebController>(tag: tag);
      controller.url = Constants.uriCart.toString();
      controller.cartViewed();
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: Get.find<CartWebController>(tag: tag),
      tag: tag,
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            leading: Obx(() {
              if (controller.prefProvider.cartQuantity > 0) {
                return BackButton(
                  onPressed: controller.onBackPressed,
                );
              }
              return const SizedBox();
            }),
            title: const Text('購物車'),
            centerTitle: true,
          ),
          body: _body(),
        );
      },
    );
  }

  Widget _body() {
    return Column(
      children: [
        Expanded(
          child: WebViewWidget(controller: controller.webViewController),
        ),
        Obx(() {
          final height = controller.toggle ? 1.0 : 0.0;
          return SizedBox(height: height);
        }),
      ],
    );
  }
}
