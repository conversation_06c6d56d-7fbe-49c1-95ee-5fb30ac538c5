import 'package:efshop/app/models/products_id_comments_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';

class CommentController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  final _id = ''.obs;
  String get id => _id.value;
  final data = <ProductsCommentsRes>[].obs;

  CommentController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _fetchParameters();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void _fetchParameters() {
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id] ?? '';
    }
  }

  Future<void> onRefresh() async {
    try {
      final box = boxProvider.getGsBox(Boxes.productComment.name);
      final json = box.read(id);
      if (json != null) {
        final it = List.from(json).map((e) => ProductsCommentsRes.fromJson(e));
        data.assignAll(it);
      } else {
        final it = await wabowProvider.getProductsComments(id);
        data.assignAll(it);
      }
      data.isEmpty
          ? change('', status: RxStatus.empty())
          : change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
