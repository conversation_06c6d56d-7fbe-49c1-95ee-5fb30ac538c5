import 'package:efshop/app/components/thumbnail_image.dart';
import 'package:efshop/app/models/products_id_comments_res.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/comment_controller.dart';

class CommentView extends GetView<CommentController> {
  CommentView({Key? key}) : super(key: key) {
    Get.lazyPut<CommentController>(
      () => CommentController(
        wabowProvider: Get.find(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CommentController>(
      init: Get.find<CommentController>(),
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            title: const Text('評價'),
            centerTitle: true,
          ),
          body: controller.obx(
            (state) => _body(),
            onEmpty: _empty(),
          ),
        );
      },
    );
  }

  Widget _empty() {
    return const Center(
      child: Text(
        '沒有資料',
        style: TextStyle(fontSize: 20),
      ),
    );
  }

  Widget _body() {
    final it = controller.data;
    return ListView.separated(
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return _Item(data);
      },
      separatorBuilder: (context, index) {
        return const Divider();
      },
      itemCount: it.length,
    );
  }
}

class _Item extends StatelessWidget {
  final ProductsCommentsRes data;

  const _Item(
    this.data, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    Iterable<Widget> children() sync* {
      yield ThumbnailImage(data.thumbnail);
      yield const SizedBox(width: 12);
      yield _body().expanded();
    }

    return SizedBox(
      height: 116.dh,
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 16.dw,
          vertical: 8.dh,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: children().toList(growable: false),
        ),
      ),
    );
  }

  Widget _body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: 8);
    yield Text(
      '訂單後四碼：${data.simpleOrderNumber}',
      style: const TextStyle(
        fontSize: 12,
        color: EfColors.grayTextLight,
      ),
      softWrap: false,
    );
    yield Text(
      // '好穿',
      data.userComment ?? '',
      style: const TextStyle(
        fontSize: 13,
        color: EfColors.grayText,
      ),
      softWrap: true,
      maxLines: 2,
    );
    yield Expanded(
      child: Align(
        alignment: Alignment.bottomLeft,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              // '酒紅S',
              data.productSpecName ?? '',
              style: const TextStyle(
                fontSize: 12,
                color: EfColors.grayTextLight,
              ),
              softWrap: false,
            ).expanded(),
            Text(
              // '2020-12-19 07:45:34',
              data.userCommentDatetime ?? '',
              style: const TextStyle(
                fontSize: 12,
                color: EfColors.grayTextLight,
              ),
              textAlign: TextAlign.right,
              softWrap: false,
            ),
          ],
        ),
      ),
    );
    yield const SizedBox(height: 8);
  }
}
