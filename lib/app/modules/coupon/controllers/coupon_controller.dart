import 'package:efshop/app/models/voucher_res.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/extension.dart';
import 'package:get/get.dart';

class CouponController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  final data = <VoucherRes>[].obs;

  Iterable<VoucherRes> get sortedData {
    // sort by can_use and end_at
    data.sort((a, b) {
      if (a.isAvailable == b.isAvailable) {
        return a.endAt!.compareTo(b.endAt!);
      }
      return a.isAvailable ? -1 : 1;
    });
    return data;
  }

  CouponController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final it = await wabowProvider.getMembersMessagesVouchers();
      data.assignAll(it);
      // final test = VoucherRes.fromJson({
      //   "voucher_id": "1",
      //   "name": "APP 會員首購禮",
      //   "threshold": "全館滿額 1288",
      //   "title": "現金 88 元",
      //   "end_at": "2024-03-22T03:26:59.299Z",
      //   "can_use": true
      // });
      // data.add(test);
      data.sort((a, b) {
        if (a.isAvailable == b.isAvailable) {
          return a.endAt!.compareTo(b.endAt!);
        }
        return a.isAvailable ? -1 : 1;
      });
      if (data.isEmpty) {
        change('', status: RxStatus.empty());
      } else {
        change('', status: RxStatus.success());
      }
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
