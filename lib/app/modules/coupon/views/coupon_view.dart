import 'package:efshop/app/components/coupon_item.dart';
import 'package:efshop/app/components/ef_center.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/coupon_controller.dart';

class CouponView extends GetView<CouponController> {
  const CouponView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('會員好禮'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) {
          return SafeArea(
            child: Obx(() => _body()),
          );
        },
        onEmpty: _empty(),
        onError: (err) => ErrorButton(
          err,
          onTap: () {
            controller.onRefresh();
          },
        ),
      ),
    );
  }

  Widget _empty() {
    Iterable<Widget> children() sync* {
      yield SvgPicture.asset(
        'assets/images/big_star.svg',
        width: 50,
        height: 46,
        fit: BoxFit.contain,
      );
      yield const SizedBox(height: 20);
      yield const Text(
        '目前沒有優惠券',
        style: TextStyle(
          fontSize: 13,
          color: EfColors.gray93,
        ),
        softWrap: false,
      );
    }

    return EfCenter(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _body() {
    final it = controller.data;
    return ListView.separated(
      padding: EdgeInsets.symmetric(
        horizontal: 14.dw,
        vertical: 14.dh,
      ),
      itemBuilder: (context, index) {
        final element = it.elementAt(index);
        return CouponItem(
          element,
          onTap: () {
            // 特殊處理: 回到首頁的分類頁
            // Get.offAllNamed(
            //   Routes.INDEX,
            //   parameters: <String, String>{
            //     Keys.id: '${IndexTab.category.index}'
            //   },
            // );
            if (Get.canLaunchUrl(element.url?.uri ?? Uri())) {
              Get.launchUrl(element.url?.uri ?? Uri());
            }
          },
        );
      },
      separatorBuilder: (context, index) {
        if (index + 1 < it.length) {
          final pre = it.elementAt(index);
          final next = it.elementAt(index + 1);
          if (pre.isAvailable && !next.isAvailable) {
            return SizedBox(
              height: 78.dh,
              child: _separator(),
            );
          }
        }
        return SizedBox(height: 10.dh);
      },
      itemCount: it.length,
    );
  }

  Widget _separator() {
    Iterable<Widget> children() sync* {
      yield const Expanded(
        child: Divider(
          color: EfColors.grayD0,
        ),
      );
      yield SizedBox(width: 18.dw);
      yield const Text(
        '已失效',
        style: TextStyle(
          fontSize: 13,
          color: EfColors.gray94,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      );
      yield SizedBox(width: 18.dw);
      yield const Expanded(
        child: Divider(
          color: EfColors.grayD0,
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }
}
