import 'package:efshop/app/components/thumbnail_image.dart';
import 'package:efshop/app/models/products_collocation_res.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

class CollocationView extends StatelessWidget {
  final Iterable<ProductsCollocationRes> data;
  final ValueSetter<ProductsCollocationRes>? onTap;

  const CollocationView(
    this.data, {
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: _list(),
    );
  }

  Widget _list() {
    final it = data;
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      padding: EdgeInsets.symmetric(horizontal: 12.dw, vertical: 8.dh),
      itemBuilder: (context, index) {
        final element = it.elementAt(index);
        return SizedBox.square(
          dimension: 56.dw,
          child: ThumbnailImage(
            element.thumbnail,
            onTap: () => onTap?.call(element),
          ),
        );
      },
      separatorBuilder: (context, index) {
        return SizedBox(width: 12.dw);
      },
      itemCount: it.length,
    );
  }
}
