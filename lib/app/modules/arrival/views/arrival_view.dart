import 'package:efshop/app/components/ef_center.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/components/favorite_item.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../controllers/arrival_controller.dart';

class ArrivalView extends GetView<ArrivalController> {
  const ArrivalView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: _actions().toList(growable: false),
        title: const Text('貨到通知'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) => Obx(() => _body()),
        onEmpty: _empty(),
        onError: (error) {
          return ErrorButton(
            error,
            onTap: controller.onRefresh,
          );
        },
      ),
    );
  }

  Iterable<Widget> _actions() sync* {
    yield Obx(() {
      return Visibility(
        visible: controller.data.isNotEmpty,
        child: TextButton(
          onPressed: () async {
            final res = await Get.showConfirm('即將清空貨到通知', textConfirm: '清空');
            if (res == Button.confirm) {
              Get.showLoading();
              try {
                await controller.clearPreorder();
                Get.back();
              } catch (e) {
                Get.back();
                Get.showAlert(e.toString());
              }
            }
          },
          child: const Text(
            '清空',
            style: TextStyle(
              fontSize: 15,
              color: EfColors.gray98,
            ),
            softWrap: false,
          ),
        ),
      );
    });
  }

  Widget _body() {
    return ListView.separated(
      itemBuilder: (context, index) {
        final data = controller.data.elementAt(index);
        return FavoriteItem(
          data.toFavoriteData(),
          isLogin: controller.prefProvider.isLogin,
          onCartPressed: data.canAddToCart
              ? () async {
                  Get.showLoading();
                  try {
                    await controller.addToCart(data);
                    Get.back();
                    Get.showToast('已加入購物車');
                  } catch (e) {
                    Get.back();
                    Get.showAlert(e.toString());
                  }
                }
              : null,
          onRemovePressed: () async {
            final res = await Get.showConfirm('即將移除貨到通知', textConfirm: '移除');
            if (res == Button.confirm) {
              Get.showLoading();
              try {
                await controller.removePreorder(data);
                Get.back();
              } catch (e) {
                Get.back();
                Get.showAlert(e.toString());
              }
            }
          },
          onPressed: () {
            Get.toNamed(
              Routes.PRODUCT,
              parameters: <String, String>{
                Keys.id: '${data.productId}',
              },
            );
          },
        );
      },
      separatorBuilder: (context, index) {
        return const Divider(height: 1);
      },
      itemCount: controller.data.length,
    );
  }

  Widget _empty() {
    Iterable<Widget> children() sync* {
      yield SvgPicture.asset(
        'assets/images/empty_cloth.svg',
        width: 48.dw,
        height: 44.dh,
        fit: BoxFit.contain,
      );
      yield const SizedBox(height: 20);
      yield const Text(
        '目前沒有貨到通知',
        style: TextStyle(
          fontSize: 14,
          color: EfColors.gray,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      );
      yield const SizedBox(height: 20);
    }

    return EfCenter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }
}
