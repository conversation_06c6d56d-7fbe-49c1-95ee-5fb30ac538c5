import 'package:efshop/app/models/members_preorders_res.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:get/get.dart';

class ArrivalController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  final data = <MembersPreordersRes>[].obs;

  ArrivalController({required this.wabowProvider});
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final res = await wabowProvider.getMemberPreorders();
      final box = boxProvider.getGsBox(Boxes.preorder.name);
      box.erase();
      for (final item in res) {
        box.write('${item.id}', item.toJson());
      }
      _loadFromStorage();
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  // TODO: use iterable map
  Iterable<MembersPreordersRes> _getPreorders() sync* {
    final box = boxProvider.getGsBox(Boxes.preorder.name);
    for (var element in box.getValues()) {
      if (element is Map<String, dynamic>) {
        yield MembersPreordersRes.fromJson(element);
      }
    }
  }

  void _loadFromStorage() {
    data.assignAll(_getPreorders());
    change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
  }

  Future<MessageRes> clearPreorder() async {
    try {
      final box = boxProvider.getGsBox(Boxes.preorder.name);
      box.erase();
      return wabowProvider.deleteMemberPreorders();
    } finally {
      _loadFromStorage();
    }
  }

  Future<MessageRes> removePreorder(MembersPreordersRes element) {
    try {
      final box = boxProvider.getGsBox(Boxes.preorder.name);
      box.remove(element.id ?? '');
      return wabowProvider.deleteMemberPreorder(element.id ?? '');
    } finally {
      _loadFromStorage();
    }
  }

  Future<MessageRes> addToCart(MembersPreordersRes element) {
    final id = element.productId ?? '';
    if (id.isNotEmpty) {
      return wabowProvider.postCart(id, 1);
    }
    throw 'no data';
  }
}
