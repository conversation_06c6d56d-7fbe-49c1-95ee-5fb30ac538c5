import 'package:efshop/app/components/message_menu.dart';
import 'package:efshop/ef_colors.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/message_controller.dart';

class MessageView extends GetView<MessageController> {
  const MessageView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: EfColors.grayF6,
      appBar: AppBar(
        title: const Text('訊息'),
        centerTitle: true,
      ),
      body: controller.obx((state) {
        return SafeArea(
          child: Obx(() => _body()),
        );
      }),
    );
  }

  Widget _body() {
    final it = controller.data;
    return ListView.separated(
      padding: const EdgeInsets.only(
        top: 8,
        bottom: 8,
      ),
      itemCount: it.length,
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return MessageMenu.data(
          data,
          onTap: () {
            Get.toNamed(data.path ?? '');
          },
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 8);
      },
    );
  }
}
