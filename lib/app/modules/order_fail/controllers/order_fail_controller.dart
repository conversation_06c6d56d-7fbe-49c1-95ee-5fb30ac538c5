import 'dart:async';

import 'package:efshop/app/models/members_orders_res.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';

class OrderFailController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  final _disposable = Completer();
  final _orderId = ''.obs;
  String get orderId => _orderId.value;
  set orderId(String value) => _orderId.value = value;
  final _order = MembersOrdersRes().obs;
  MembersOrdersRes get order => _order.value;
  final _leading = false.obs;
  bool get leading => _leading.value;

  OrderFailController({
    required this.wabowProvider,
    String orderId = '',
  }) {
    if (orderId.isNotEmpty) {
      this.orderId = orderId;
    }
  }

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.orderId)) {
      orderId = Get.parameters[Keys.orderId] ?? '';
    }
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      _order.value = await wabowProvider.getMembersOrdersWithId(orderId);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
      _leading.value = true;
    }
  }

  Future<MessageRes> reorder() {
    return wabowProvider.postCartBuyAgain(orderId);
  }
}
