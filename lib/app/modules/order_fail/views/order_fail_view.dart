import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/order_fail_controller.dart';

class OrderFailView extends GetView<OrderFailController> {
  final String? tag;
  final AsyncValueSetter<IndexTab>? changeTab;

  OrderFailView({
    super.key,
    String orderId = '',
    this.changeTab,
  }) : tag = orderId {
    Get.lazyPut<OrderFailController>(
      () => OrderFailController(
        wabowProvider: Get.find(),
        orderId: orderId,
      ),
      tag: tag,
      fenix: true,
    );
  }
  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderFailController>(
      init: Get.find<OrderFailController>(tag: tag),
      tag: tag,
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('付款失敗'),
            centerTitle: true,
            leading: Obx(() {
              return Visibility(
                visible: controller.leading,
                child: BackButton(
                  onPressed: () {
                    changeTab?.call(IndexTab.category);
                  },
                ),
              );
            }),
          ),
          body: controller.obx(
            (state) {
              return _body();
            },
            onError: (error) {
              return ErrorButton(
                error,
                onTap: controller.onRefresh,
              );
            },
          ),
        );
      },
    );
  }

  Widget _body() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield _status();
    yield SizedBox(height: 10.dh);
    yield _info();
  }

  Widget _status() {
    Iterable<Widget> children() sync* {
      yield SizedBox(height: 30.dh);
      yield Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.dw),
        child: const Text(
          '親愛的顧客，\n請檢查信用卡資訊填寫是否正確，銀行回覆「交易失敗，請查詢發卡銀行」',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
            height: 1.8,
          ),
          textHeightBehavior:
              TextHeightBehavior(applyHeightToFirstAscent: false),
        ),
      );
      yield SizedBox(height: 30.dh);
      yield OutlinedButton(
        onPressed: () async {
          // 跳轉到會員頁面
          await changeTab?.call(IndexTab.profile);
          // 跳轉到訂單查詢 - 全部訂單
          await Get.toNamed(Routes.ORDERS, parameters: {
            Keys.id: '${OrderStatus.all.index}',
          });
          // 跳轉到訂單
          // await Get.toNamed(Routes.ORDER_DETAIL, parameters: {
          //   Keys.id: controller.orderId,
          // });
        },
        child: const Text(
          '查看訂單',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.gray,
          ),
          textAlign: TextAlign.center,
        ),
      );
      yield SizedBox(height: 30.dh);
    }

    return ColoredBox(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _info() {
    Iterable<Widget> children() sync* {
      yield SizedBox(height: 30.dh);
      yield Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(horizontal: 20.dw),
        child: const Text(
          '此訂單將視為無效（已取消訂單），\n將不會進行扣款及出貨。\n我們仍將您的商品保留在購物車喔！',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.primary,
            height: 1.8,
          ),
          textHeightBehavior:
              TextHeightBehavior(applyHeightToFirstAscent: false),
        ),
      );
      yield SizedBox(height: 20.dh);
      yield const Text(
        '請點選',
        style: TextStyle(
          fontSize: 12,
          color: EfColors.gray,
          height: 2,
        ),
        textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        textAlign: TextAlign.center,
      );
      yield SizedBox(height: 10.dh);
      yield OutlinedButton(
        style: OutlinedButton.styleFrom(
          side: const BorderSide(
            color: EfColors.primary,
            width: 1,
          ),
        ),
        onPressed: _reorder,
        child: const Text(
          '重新付款',
          style: TextStyle(
            fontSize: 13,
            color: EfColors.primary,
          ),
          textAlign: TextAlign.center,
        ),
      );
      yield SizedBox(height: 30.dh);
    }

    return ColoredBox(
      color: Colors.white,
      child: SizedBox(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: children().toList(growable: false),
        ),
      ),
    );
  }

  Future<void> _reorder() async {
    Get.showLoading();
    try {
      await controller.reorder();
      Get.back();
      // 跳轉到購物車
      await changeTab?.call(IndexTab.cart);
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString()).then((value) {
        if (e.toString().contains('登入')) {
          changeTab?.call(IndexTab.category);
        }
      });
    }
  }
}
