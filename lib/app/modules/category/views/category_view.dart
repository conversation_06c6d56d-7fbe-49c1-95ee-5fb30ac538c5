import 'package:efshop/app/modules/ef_grid/views/ef_grid_view.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/category_controller.dart';

class CategoryView extends GetView<CategoryController> {
  final String? tag;

  CategoryView({
    super.key,
  }) : tag = '${DateTime.now().hashCode}' {
    Get.lazyPut(
      () => CategoryController(
        wabowProvider: Get.find(),
      ),
      tag: tag,
      fenix: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return controller.obx(
      (state) {
        return Scaffold(
          appBar: AppBar(
            title: Text(controller.title),
            centerTitle: true,
            bottom: _preferredSizeWidget(),
          ),
          body: _body(),
        );
      },
      onLoading: Scaffold(
        appBar: AppBar(),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
      onError: _error,
    );
  }

  Widget _error(String? error) {
    Future.microtask(() {
      Get.offNamed(Routes.NOT_FOUND);
    });
    return const SizedBox();
  }

  PreferredSizeWidget? _preferredSizeWidget() {
    if (controller.showTabBar) {
      return PreferredSize(
        preferredSize: const Size.fromHeight(Constants.tabHeight),
        child: Align(
          alignment: Alignment.centerLeft,
          child: _tabBar(),
        ),
      );
    }
    return null;
  }

  Widget _tabBar() {
    return Align(
      alignment: Alignment.centerLeft,
      child: TabBar(
        controller: controller.tabController,
        isScrollable: true,
        tabs: _tabs().toList(growable: false),
        padding: EdgeInsets.zero,
        labelPadding: EdgeInsets.zero,
        onTap: (value) {
          try {
            controller.pageController.jumpToPage(value);
          } catch (e) {
            controller.logger.e(e);
          }
        },
      ),
    );
  }

  Iterable<Widget> _tabs() {
    return controller.tabs.map((e) {
      return Tab(
        iconMargin: const EdgeInsets.only(
          bottom: 2,
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minWidth: 74.dw,
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.dw),
            child: Text(
              e.name ?? '',
              softWrap: false,
              textAlign: TextAlign.center,
              overflow: TextOverflow.visible,
            ),
          ),
        ),
      );
    });
  }

  Widget _body() {
    final it = controller.tabs;
    if (it.isEmpty) {
      return EfGridView(
        url: controller.url,
      );
    }
    return PageView.builder(
      controller: controller.pageController,
      itemCount: it.length,
      onPageChanged: (value) {
        try {
          controller.tabController?.animateTo(value);
        } catch (e) {
          controller.logger.e(e);
        }
      },
      itemBuilder: (BuildContext context, int index) {
        controller.talker.debug('[CategoryView] itemBuilder index: $index');
        final category = it.elementAt(index);
        controller.sendTypeViewedEvent(category);
        return EfGridView(
          url: category.url,
          category: category,
        );
      },
    );
  }
}
