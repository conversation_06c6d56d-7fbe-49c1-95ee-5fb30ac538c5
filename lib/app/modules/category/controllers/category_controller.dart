import 'dart:async';

import 'package:efshop/app/models/appier_type_viewed_req.dart';
import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/models/url.dart';
import 'package:efshop/app/providers/appier_provider.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:talker_flutter/talker_flutter.dart';

class CategoryController extends GetxController
    with StateMixin<String>, GetSingleTickerProviderStateMixin {
  final _disposable = Completer();
  final WabowProvider wabowProvider;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  AppierProvider get appierProvider => wabowProvider.appierProvider;
  Logger get logger => wabowProvider.logger;
  Talker get talker => wabowProvider.talker;
  String? get id => _url.value.id;

  final _pageController = Rx<PageController?>(null);
  PageController get pageController {
    final currentIndex = tabs.isEmpty ? 0 : (tabController?.index ?? 0);
    _pageController.value ??= PageController(
      keepPage: false,
      viewportFraction: 1,
      initialPage: currentIndex,
    );
    final res = _pageController.value!;
    if (res.initialPage != currentIndex) {
      _safeJumpToPage(currentIndex);
    }
    return res;
  }

  final _tabController = Rx<TabController?>(null);
  TabController? get tabController {
    if (_tabController.value == null) {
      final ls = List.from(tabs, growable: false);
      final tabCount = ls.isEmpty ? 1 : ls.length;
      final index =
          ls.indexWhere((element) => element.id == id || element.url?.id == id);
      _tabController.value = TabController(
        vsync: this,
        length: tabCount,
        initialIndex: ls.isEmpty ? 0 : index.clamp(0, ls.length - 1),
      );
    }
    return _tabController.value;
  }

  final _parentId = ''.obs;
  String get parentId => _parentId.value;

  String get title => parentTab.name ?? '';
  bool get showTabBar => parentId.isNotEmpty && tabs.isNotEmpty;

  final _parentTab = Category().obs;
  Category get parentTab => _parentTab.value;
  Iterable<Category> get tabs => parentTab.subCategories ?? [];
  Category get currentTab => tabs.isEmpty ? Category() : tabs.elementAt(tabController?.index ?? 0);
  // url
  final _url = Url().obs;
  Url get url => _url.value;

  CategoryController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _fetchParameters();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    _tabController.value?.dispose();
    _pageController.value?.dispose();
    super.onClose();
  }

  void _fetchParameters() {
    // id
    if (Get.parameters.containsKey(Keys.id)) {
      _url.value.id = Get.parameters[Keys.id];
    }
    // parent id
    if (Get.parameters.containsKey(Keys.parentId)) {
      _parentId.value = Get.parameters[Keys.parentId] ?? '';
    }
    // action
    if (Get.parameters.containsKey(Keys.action)) {
      _url.value.action = Get.parameters[Keys.action];
    }
    // category
    if (Get.parameters.containsKey(Keys.category)) {
      _url.value.category = Get.parameters[Keys.category];
    }
    // page
    if (Get.parameters.containsKey(Keys.page)) {
      _url.value.page = Get.parameters[Keys.page];
    }
  }

  Future<void> _fetchPromotionTabs() async {
    final res = await wabowProvider.getPromotionsLimitedNMGiftMix();
    parentTab.subCategories = res.where((element) => element.isVisible).toList();
    _parentId.value = 'null';
  }

  Future<void> onRefresh() async {
    talker.debug('[CategoryController] onRefresh');
    try {
      final box = boxProvider.getGsBox(Boxes.category.name);
      final keys = Iterable.castFrom(box.getKeys());
      if (keys.isEmpty) {
        await wabowProvider.getCategoriesIndex();
      }
      final json = box.read(parentId);
      if (json != null) {
        _parentTab.value = Category.fromJson(json);
      }
      // special case: promotion
      if (url.action == Keys.promotion) {
        await _fetchPromotionTabs();
      }
      
      // Recreate TabController when tabs change
      _tabController.value?.dispose();
      _tabController.value = null;
      
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  /// Safely jump to specified page to avoid "Bad state: No element" error
  void _safeJumpToPage(int index) {
    final controller = _pageController.value;
    if (controller == null || tabs.isEmpty) return;
    
    // Use addPostFrameCallback to ensure execution after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // Check if PageController has valid position
        if (controller.hasClients && controller.positions.isNotEmpty) {
          final safeIndex = index.clamp(0, tabs.length - 1);
          controller.jumpToPage(safeIndex);
        } else {
          // If no clients yet, retry with delay
          Future.delayed(const Duration(milliseconds: 100), () {
            _safeJumpToPage(index);
          });
        }
      } catch (e, s) {
        talker.error('Failed to jump to page $index: $e', e, s);
      }
    });
  }

  ///
  /// appier event: type viewed
  ///
  Future<void> sendTypeViewedEvent(Category value) async {
    try {
      await appierProvider.typeViewed(AppierTypeViewedReq(
        typeName: value.name,
      ));
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
