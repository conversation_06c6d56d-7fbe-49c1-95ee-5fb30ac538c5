import 'package:efshop/app/modules/password_input/bindings/password_input_binding.dart';
import 'package:efshop/app/modules/password_input/views/password_input_view.dart';
import 'package:efshop/app/modules/sign_in/bindings/sign_in_binding.dart';
import 'package:efshop/app/modules/sign_in/views/sign_in_view.dart';
import 'package:efshop/app/modules/sign_up/bindings/sign_up_binding.dart';
import 'package:efshop/app/modules/sign_up/views/sign_up_view.dart';
import 'package:efshop/app/modules/sign_up_mid/bindings/sign_up_mid_binding.dart';
import 'package:efshop/app/modules/sign_up_mid/views/sign_up_mid_view.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/enums.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

import '../../sign_up_uid/bindings/sign_up_uid_binding.dart';
import '../../sign_up_uid/views/sign_up_uid_view.dart';

class SubRouter extends StatelessWidget {
  const SubRouter({super.key});

  @override
  Widget build(BuildContext context) {
    return Navigator(
      key: Get.nestedKey(SubRouteType.signIn.index),
      initialRoute: Routes.SIGN_IN,
      onGenerateRoute: (settings) {
        Get.parameters = settings.arguments as Map<String, String>? ?? {};
        if (Routes.PASSWORD_INPUT == settings.name) {
          return GetPageRoute(
            settings: settings,
            page: () => const PasswordInputView(),
            binding: PasswordInputBinding(),
          );
        }
        if (Routes.SIGN_UP == settings.name) {
          return GetPageRoute(
            settings: settings,
            page: () => const SignUpView(),
            binding: SignUpBinding(),
          );
        }
        if (Routes.SIGN_UP_MID == settings.name) {
          return GetPageRoute(
            settings: settings,
            page: () => const SignUpMidView(),
            binding: SignUpMidBinding(),
          );
        }
        if (Routes.SIGN_UP_UID == settings.name) {
          return GetPageRoute(
            settings: settings,
            page: () => const SignUpUidView(),
            binding: SignUpUidBinding(),
          );
        }
        return GetPageRoute(
          settings: settings,
          page: () => SignInView(),
          binding: SignInBinding(),
        );
      },
    );
  }
}
