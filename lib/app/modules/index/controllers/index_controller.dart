import 'dart:async';
import 'dart:convert';

import 'package:app_links/app_links.dart';
import 'package:appier_flutter/appier_flutter.dart';
import 'package:efshop/app/models/appier_auth_code_req.dart';
import 'package:efshop/app/models/jwt_res.dart';
import 'package:efshop/app/models/login_req.dart';
import 'package:efshop/app/models/login_res.dart';
import 'package:efshop/app/models/members_messages_unread_res.dart';
import 'package:efshop/app/models/members_my_favorite_post_req.dart';
import 'package:efshop/app/models/members_popup_res.dart';
import 'package:efshop/app/models/notification_message_data.dart';
import 'package:efshop/app/models/url.dart';
import 'package:efshop/app/providers/api_provider.dart';
import 'package:efshop/app/providers/appier_provider.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/notification_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class IndexController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final WabowProvider wabowProvider;
  ApiProvider get apiProvider => wabowProvider.apiProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  final AppierProvider appierProvider;
  final _currentTab = IndexTab.home.obs;
  IndexTab get currentTab => _currentTab.value;
  set currentTab(IndexTab value) {
    wabowProvider.fetchCartQuantity();
    Future(() => _currentTab.value = value);
  }

  PrefProvider get prefProvider => wabowProvider.prefProvider;
  Logger get logger => wabowProvider.logger;
  Talker get talker => wabowProvider.talker;
  // pop up
  final membersPopupRes = Completer<MembersPopupRes>();
  // 取得初始 url
  static var _initialUriIsHandled = false;
  // page controller
  final _pageController = Rx<PageController?>(null);
  PageController get pageController {
    final currentIndex = currentTab.index;
    _pageController.value ??= PageController(
      keepPage: false,
      viewportFraction: 1,
      initialPage: currentIndex,
    );
    final res = _pageController.value!;
    if (res.initialPage != currentIndex) {
      _safeJumpToPage(currentIndex);
    }
    return res;
  }

  /// 安全地跳轉到指定頁面，避免 "Bad state: No element" 錯誤
  void _safeJumpToPage(int index) {
    final controller = _pageController.value;
    if (controller == null) return;

    // 使用 addPostFrameCallback 確保在 widget 構建完成後執行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // 檢查 PageController 是否有有效的 position
        if (controller.hasClients && controller.positions.isNotEmpty) {
          controller.jumpToPage(index);
        } else {
          // 如果還沒有 clients，延遲重試
          Future.delayed(const Duration(milliseconds: 100), () {
            _safeJumpToPage(index);
          });
        }
      } catch (e, s) {
        talker.error('Failed to jump to page $index: $e', e, s);
      }
    });
  }

  IndexController({
    required this.wabowProvider,
    required this.appierProvider,
  });

  @override
  void onInit() {
    super.onInit();
    talker.info('IndexController onInit');
    _initObservable();
    // _initUniLinks();
    _initAppLinks();
    _fetchParameters();
    prefProvider.printInfo();
    prefProvider.tokenStream
        .distinct()
        .asyncMap((event) async {
          talker.info('stream - token: event');
          var jwt = JwtRes();
          if (event.isEmpty) {
            // currentTab = IndexTab.home;
            boxProvider.namespace = '';
          } else {
            jwt = LoginRes(token: event).jwtRes ?? JwtRes();
            // currentTab = IndexTab.profile;
            talker.info(jwt);
            boxProvider.namespace = jwt.userid ?? '';
          }
          for (var box in Boxes.values) {
            await boxProvider.initGsBox(box.name);
          }
          if (prefProvider.isLogin) {
            await _postCartFromLocalStorage();
            await _postFavoriteFromLocalStorage();
            await _fetchMembersPopup();
          }
          await wabowProvider.fetchCartQuantity();
          await _fetchUnread();
          // 設定 Appier
          await _setupAppier(jwt);
          // GA: log user
          await _logUser(jwt);
          // firebase crashlytics user
          await FirebaseCrashlytics.instance
              .setUserIdentifier(jwt.userid ?? '');
          // 取得 configs
          await _fetchConfigs();
          await _checkNewVersion();
          await _postDeviceToken();
        })
        .takeUntil(_disposable.future)
        .listen((event) {
          //
        }, onError: (e, s) {
          talker.error(e.toString(), e, s);
        });
  }

  Future<void> _postFavoriteFromLocalStorage() async {
    final box = boxProvider.getGsBox(Boxes.favorite.name, withNamespace: false);
    final map = Map.fromIterables(box.getKeys(), box.getValues());
    await box.erase();
    for (final entry in map.entries) {
      try {
        final number = entry.key as String;
        final color = entry.value as String;
        await wabowProvider.postMembersMyFavorite(MembersMyFavoritePostReq(
          number: number,
          color: color,
        ));
      } catch (e) {
        logger.e(e.toString());
      }
    }
  }

  Future<void> _postCartFromLocalStorage() async {
    final box = boxProvider.getGsBox(Boxes.cart.name, withNamespace: false);
    final map = Map.fromIterables(box.getKeys(), box.getValues());
    await box.erase();
    for (final entry in map.entries) {
      try {
        final quantity = entry.value;
        if (quantity != 0) {
          await wabowProvider.postCart(entry.key as String, quantity as int);
        }
      } catch (e) {
        logger.e(e.toString());
      }
    }
  }

  Future<void> putPopup(num id) async {
    try {
      await wabowProvider.putMembersPopup(id);
    } catch (e) {
      logger.e(e.toString());
    }
  }

  // 取得 pop up
  Future<void> _fetchMembersPopup() async {
    try {
      final res = await wabowProvider.getMembersPopup();
      membersPopupRes.complete(res);
    } catch (e) {
      logger.e(e.toString());
    }
  }

  Future<void> _postDeviceToken() async {
    try {
      final deviceType = Constants.deviceType;
      final deviceId = prefProvider.deviceId;
      await wabowProvider.postDevicesIdCreate(deviceType, deviceId);
    } catch (e) {
      logger.e(e.toString());
    }
  }

  Future<void> _fetchConfigs() async {
    try {
      prefProvider.configs = await wabowProvider.getConfigs();
    } catch (e) {
      logger.e(e.toString());
    }
  }

  Future<void> _checkNewVersion() async {
    if (prefProvider.hasNewerVersion) {
      final res = await Get.showConfirm(
        '新版本提醒 ${prefProvider.appVersionRequires}',
        textConfirm: '立即更新',
        textCancel: '下次再說',
      );
      if (res == Button.confirm) {
        if (await canLaunchUrl(Constants.uriStore)) {
          await launchUrl(Constants.uriStore);
        }
      }
    }
  }

  ///
  /// GA: log user
  ///
  Future<void> _logUser(JwtRes? jwt) async {
    try {
      // await FirebaseAnalytics.instance.logLogin();
      await FirebaseAnalytics.instance.setUserId(id: jwt?.userid);
      await FirebaseAnalytics.instance.setUserProperty(
        name: jwt?.fullname ?? '',
        value: jwt?.fullname,
      );
    } catch (e) {
      logger.e(e.toString());
    }
  }

  Future<void> _setupAppier(JwtRes? jwt) async {
    await AppierFlutter.setUserId(jwt?.userid ?? '');
    await AppierFlutter.setName(jwt?.fullname ?? '');
    // await AppierFlutter.setFirstName(String firstName)
    // await AppierFlutter.setLastName(String lastName)
    // await AppierFlutter.setCity(String city)
    // await AppierFlutter.setPhoneNumber(String phoneNo)
    await AppierFlutter.setEmail(jwt?.email ?? '');
    final birthday = jwt?.birthdayAsDateTime;
    if (birthday != null) {
      await AppierFlutter.setDayOfBirth(birthday.day);
      await AppierFlutter.setMonthOfBirth(birthday.month);
      await AppierFlutter.setYearOfBirth(birthday.year);
    }
    try {
      prefProvider.appierAuthCode =
          await appierProvider.getAuthCode(AppierAuthCodeReq(
        crmId: jwt?.userid ?? '',
        pageId: Constants.appierWebChatBotPageId,
        crmName: jwt?.fullname ?? '',
      ));
    } on Exception catch (e) {
      prefProvider.appierAuthCode = '';
      logger.e(e.toString());
    }
  }

  void _fetchParameters() {
    if (Get.parameters.containsKey(Keys.id)) {
      final index = int.tryParse(Get.parameters[Keys.id] ?? '') ?? 0;
      // clamp
      // final clampedIndex = index.clamp(0, IndexTab.values.length - 1);
      // 延遲執行
      Future(() {
        // prevent out of range
        if (index >= 0 && index < IndexTab.values.length) {
          currentTab = IndexTab.values.elementAt(index);
        } else {
          currentTab = IndexTab.home;
        }
      });
      // change('', status: RxStatus.loading());
      // Future(() {
      //   currentTab = IndexTab.values.elementAt(index);
      //   change('', status: RxStatus.success());
      // });
    }
  }

  @override
  void onReady() {
    super.onReady();
    prefProvider.loginRes = prefProvider.loginRes;
    _checkToken();
    onRefresh().then((value) {
      _currentTab.stream.distinct().takeUntil(_disposable.future).listen(
          (event) {
        talker.info('currentTab: $event');
        _safeJumpToPage(event.index);
      }, onError: (e, s) {
        talker.error(e.toString(), e, s);
      });
    });
  }

  @override
  void onClose() {
    _pageController.value?.dispose();
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      if (!_initialUriIsHandled) {
        _initialUriIsHandled = true;
        await _getInitialLink();
      }
    } catch (e, s) {
      talker.error(e.toString(), e, s);
    }
    try {
      // 取得分類廣告
      await _fetchAdvsCategories();
      await _setupInteractedMessage();
      // dynamic links (for auth)
      // await _initDynamicLinks();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  ///
  /// 取得分類廣告
  ///
  Future<void> _fetchAdvsCategories() async {
    try {
      final res = await wabowProvider.getAdvsCategories();
      logger.i(res);
      // save to local storage
      final box =
          boxProvider.getGsBox(Boxes.advsCategories.name, withNamespace: false);
      for (final item in res) {
        box.write(item.categoryId ?? '', item.toJson());
      }
    } catch (e, s) {
      talker.error(e.toString(), e, s);
    }
  }

  Future<void> _fetchUnread() async {
    try {
      await wabowProvider.getMembersMessagesUnread();
    } catch (e) {
      logger.i(e);
      prefProvider.unreadRes = MembersMessagesUnreadRes();
    }
  }

  void loginTest() {
    final req = LoginReq(email: "<EMAIL>", password: "vufWCR4V");
    wabowProvider.login(req).then((value) {
      logger.i(value);
      final jwt = value.jwtRes;
      // isExpired: false
      logger.i('isExpired: ${value.isExpired}');
      // expiresAt: 2023-07-04 11:33:53.000
      logger.i('expiresAt: ${value.expirationDate}');
      // tokenTime: 0:00:00.308150
      logger.i('tokenTime: ${value.tokenTime}');
      // remainingTime: 719:59:59.690967
      logger.i('remainingTime: ${value.remainingTime}');
      logger.i('$jwt');
      wabowProvider.logout().then((value) {
        logger.i(value);
      });
    });
  }

  void _initObservable() {
    prefProvider.tokenStream
        .distinct()
        .takeUntil(_disposable.future)
        .listen((event) {
      if (event.isEmpty) {
        currentTab = IndexTab.home;
      }
    });
    // _currentTab.stream.distinct().takeUntil(_disposable.future).listen((event) {
    //   talker.info('currentTab: $event');
    //   pageController.jumpToPage(event.index);
    // }, onError: (e, s) {
    //   talker.error(e.toString(), e, s);
    // });
    // _observeNotFound();
  }

  void _observeNotFound() {
    apiProvider.httpClient.options.validateStatus = (status) {
      if (status == 404) {
        talker.warning('🔍 404 Not Found - 導航到 not_found 頁面');

        // 使用 Future.microtask 確保在當前執行堆疊完成後執行導航
        Future.microtask(() {
          if (Get.currentRoute != Routes.NOT_FOUND) {
            Get.toNamed(Routes.NOT_FOUND);
          }
        });

        // 返回 false 讓 Dio 將此視為錯誤，但我們已經處理了導航
        return false;
      }
      return status != null && status < 500;
    };
  }

  Future<void> _setupInteractedMessage() async {
    try {
      // Get any messages which caused the application to open from
      // a terminated state.
      // 用於獲取當應用程式啟動時由 Firebase Cloud Messaging (FCM) 傳送的初始訊息。
      // 當你的應用程式在背景或關閉狀態時收到一個 FCM 推送通知，
      // 並且用戶點擊該通知來啟動應用程式，這個方法就會返回該通知的數據。
      // 如果應用程式是由用戶直接啟動（即並非由點擊 FCM 推送通知啟動），則此方法將返回 null。
      final initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();
      // If the message also contains a data property with a "type" of "chat",
      // navigate to a chat screen
      if (initialMessage != null) {
        final jsonString = jsonEncode(initialMessage.toMap());
        talker.info('initialMessage: $jsonString');
        // _handleMessage(initialMessage);
        // test code
        // final data = NotificationMessageData.fromJson(Constants.notificationData);
        final data = NotificationMessageData.fromJson(initialMessage.data);
        // 檢查是否為 Url 格式 (這邊是自定義的 Url 類別)
        final url = Url.fromRawJson(data.parameters ?? '');
        await _onAppLinks(url.uri);
      } else {
        talker.info('initialMessage: null');
      }
    } catch (e) {
      logger.e(e.toString());
    }

    // Also handle any interaction when the app is in the background via a
    // Stream listener
    // 它會在應用程式在背景運行時收到 Firebase Cloud Messaging (FCM) 推送通知，
    // 並且用戶點擊該通知將應用程式帶到前台時被觸發。
    FirebaseMessaging.onMessageOpenedApp
        .takeUntil(_disposable.future)
        .listen((message) {
      talker.info('onMessageOpenedApp: $message');
      _handleMessage(message);
    });
    // 它會在應用程式在前台運行時收到 Firebase Cloud Messaging (FCM) 推送通知時被觸發。
    FirebaseMessaging.onMessage.takeUntil(_disposable.future).listen((message) {
      talker.info('onMessage: $message');
      _handleMessage(message, false);
    });
  }

  // {body=衣芙 客服中心回覆 (訂單編號47582251), icon=myicon, sound=, title=衣芙 客服中心回覆 (訂單編號47582251), parameters={"type":"questions"}}
  // {body=測試推播, icon=myicon, title=測試推播, parameters={"action":"product","id":"70481","type":"activities"}}
  // data: {qgPush: {type: basic}, rno: 2, source: QG, nid: 19138650000, deepLink: efshop://m.efshop.com.tw/category/324, qg: 1913865}
  // dataString: {"qgPush":{"type":"basic"},"rno":"2","source":"QG","nid":"19138650000","deepLink":"efshop://m.efshop.com.tw/category/324","qg":"1913865"}
  Future<void> _handleMessage(
    RemoteMessage message, [
    bool deepLink = true,
  ]) async {
    final jsonString = jsonEncode(message.toMap());
    talker.info('message: $jsonString');
    final from = message.from;
    // from: 498175649962
    talker.info('from: $from');
    final data = message.data;
    // Android
    // data: {message: {"bgColor":"#ffffff","blackout":{"end":"2300","start":"1700"},"channelId":"po","deepLink":"https://m.efshop.com.tw/product/95985","expiration_time":1720601584,"headsUp":false,"message":"測試測試測試\n走過路過不要錯過","notificationId":19138540000,"resize_image":false,"rno":55,"source":"QG","textColor":"#000000","title":"20240529測試推播","type":"basic"}}
    // data: {icon: myicon, body: 訂購完成通知： (訂單編號 {74616161}), title: 訂購完成通知： (訂單編號 {74616161}), parameters: []}
    talker.info('data: $data');
    final dataString = jsonEncode(data);
    talker.info('dataString: $dataString');
    // for testing
    // await _logDataString(dataString);
    // dataString: {"icon":"myicon","body":"訂購完成通知： (訂單編號 {74616161})","title":"訂購完成通知： (訂單編號 {74616161})","parameters":"[]"}
    final messageMap =
        data.containsKey(Keys.message) ? jsonDecode(data[Keys.message]) : {};
    final isAndroidAppierPush =
        messageMap.containsKey(Keys.source) && messageMap[Keys.source] == 'QG';
    final isIosAppierPush = data.containsKey('qg');
    final isAppierPush = isAndroidAppierPush || isIosAppierPush;
    // final isAppierPush = await AppierFlutter.isAppierPush(dataString);
    talker.info('isAppierPush: $isAppierPush');
    if (isAppierPush) {
      try {
        await AppierFlutter.handleRemoteMessage(dataString);
        // final an = AppierNotification.fromRawJson(dataString);
        // final deepLinks = an.deepLinks;
        // if (deepLinks.isNotEmpty) {
        //   final uri = Uri.parse(deepLinks.first);
        //   await _onAppLinks(uri);
        // }
      } catch (e) {
        logger.e(e.toString());
      }
    } else {
      // final type = message.data['type'] ?? '';
      // final String id = message.data['id'] ?? '';
      // final url = id.isNotEmpty ? '/$type/$id' : '/$type';
      // final uri = Uri.parse(url);
      // sample code
      // if (type == 'chat') {
      //   Navigator.pushNamed(
      //     context,
      //     '/chat',
      //     arguments: ChatArguments(message),
      //   );
      // }
      try {
        final data = NotificationMessageData.fromJson(message.data);
        // log title
        talker.info('title: ${data.title}');
        // log body
        talker.info('body: ${data.body}');
        final notificationProvider = Get.find<NotificationProvider>();
        await notificationProvider.showNotification(
          title: data.title ?? '',
          body: data.body ?? '',
          payload: data.parameters ?? '',
        );
      } catch (e, s) {
        talker.error(e.toString(), e, s);
      }
    }
  }

  // Future<void> _logDataString(String dataString) async {
  //   try {
  //     talker.info('dataString: $dataString');
  //     await FirebaseCrashlytics.instance.log('dataString: $dataString');
  //     await FirebaseCrashlytics.instance
  //         .recordError('dataString: $dataString', null);
  //   } catch (e) {
  //     logger.e(e.toString());
  //   }
  // }

  Future<void> _onAppLinks(Uri uri) async {
    talker.info('allUriLinkStream1: $uri');
    // Do something (navigation, ...)
    // log path
    talker.info('allUriLinkStream2: ${uri.path}');
    talker.info('allUriLinkStream3: ${uri.queryParameters}');
    Get.popAll();
    await Get.launchUrl(uri);
  }

  // final _api = ExampleHostApi();

  Future<void> _getInitialLink() async {
    // try {
    //   final uri = await _api.getInitialLink();
    //   _onAppLinks(Uri.parse(uri));
    // } catch (e) {
    //   logger.e(e.toString());
    // }
    try {
      final uri = await _appLinks.getInitialLink();
      if (uri != null) {
        talker.info('getInitialLink: $uri');
        await _onAppLinks(uri);
      } else {
        talker.info('getInitialLink: null');
      }
    } catch (e, s) {
      talker.error(e.toString(), e, s);
    }
    // ... check initialUri
    // Platform messages may fail, so we use a try/catch PlatformException.
    // try {
    //   final uri = await getInitialUri();
    //   if (uri != null) {
    //     logger.i('initialUri: $uri');
    //     _onAppLinks(uri);
    //   }
    // } catch (e) {
    //   logger.e(e.toString());
    // }
  }

  // void _initUniLinks() {
  //   // Attach a listener to the stream
  //   uriLinkStream.takeUntil(_disposable.future).listen((Uri? uri) {
  //     // Use the uri and warn the user, if it is not correct
  //     if (uri != null) {
  //       logger.i('uriLinkStream: $uri');
  //       _onAppLinks(uri);
  //     }
  //   }, onError: (err) {
  //     // Handle exception by warning the user their action did not succeed
  //     logger.e('uriLinkStream: $err');
  //   });
  //   // NOTE: Don't forget to call _sub.cancel() in dispose()
  // }

  final _appLinks = AppLinks(); // AppLinks is singleton

  void _initAppLinks() {
    // Subscribe to all events (initial link and further)
    _appLinks.uriLinkStream.listen(_onAppLinks);
  }

  // Future<void> _initDynamicLinks() async {
  //   FirebaseDynamicLinks.instance.onLink
  //       .listen((PendingDynamicLinkData? dynamicLink) async {
  //     final deepLink = dynamicLink?.link;
  //     if (deepLink != null) {
  //       await _handleDeepLink(deepLink);
  //     }
  //   }).onError((dynamic e) {
  //     // _authBloc.add(
  //     //   AuthFailedEvent(
  //     //     exception: e as Exception,
  //     //     message: 'Auth redirect failed!',
  //     //   ),
  //     // );
  //   });
  //   final data = await FirebaseDynamicLinks.instance.getInitialLink();
  //   final deepLink = data?.link;
  //   if (deepLink != null) {
  //     await _handleDeepLink(deepLink);
  //   }
  // }

  // Future<void> _handleDeepLink(Uri link) async {
  //   final params = link.queryParameters;
  //   if (params.containsKey('token')) {
  //     _authBloc.add(AuthSignInEvent(token: params['token']!));
  //   } else if (params.containsKey('code')) {
  //     _authBloc.add(
  //       AuthFailedEvent(
  //         message: "Authentication failed! Code: ${params['code']!}",
  //       ),
  //     );
  //   }
  // }

  /// 檢查 token 是否過期 (每次進入首頁都會檢查)
  /// 剩餘 14 天以內，更新 token
  Future<void> _checkToken() async {
    // 有登入才需要檢查
    if (prefProvider.loginRes != null) {
      final loginRes = prefProvider.loginRes!;
      if (loginRes.isExpired) {
        // 移除 token
        prefProvider.loginRes = null;
      } else {
        // 14 天以內，更新 token
        final needToUpdate = DateTime.now().add(const Duration(days: 14));
        final date = loginRes.expirationDate;
        if (date.isBefore(needToUpdate)) {
          try {
            prefProvider.loginRes = await wabowProvider.renew();
          } catch (e, s) {
            talker.error(e.toString(), e, s);
          }
        }
      }
    }
  }
}
