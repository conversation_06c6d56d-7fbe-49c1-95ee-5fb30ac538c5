import 'package:efshop/app/models/notification_message_data.dart';
import 'package:efshop/app/providers/notification_provider.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../controllers/devtool_controller.dart';

class DevtoolView extends GetView<DevtoolController> {
  const DevtoolView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Devtool'),
        centerTitle: true,
        actions: _actions(context).toList(growable: false),
      ),
      body: controller.obx((state) {
        // return Obx(() => _body());
        return _body();
      }),
    );
  }

  Iterable<Widget> _actions(BuildContext context) sync* {
    yield IconButton(
      onPressed: () {
        // 開啟 logger
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => TalkerScreen(talker: Get.find()),
          ),
        );
      },
      icon: const Icon(Icons.logo_dev),
    );
  }

  Widget _body() {
    return ListView(
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    // bundle id
    yield ListTile(
        leading: const Text('bundle id'),
        title: Text(
          controller.prefProvider.packageInfo.packageName,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: IconButton(
          icon: const Icon(Icons.copy),
          onPressed: () async {
            // 複製 bundle id 到剪貼簿
            await Clipboard.setData(ClipboardData(
                text: controller.prefProvider.packageInfo.packageName));
            await Get.showToast('已複製到剪貼簿');
          },
        ));
    // version
    yield ListTile(
      leading: const Text('version'),
      title: Text(
        controller.prefProvider.packageInfo.version,
        overflow: TextOverflow.ellipsis,
      ),
    );
    // build number
    yield ListTile(
      leading: const Text('build number'),
      title: Text(
        controller.prefProvider.packageInfo.buildNumber,
        overflow: TextOverflow.ellipsis,
      ),
    );
    // fcm token
    yield ListTile(
      leading: const Text('fcm token'),
      title: Text(
        controller.prefProvider.fcmToken,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: IconButton(
        icon: const Icon(Icons.copy),
        onPressed: () async {
          // 複製 fcm token 到剪貼簿
          await Clipboard.setData(
              ClipboardData(text: controller.prefProvider.fcmToken));
          // Get.showSnackbar(GetSnackBar(
          //   message: '已複製到剪貼簿',
          //   duration: 1.seconds,
          // ));
          await Get.showToast('已複製到剪貼簿');
        },
      ),
    );
    // apns token
    yield ListTile(
      leading: const Text('apns token'),
      title: Text(
        controller.prefProvider.apnsToken,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: IconButton(
        icon: const Icon(Icons.copy),
        onPressed: () async {
          // 複製 apns token 到剪貼簿
          await Clipboard.setData(
              ClipboardData(text: controller.prefProvider.apnsToken));
          await Get.showToast('已複製到剪貼簿');
        },
      ),
    );
    // 模擬收到通知
    yield ListTile(
      leading: const Text('推播'),
      title: const Text('點擊後模擬收到通知'),
      onTap: _showNotification,
    );
    // device info
    yield* _deviceInfo();
  }

  Future<void> _showNotification() async {
    try {
      final data = NotificationMessageData.fromJson(Constants.notificationData);
      final notificationProvider = Get.find<NotificationProvider>();
      await notificationProvider.showNotification(
        title: data.title ?? '',
        body: data.body ?? '',
        payload: data.parameters ?? '',
      );
    } catch (e) {
      controller.logger.e(e);
    }
  }

  Iterable<Widget> _deviceInfo() sync* {
    for (var item in controller.deviceInfo.data.entries) {
      if (item.key == 'utsname') {
        yield* _utsname(item.value);
      } else {
        yield ListTile(
          leading: Text(item.key),
          title: Text(item.value.toString()),
          trailing: IconButton(
            icon: const Icon(Icons.copy),
            onPressed: () async {
              // 複製 device info 到剪貼簿
              await Clipboard.setData(
                  ClipboardData(text: item.value.toString()));
              await Get.showToast('已複製到剪貼簿');
            },
          ),
        );
      }
    }
  }

  Iterable<Widget> _utsname(Map value) sync* {
    for (var item in value.entries) {
      yield ListTile(
        leading: Text(item.key),
        title: Text(item.value.toString()),
        trailing: IconButton(
          icon: const Icon(Icons.copy),
          onPressed: () async {
            // 複製 device info 到剪貼簿
            await Clipboard.setData(ClipboardData(text: item.value.toString()));
            await Get.showToast('已複製到剪貼簿');
          },
        ),
      );
    }
  }
}
