import 'package:device_info_plus/device_info_plus.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

class DevtoolController extends GetxController with StateMixin<String> {
  final PrefProvider prefProvider;
  late Rx<BaseDeviceInfo> _deviceInfo;
  BaseDeviceInfo get deviceInfo => _deviceInfo.value;
  Logger get logger => prefProvider.logger;

  DevtoolController({
    required this.prefProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final dInfo = await prefProvider.deviceInfo.deviceInfo;
      _deviceInfo = dInfo.obs;
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
