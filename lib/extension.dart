import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';
import 'dart:math' show max;

import 'package:efshop/app/components/alert.dart';
import 'package:efshop/app/components/confirm.dart';
import 'package:efshop/app/providers/address_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/ef_colors.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:sizer/sizer.dart';

import 'app/models/app.dart';
import 'app/models/appier_notification.dart' show AppierNotification;
import 'app/models/cart_items_res.dart';
import 'app/models/categories_seo_res.dart';
import 'app/models/category.dart';
import 'app/models/city.dart';
import 'app/models/color_and_is_strong.dart';
import 'app/models/configs_res.dart';
import 'app/models/error_res.dart';
import 'app/models/favorite_data.dart';
import 'app/models/index_module_app.dart';
import 'app/models/index_module_app_res.dart';
import 'app/models/jwt_res.dart';
import 'app/models/login_res.dart';
import 'app/models/members_address_res.dart';
import 'app/models/members_addresses_post_req.dart';
import 'app/models/members_messages_activity.dart';
import 'app/models/members_messages_order.dart';
import 'app/models/members_messages_question.dart';
import 'app/models/members_messages_ship.dart';
import 'app/models/members_messages_unread_res.dart';
import 'app/models/members_my_favorite_res.dart';
import 'app/models/members_orders_invoices_res.dart';
import 'app/models/members_orders_questions_res.dart';
import 'app/models/members_orders_refund_post_req.dart';
import 'app/models/members_orders_refund_product.dart';
import 'app/models/members_orders_refund_res.dart' as refund;
import 'app/models/members_orders_res.dart';
import 'app/models/members_popup_res.dart';
import 'app/models/members_preorders_res.dart';
import 'app/models/members_profile_patch_req.dart';
import 'app/models/members_profile_res.dart';
import 'app/models/members_refund_post_req.dart';
import 'app/models/menu_res.dart';
import 'app/models/message_data.dart';
import 'app/models/product.dart';
import 'app/models/product_detail.dart';
import 'app/models/promotion_detail_res.dart' as promotion;
import 'app/models/product_series_res.dart';
import 'app/models/products_id_comments_res.dart';
import 'app/models/register_apple_req.dart';
import 'app/models/register_line_req.dart';
import 'app/models/register_req.dart';
import 'app/models/thumbnail.dart';
import 'app/models/url.dart';
import 'app/models/voucher_res.dart';
import 'app/modules/home/<USER>/ef_search_delegate.dart';
import 'app/modules/index/views/sub_router.dart';
import 'app/providers/love_code_provider.dart';
import 'app/routes/app_pages.dart';
import 'constants.dart';
import 'enums.dart';
import 'keys.dart';

extension MapX<K, V> on Map<K, V> {
  void removeNull() {
    removeWhere(
        (key, value) => key == null || value == null || value == 'null');
  }

  Map<String, String?> toStringMap() {
    return map(
        (key, value) => MapEntry('$key', value == null ? null : '$value'));
  }
}

extension GetInterfaceX on GetInterface {
  bool canLaunchUrl(Uri uri) {
    return Constants.availablePath.any((element) {
      if (uri.path.startsWith(element)) {
        return true;
      }
      // https 且非 efshop 網域
      if (uri.scheme == Constants.schemeHttps &&
          !uri.host.contains(Constants.efDomain)) {
        return true;
      }
      return false;
    });
  }

  Map<String, String> get headers {
    final prefProvider = find<PrefProvider>();
    return {
      'Authorization': 'Bearer ${prefProvider.token}',
      'device_type': Platform.operatingSystem,
      'app_version': prefProvider.packageInfo.version,
    };
  }

  Future<void> launchUrl(Uri uri) async {
    try {
      final queryParameters = {...uri.queryParameters};
      queryParameters.removeNull();
      // 特殊處理: 搜尋
      if (uri.path.startsWith(Routes.SEARCH)) {
        showSearch(
          context: Get.context!,
          delegate: EfSearchDelegate(
            wabowProvider: Get.find(),
            showResult: true,
          ),
          query: queryParameters['keyword'] ?? '',
        );
        return;
      }
      // 特殊處理: 回到首頁的子頁面
      final indexTab = IndexTab.values.firstWhere(
        (element) => uri.path.startsWith(element.value),
        orElse: () => IndexTab.empty,
      );
      if (indexTab != IndexTab.empty) {
        final parameters = <String, String>{
          Keys.id: '${indexTab.index}',
        };
        // 訂單失敗: /app/CartFinish/fail/7422788
        // 訂單成功: /app/CartFinish/success/7422788
        if ([
          Routes.CART_FINISH,
          Routes.APP_CART_FINISH,
        ].any((element) => uri.path.startsWith(element))) {
          final length = uri.pathSegments.length;
          parameters[Keys.orderStatus] = uri.pathSegments[length - 2];
          parameters[Keys.orderId] = uri.pathSegments.last;
        }
        await Get.offAllNamed(
          Routes.LOADING,
          parameters: parameters,
        );
        return;
      }
      // https 且非 efshop 網域
      if (uri.scheme == Constants.schemeHttps &&
          !uri.host.contains(Constants.efDomain)) {
        await Get.toNamed(
          Routes.EF_WEB,
          parameters: <String, String>{
            Keys.url: uri.toString(),
            Keys.headers: jsonEncode(headers),
          },
        );
        return;
      }
      // 紅利點數特殊處理
      if (uri.path.startsWith(Routes.MY_BONUS)) {
        final entries = <MapEntry<String, String>>[];
        entries.add(const MapEntry(Keys.title, '紅利點數'));
        entries.add(MapEntry(Keys.url, Constants.uriReward.toString()));
        entries.add(MapEntry(Keys.headers, jsonEncode(headers)));
        queryParameters.addEntries(entries);
      }
      // /content/1045/display
      if (uri.path.isNotEmpty) {
        await Get.toNamed(uri.path, parameters: queryParameters);
      }
    } catch (e) {
      developer.log(e.toString());
    }
  }

  void popAll() {
    // Get.until((route) => Get.currentRoute == Routes.INDEX);
    Get.until((route) => route.isFirst);
  }

  Future<void> restartIndex() async {
    try {
      await Get.offAllNamed(Routes.LOADING,
          parameters: <String, String>{Keys.id: '${IndexTab.home.index}'});
    } catch (e) {
      developer.log(e.toString());
    }
  }

  Future<T?> showLoading<T>() {
    return const Center(
      child: CircularProgressIndicator(
        color: EfColors.primary,
      ),
    ).dialog(
      barrierDismissible: false,
    );
  }

  Future<T?> showAlert<T>(
    String content, {
    String title = '',
  }) {
    return Alert(
      content: content,
      onPressed: () => Get.back(result: Button.confirm),
    ).dialog();
    // return defaultDialog<T>(
    //   title: title,
    //   content: Text(content),
    //   textConfirm: '確認',
    //   onConfirm: () {
    //     Get.back();
    //   },
    // );
  }

  Future<T?> showConfirm<T>(
    // String title,
    String content, {
    String? textConfirm,
    String? textCancel,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    return Confirm(
      content: content,
      confirmButtonText: textConfirm,
      cancelButtonText: textCancel,
      onConfirmPressed: () => Get.back(result: Button.confirm),
      onCancelPressed: () => Get.back(result: Button.cancel),
    ).dialog();
    // return defaultDialog<T>(
    //   title: title,
    //   content: Text(content),
    //   textConfirm: textConfirm ?? Button.confirm.text,
    //   textCancel: textCancel ?? Button.cancel.text,
    //   onConfirm: onConfirm ?? () => Get.back(result: Button.confirm),
    //   onCancel: onCancel ?? () => Get.back(result: Button.cancel),
    // );
  }

  Future<bool?> showToast<T>(String content) {
    return Fluttertoast.showToast(
      msg: content,
      backgroundColor: Colors.black.withOpacity(0.6),
      textColor: Colors.white,
      fontSize: 14,
      gravity: ToastGravity.CENTER,
    );
  }
}

final _decimalFormat = NumberFormat.currency(
  locale: 'en_US',
  symbol: '',
  decimalDigits: 0,
);

extension NumX on num {
  double get dh => Constants.sizer.isOn
      ? (toDouble() * Constants.designHeightRatio).h
      : toDouble();
  double get dw => Constants.sizer.isOn
      ? (toDouble() * Constants.designWidthRatio).w
      : toDouble();
  double get dsp => Constants.sizer.isOn ? sp : toDouble();
  String get twoDigits => '$this'.padLeft(2, '0');
  // timestamp to string
  String get formattedDate {
    final timestamp = toInt();
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return '${date.year}/${date.month.twoDigits}/${date.day.twoDigits} ${date.hour.twoDigits}:${date.minute.twoDigits}';
  }

  String get decimalStyle => _decimalFormat.format(this);
}

extension WidgetX on Widget {
  Future<LoginRes?> getLoginRes() {
    var res = Get.find<PrefProvider>().loginRes;
    if (res == null || res.isExpired) {
      return const SubRouter()
          .sizedBox(
            height: (722 * Constants.designHeightRatio).h,
          )
          .sheet(
            // useRootNavigator: true,
            // enableDrag: false,
            isScrollControlled: true,
            ignoreSafeArea: true,
          );
    }
    return Future.value(res);
  }

  Future<bool> initializeController() {
    final completer = Completer<bool>();

    /// Callback called after widget has been fully built
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      completer.complete(true);
    });

    return completer.future;
  }

  Widget click(VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: this,
    );
  }

  Widget expanded({int flex = 1}) => Expanded(flex: flex, child: this);
  Widget sizedBox({double? width, double? height}) =>
      SizedBox(width: width, height: height, child: this);

  // 可以把任何 Widget 使用 Dialog 形式呈現
  Future<T?> dialog<T>({
    bool barrierDismissible = true,
    EdgeInsets insetPadding = EdgeInsets.zero,
  }) {
    return Get.dialog<T>(
      Dialog(
        insetPadding: insetPadding,
        backgroundColor: Colors.transparent,
        elevation: 0.0,
        child: SizedBox(
          // width: 300.dw,
          child: this,
        ),
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  // 可以把任何 Widget 使用 Sheet 形式呈現
  Future<T?> sheet<T>({
    final bool isDismissible = true,
    final bool enableDrag = false,
    final bool isScrollControlled = true,
    final bool ignoreSafeArea = false,
  }) {
    return Get.bottomSheet<T>(
      SafeArea(child: this),
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      ignoreSafeArea: ignoreSafeArea,
      isDismissible: isDismissible,
      enterBottomSheetDuration: 200.milliseconds,
      exitBottomSheetDuration: 200.milliseconds,
    );
  }
}

extension MembersAddressesPostReqX on MembersAddressesPostReq {
  // 地址前綴
  String get addressLeading {
    final addressProvider = Get.find<AddressProvider>();
    final res = addressProvider.getCityTown(zipcode ?? '');
    return '${res.key ?? ''}${res.value ?? ''}';
  }

  // 地址後綴
  String get addressTail {
    final localAddress = address ?? '';
    if (localAddress.contains(addressLeading)) {
      return localAddress.substring(addressLeading.length);
    }
    return localAddress;
  }

  //
  bool get isAddressLeadingOnly => address == addressLeading;
  void validate(AddressType type) {
    if (address == null || address!.isEmpty) {
      throw '地址是必填欄位';
    }
    if (receiverName == null || receiverName!.isEmpty) {
      throw '收件人姓名是必填欄位';
    }
    if (zipcode == null || zipcode!.isEmpty) {
      throw '縣市、市區鄉鎮是必填欄位';
    }
  }

  String get display {
    Iterable<String> children() sync* {
      if (storeName != null && storeName!.isNotEmpty) {
        yield storeName!;
        yield '-';
      }
      yield address ?? '';
    }

    return children().join();
  }

  bool get isValid => address != null && address!.isNotEmpty;
}

extension MembersAddressResX on MembersAddressRes {
  MembersAddressesPostReq toPostReq() {
    return MembersAddressesPostReq(
      isDefault: isDefault,
      receiverName: receiverName,
      storeId: storeId,
      storeName: storeName,
      zipcode: zipcode,
      address: address,
    );
  }

  String get display {
    Iterable<String> children() sync* {
      if (storeName != null && storeName!.isNotEmpty) {
        yield storeName!;
        yield '-';
      }
      yield address ?? '';
    }

    return children().join();
  }

  bool get isValid => address != null && address!.isNotEmpty;

  // String getSubAddress(AddressProvider addressProvider) {
  String get addressTail {
    final addressProvider = Get.find<AddressProvider>();
    final res = addressProvider.getCityTown(zipcode ?? '');
    final prefix = '${res.key ?? ''}${res.value ?? ''}';
    address ??= '';
    final index = address!.indexOf(prefix);
    if (index >= 0) {
      return address!.substring(index + prefix.length);
    }
    return address!;
  }
}

extension CityX on City {
  String getZipCode(String townName) {
    for (final store in data ?? <Store>[]) {
      if (store.name == townName) {
        return store.code ?? '';
      }
    }
    return '';
  }
}

extension GetStorageX on GetStorage {
  Stream<String> watch([String? key]) async* {
    final streamController = StreamController<String>();
    VoidCallback disposeListen;
    if (key != null && key.isNotEmpty) {
      disposeListen = listenKey(key, (e) {
        streamController.add(key);
      });
    } else {
      disposeListen = listen(() {
        streamController.add('');
      });
    }
    yield* streamController.stream;
    disposeListen.call();
    streamController.close();
  }

  // to map with template K, V
  Map<K, V> toMap<K, V>() {
    final keys = getKeys<Iterable<K>>();
    final values = getValues<Iterable<V>>();
    return Map<K, V>.fromIterables(keys, values);
    // return <K, V>{
    //   for (final key in getKeys<Iterable<String>>())
    //     key as K: read(key) as V,
    // };
  }
}

extension ListenableX on Listenable {
  Stream<void> watch() async* {
    final streamController = StreamController<void>();
    void callback() {
      streamController.add(null);
    }

    addListener(callback);
    yield* streamController.stream;
    removeListener(callback);
    streamController.close();
  }
}

extension IterableX<T extends Widget> on Iterable<T> {
  Widget column({
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.min,
  }) {
    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: toList(growable: false),
    );
  }

  Widget row({
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.min,
  }) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: toList(growable: false),
    );
  }
}

extension MembersMyFavoriteX on MembersMyFavorite {
  num get finalPrice {
    return hasPromotionPrice ? (promotionPrice ?? 0) : (price ?? 0);
  }

  num get numOfQuantity => num.tryParse(quantity ?? '') ?? 0;

  bool get hasPromotionPrice => promotionPrice != null && promotionPrice! >= 0;

  FavoriteData toFavoriteData() => FavoriteData.fromJson(toJson());

  bool get isAvailable => available == '1';

  bool get isOutOfStock => numOfQuantity == 0;

  bool get isInStock => numOfQuantity != 0;

  bool get isSoldOut => isOutOfStock;

  bool get canAddToCart => isAvailable && isInStock;
}

extension FavoriteDataX on FavoriteData {
  num get numOfQuantity => num.tryParse(quantity ?? '') ?? 0;
  num get finalPrice {
    return hasPromotionPrice ? (promotionPrice ?? 0) : (price ?? 0);
  }

  bool get hasPromotionPrice =>
      promotionPrice != null && promotionPrice! >= 0 && promotionPrice != price;

  Color get displayColor {
    if (available == '0') {
      return EfColors.grayBB;
    }
    if (numOfQuantity == 0) {
      // return EfColors.gray93;
      return EfColors.grayBB;
    }
    return EfColors.primary;
  }

  String get displayText {
    if (available == '0') {
      return '售完已下架';
    }
    if (quantity == '0') {
      return '售完。貨到通知我';
    }
    return '加到購物車';
  }

  bool get isAvailable => available == '1';

  bool get isOutOfStock => numOfQuantity == 0;

  bool get isInStock => numOfQuantity != 0;

  bool get isSoldOut => isOutOfStock;

  bool get canAddToCart => isAvailable && isInStock;
}

extension MembersPreordersResX on MembersPreordersRes {
  FavoriteData toFavoriteData() {
    return FavoriteData(
      name: productName,
      color: color,
      price: productPrice,
      promotionPrice: promotionPrice,
      size: size,
      thumbnail: thumbnail,
      available: available == true ? '1' : '0',
      quantity: '$quantity',
    );
  }

  bool get isOutOfStock => quantity == 0;

  bool get isInStock => quantity != 0;

  bool get isSoldOut => isOutOfStock;

  bool get canAddToCart => available == true && isInStock;
}

extension MembersOrdersResX on MembersOrdersRes {
  bool get needBankInfoWhenRefund {
    // payment name 包含信用卡
    if ((paymentName ?? '').contains('信用卡')) {
      return false;
    }
    // payment name 包含 LINE Pay 不分大小寫使用正規
    if (RegExp(r'line ?pay', caseSensitive: false)
        .hasMatch(paymentName ?? '')) {
      return false;
    }
    return true;
  }

  // 訂單取消
  bool get isCanceled => Constants.orderStatusFailed.contains(status);
  // 顯示評價按鈕的規則
  bool get containsRating {
    if (Constants.ratingButton.contains(status)) {
      // 任一商品評價為空
      return (products ?? []).any((element) =>
          element.userComment == null || element.userComment!.isEmpty);
    }
    return false;
  }

  // 訂單成立
  bool get containsCreated => true;
  // 處理中
  bool get containsProcessing {
    // tag name 包含 '送達'
    if ((tagName ?? '').contains('待出貨')) {
      return false;
    }
    // tag name 包含 '打單完畢'
    // if ((tagName ?? '').contains('打單完畢')) {
    //   return true;
    // }
    return true;
  }

  // 已出貨
  bool get containsShipped {
    if ([
      OrderStatusRaw.shipped.value, // 已出貨
      OrderStatusRaw.awaitingSignature.value, // 待簽收
      OrderStatusRaw.arrivedAtStore.value, // 已到店
      OrderStatusRaw.pickedUp.value, // 已取貨
      OrderStatusRaw.awaitingRating.value, // 待評價
      OrderStatusRaw.returned.value, // 退貨
      OrderStatusRaw.closed.value, // 結案
      OrderStatusRaw.shipped.value, // 已出貨
    ].contains(status)) {
      return true;
    }
    // tag name 包含 '已上傳'
    if ((tagName ?? '').contains('已上傳')) {
      return true;
    }
    if ((tagName ?? '').contains('已出貨')) {
      return true;
    }
    return false;
  }

  // 派送中
  bool get containsDelivering {
    if ([
      OrderStatusRaw.awaitingSignature.value, // 待簽收
      OrderStatusRaw.arrivedAtStore.value, // 已到店
      OrderStatusRaw.pickedUp.value, // 已取貨
      OrderStatusRaw.awaitingRating.value, // 待評價
      OrderStatusRaw.returned.value, // 退貨
      OrderStatusRaw.closed.value, // 結案
    ].contains(status)) {
      return true;
    }
    // tag name 包含 '已到'
    if ((tagName ?? '').contains('已到')) {
      return true;
    }
    if ((tagName ?? '').contains('已出貨')) {
      // "shippingDate": "2024-06-04 17:53:52"
      if (shippingDateTime != null) {
        // 隔日才會傳 true
        final tomorrow = shippingDateTime!.midnight.add(1.days);
        final now = DateTime.now();
        // if (now.difference(shippingDateTime).inDays > 0) {
        if (now.isAfter(tomorrow)) {
          return true;
        }
      }
    }
    return false;
  }

  DateTime? get shippingDateTime {
    if (shippingDate != null && shippingDate!.isNotEmpty) {
      return DateTime.tryParse(shippingDate!);
    }
    return null;
  }

  // 送達門市(超取) or 派送成功(宅配)
  bool get containsEndPoint {
    if ([
      OrderStatusRaw.awaitingSignature.value, // 待簽收
      OrderStatusRaw.arrivedAtStore.value, // 已到店
      OrderStatusRaw.pickedUp.value, // 已取貨
      OrderStatusRaw.returned.value, // 退貨
      OrderStatusRaw.closed.value, // 結案
    ].contains(status)) {
      return true;
    }
    // tag name 包含 '送達'
    if ((tagName ?? '').contains('送達')) {
      return true;
    }
    // tag name 包含 '取貨'
    if ((tagName ?? '').contains('取貨')) {
      return true;
    }
    return false;
  }

  num get numOfTotal => total ?? 0;
  String get displayInvoiceVehicle {
    if (carrierType == CarrierType.love) {
      final lp = Get.find<LoveCodeProvider>();
      // 有找到
      if (lp.dict.containsKey(invoiceVehicle)) {
        return lp.dict[invoiceVehicle] ?? '';
      }
    }
    return invoiceVehicle ?? '';
  }

  String get displayStatus {
    if (status == '取消') {
      return '訂單已取消';
    }
    if (status == '付款失敗') {
      return '付款失敗 ($paymentName)';
    }
    return status ?? '';
  }

  CarrierType get carrierType {
    final invoiceRequestNum = int.tryParse(invoiceRequest ?? '') ?? 0;
    if (invoiceRequestNum >= 0 &&
        invoiceRequestNum < CarrierType.values.length) {
      return CarrierType.values.elementAt(invoiceRequestNum);
    }
    return CarrierType.none;
  }

  String get displayInvoiceTypeWithCarrier {
    Iterable<String> strings() sync* {
      yield invoiceType.display;
      // 統一編號
      if (InvoiceType.company == invoiceType) {
        yield vatNumber ?? '';
      }
      // 載具條碼, 捐贈
      if ([InvoiceType.carrier, InvoiceType.loveCode].contains(invoiceType)) {
        yield displayInvoiceVehicle;
      }
    }

    return strings().join(' ');
  }

  InvoiceType get invoiceType {
    // 公司
    if (vatNumber != null && vatNumber!.isNotEmpty) {
      return InvoiceType.company;
    }
    final carrierType = this.carrierType;
    // 捐贈
    if (CarrierType.love == carrierType) {
      return InvoiceType.loveCode;
    }
    // 個人
    if (CarrierType.member == carrierType) {
      return InvoiceType.personal;
    }
    // 載具
    if (Constants.carriers.contains(carrierType)) {
      return InvoiceType.carrier;
    }
    // 無
    return InvoiceType.none;
  }

  num get discountProductsAmount {
    return discountProducts.fold<num>(0, (previousValue, element) {
      return previousValue + (element.subtotal ?? 0);
    });
  }

  num get normalProductsAmount {
    return normalProducts.fold<num>(0, (previousValue, element) {
      return previousValue + (element.subtotal ?? 0);
    });
  }

  Iterable<Product> getDiscountProducts(Iterable<String> productIds) {
    return (products ?? []).where((element) {
      return productIds.contains(element.productId);
    });
  }

  ///
  /// 非商品列表
  ///
  Iterable<Product> get discountProducts {
    return (products ?? []).where((element) {
      // return Constants.discountProduct.contains(element.productId);
      return element.isDiscount;
    });
  }

  ///
  /// 商品列表
  ///
  Iterable<Product> get normalProducts {
    return (products ?? []).where((element) {
      // return !Constants.discountProduct.contains(element.productId);
      return element.isNormal;
    });
  }

  num get quantity {
    return normalProducts.fold<num>(0, (previousValue, element) {
      return previousValue + element.numOfQuantity;
    });
  }

  bool get hasInvoiceNumber => !((invoiceDate == null || invoiceDate!.isEmpty));
  String get displayInvoiceNumber {
    if (hasInvoiceNumber) {
      return invoiceNumber ?? '';
    }
    return '尚未開立';
  }

  bool get hasComment => comment != null && comment!.isNotEmpty;
  bool get containsStoreId => storeId != null && storeId!.isNotEmpty;
  // TODO: rename shippingAddress
  String get displayAddress {
    if (containsStoreId) {
      return storeAddress ?? '';
    }
    return displayReceiverAddress;
  }

  String get displayReceiverAddress {
    final city = receiverCity ?? '';
    final address = receiverAddress ?? '';
    return '$city$address';
  }

  String get displayEndPoint {
    if (containsStoreId) {
      return OrderProcess.arrived.display;
    }
    return OrderProcess.success.display;
  }

  OrderStatus get orderStatus {
    if (OrderStatus.padding.displayList.contains(status ?? '')) {
      if (isCancel == false) {
        return OrderStatus.padding;
      }
    }
    if (OrderStatus.shipped.displayList.contains(status ?? '')) {
      return OrderStatus.shipped;
    }
    if (OrderStatus.signed.displayList.contains(status ?? '')) {
      return OrderStatus.signed;
    }
    if (OrderStatus.rated.displayList.contains(status ?? '')) {
      return OrderStatus.rated;
    }
    if (OrderStatus.refund.displayList.contains(status ?? '')) {
      return OrderStatus.refund;
    }
    return OrderStatus.all;
  }

  String get shippingName {
    if (storeId != null && storeId!.isNotEmpty) {
      // return '超商取貨';
      return storeName ?? '';
    }
    return '宅配';
  }
}

extension ProductX on Product {
  String get displayRefundProductName {
    final mustCheck = '(未逹滿額贈，請勾選)';
    final name = refundProductName ?? '';
    // 檢查是否包含 "(未逹滿額贈，請勾選)"，並在右括號後添加換行符號
    if (name.contains(mustCheck)) {
      return '$mustCheck\n$productName';
    }
    // 如果不包含 "(未逹滿額贈，請勾選)"，則直接返回原始名稱
    return name;
  }

  bool get isRefund {
    return refundQuantity != null && refundQuantity! > 0;
  }

  AnalyticsEventItem toAnalyticsEventItem() {
    return AnalyticsEventItem(
      // affiliation: order.affiliateOrderType,
      currency: 'TWD',
      // coupon: order.couponCode,
      // creativeName: productDetail.topCategoryName,
      // creativeSlot: productDetail.categoryName,
      discount: discount,
      // index: product.index,
      // itemBrand: productDetail.brandName,
      // itemCategory: topCategoryName,
      // itemCategory2: categoryName,
      // itemCategory3: subCategoryName,
      // itemCategory4: subSubCategoryName,
      // itemCategory5: subSubSubCategoryName,
      itemId: productNumber,
      // itemListId: productDetail.topCategoryName,
      // itemListName: productDetail.categoryName,
      itemName: productName,
      itemVariant: productSpecName,
      // locationId: product.locationId,
      price: numOfOriginalPrice,
      promotionId: promotionId != null ? '$promotionId' : null,
      promotionName: promotionName,
      quantity: numOfQuantity.toInt(),
      // parameters:
    );
  }

  bool get isCommentable => userComment == null || userComment!.isEmpty;
  String get shareUrlWithScheme => 'https://$shareUrl';
  String get displaySubtitle {
    Iterable<String> children() sync* {
      yield productSpecName ?? '';
      yield ' x';
      yield '$numOfOrderQuantity';
    }

    return children().join();
  }

  num get numOfOrderQuantity {
    var res = num.tryParse(quantity ?? '') ?? 0;
    if (res > 0) {
      return res;
    }
    res = refundQuantity ?? 0;
    if (res > 0) {
      return res;
    }
    return 1;
  }

  num get discount {
    return max(numOfOriginalPrice - numOfFinalPrice, 0);
  }

  bool get hasOriginalPrice =>
      numOfOriginalPrice > numOfFinalPrice && numOfFinalPrice > 0;
  num get numOfOriginalSubtotal => numOfOriginalPrice * numOfOrderQuantity;
  num get numOfOriginalPrice => num.tryParse(originalPrice ?? '') ?? 0;
  num get numOfFinalPrice => num.tryParse(finalPrice ?? '') ?? 0;
  num get numOfQuantity {
    final buyQuantity = num.tryParse(quantity ?? '');
    if (buyQuantity != null) {
      return buyQuantity;
    }
    if (refundQuantity != null) {
      return refundQuantity!;
    }
    return 0;
  }

  num get numOfProductId => num.tryParse(productId ?? '') ?? 0;
  bool get isNormal => !isDiscount;
  bool get isDiscount {
    if (numOfProductId <= Constants.maxOfDiscountProductId) {
      return true;
    }
    // if (Constants.discountProduct.contains(productId)) {
    //   return true;
    // }
    return false;
  }
}

extension MembersMessagesOrderX on MembersMessagesOrder {
  MessageData toMessageData() => MessageData(
        createDatetime: createDatetime,
        titleText: title,
        subtitleText: message,
        id: orderId,
      );
}

extension MembersMessagesQuestionX on MembersMessagesQuestion {
  MessageData toMessageData() => MessageData(
        createDatetime: createDatetime,
        titleText: title,
        subtitleText: message,
        id: orderId,
      );
}

extension MembersOrdersQuestionsResX on MembersOrdersQuestionsRes {
  MessageData toMessageData() => MessageData(
        createDatetime: createDatetime,
        replyDatetime: replyDatetime,
        titleText: question,
        subtitleText: reply,
      );
}

extension MembersOrdersRefundProductX on MembersOrdersRefundProduct {
  bool get hasReason => reasonId != null && reasonId!.isNotEmpty;
  num get numOfQuantity => num.tryParse(quantity ?? '') ?? 0;
  set numOfQuantity(num value) => quantity = '$value';
  RefundReason get refundReason {
    if (reasonId == null || reasonId!.isEmpty) {
      return RefundReason.none;
    }
    return RefundReason.values.firstWhere(
      (element) => '${element.value}' == reasonId,
      orElse: () => RefundReason.none,
    );
  }

  set refundReason(RefundReason value) {
    if (value == RefundReason.none) {
      reasonId = '';
    } else {
      reasonId = '${value.value}';
    }
  }
}

extension MembersOrdersRefundPostReqX on MembersOrdersRefundPostReq {
  MembersRefundPostReq toMembersRefundPostReq() {
    return MembersRefundPostReq(
      accountName: accountName,
      accountNumber: accountNumber,
      bankCode: bankCode,
      bankBranch: bankBranch,
    );
  }

  void validate() {
    // 如何沒有選擇銀行，就需要填寫銀行資訊
    if (userRefundId == null || userRefundId!.isEmpty) {
      if (accountName == null || accountName!.isEmpty) {
        throw '帳號戶名是必填欄位';
      }
      if (accountNumber == null || accountNumber!.isEmpty) {
        throw '帳號是必填欄位';
      }
      if (bankCode == null || bankCode!.isEmpty) {
        throw '銀行代碼是必填欄位';
      }
      if (bankBranch == null || bankBranch!.isEmpty) {
        throw '銀行名稱是必填欄位';
      }
    }
  }
}

extension MembersOrdersRefundResX on refund.MembersOrdersRefundRes {
  AddressType get shippingType {
    if (shippingMethod == '7return') {
      return AddressType.sevenEleven;
    }
    if (shippingMethod == 'fmreturn') {
      return AddressType.familyMart;
    }
    return AddressType.home;
  }

  num get quantity {
    return (products ?? []).fold<num>(0, (previousValue, element) {
      return previousValue + (element.refundQuantity ?? 1);
    });
  }

  num get numOfTotal => total ?? 0;

  num get amountOfRefund {
    return (products ?? []).fold<num>(0, (previousValue, element) {
      return previousValue + (element.subtotal ?? 0);
    });
  }
}

extension DetailX on Detail {
  Product toProduct() {
    return Product(
      productSpecName: productName,
      quantity: '$quantity',
      finalPrice: '${numOfAmount * numOfUnitPrice}',
    );
  }

  num get numOfAmount => amount ?? 0;
  num get numOfUnitPrice => unitPrice ?? 0;
}

extension MembersOrdersInvoicesResX on MembersOrdersInvoicesRes {
  num get numOfTaxAmount => num.tryParse(taxAmount ?? '') ?? 0;
  num get numOfInvoiceAmount => invoiceAmount ?? 0;
  String get displaySellerBan => (sellerBan ?? 0).toString().padLeft(8, '0');
  String get displayBuyerBan => (buyerBan ?? 0).toString().padLeft(8, '0');
  String get displayTaxType => numOfInvoiceAmount == 0 ? '個人' : '公司';
  int get invoiceDateTimestamp {
    // 2018-07-24 to timestamp
    final date = invoiceDate ?? '';
    final parts = date.split('-');
    if (parts.length == 3) {
      final year = int.tryParse(parts[0]) ?? 0;
      final month = int.tryParse(parts[1]) ?? 0;
      final day = int.tryParse(parts[2]) ?? 0;
      final dateTime = DateTime(year, month, day);
      return dateTime.millisecondsSinceEpoch;
    }
    return 0;
  }

  // taiwanese date
  String get displayInvoiceDate {
    final datetime = DateTime.fromMillisecondsSinceEpoch(invoiceDateTimestamp);
    final taiwaneseYear = datetime.year - 1911;
    final month = datetime.month;
    final isEvent = month % 2 == 0;
    final start = month - (isEvent ? 1 : 0);
    final end = month + (isEvent ? 0 : 1);
    return '${taiwaneseYear.twoDigits}年$start-$end月';
  }
}

extension UrlX on Url {
  bool get isValid {
    return action != null && action!.isNotEmpty && id != null && id!.isNotEmpty;
  }

  Uri get uri {
    Iterable<String> children() sync* {
      yield '/$action';
      if (id != null && id!.isNotEmpty && id != 'null') {
        yield '/$id';
        if (page != null && page!.isNotEmpty) {
          yield '/$page';
        }
      }
    }

    final uri = Uri.parse(children().join());
    final queryParameters = {
      ...uri.queryParameters,
      ...toJson(),
    };
    queryParameters.removeNull();
    return uri.replace(queryParameters: queryParameters);
  }
}

extension ProductDetailX on ProductDetail {
  String get prefixNumber {
    final number = this.number ?? '';
    final index = number.indexOf('-');
    if (index >= 0) {
      return number.substring(0, index);
    }
    return number;
  }

  String get postfixNumber {
    final number = this.number ?? '';
    final index = number.indexOf('-');
    if (index >= 0) {
      return number.substring(index + 1);
    }
    return '';
  }

  // 轉換成 firebase Analytics 事件參數
  AnalyticsEventItem toAnalyticsEventItem({int? quantity}) {
    return AnalyticsEventItem(
      // affiliation: order.affiliateOrderType,
      currency: 'TWD',
      // coupon: order.couponCode,
      // creativeName: productDetail.topCategoryName,
      // creativeSlot: productDetail.categoryName,
      discount: discount,
      // index: product.index,
      // itemBrand: productDetail.brandName,
      itemCategory: topCategoryName?.withoutTag,
      itemCategory2: categoryName,
      // itemCategory3: subCategoryName,
      // itemCategory4: subSubCategoryName,
      // itemCategory5: subSubSubCategoryName,
      itemId: number,
      // itemListId: productDetail.topCategoryName,
      // itemListName: productDetail.categoryName,
      itemName: name,
      itemVariant: specName,
      // locationId: product.locationId,
      price: originalPrice,
      promotionId: promotionId != null ? '$promotionId' : null,
      promotionName: promotionName,
      quantity: quantity,
      // parameters:
    );
  }

  // 活動判斷
  bool get containsPromotion =>
      promotionName != null && promotionName!.isNotEmpty;
  Iterable<String> get extractImageUrls {
    final htmlString = descriptionImage ?? '';
    // 定義正規表達式
    final regex = RegExp(r'<img[^>]* src="([^"]*)', caseSensitive: false);
    // 匹配正規表達式
    final matches = regex.allMatches(htmlString);
    // 提取匹配到的網址
    return matches.map((match) => match.group(1)!);
  }

  bool get hasPromotionPrice => promotionPrice != null && promotionPrice! > 0;

  num get finalPrice {
    return hasPromotionPrice ? (promotionPrice ?? 0) : originalPrice;
  }

  num get originalPrice => num.tryParse(price ?? '') ?? 0;

  num get discount {
    if (hasPromotionPrice) {
      final ret = originalPrice - (promotionPrice ?? 0);
      return max(ret, 0);
    }
    return 0;
  }

  Url getUrl() {
    if (url != null && url!.isValid) {
      return url!;
    }
    final uri = Uri.parse(shareUrl ?? '');
    return Url(
      action: uri.pathSegments[0],
      id: uri.pathSegments[1],
    );
  }

  String get descriptionHtml => _getHtmlString(description ?? '');

  String get htmlString {
    return '''
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC&display=swap">
  <head>
  <style>
  .btn_white {
    color: #565656;
    padding: 0;
    width: 100%;
    line-height: 32px;
    background: #f5f5f5;
    text-align: center;
    letter-spacing: 2px;
    margin-bottom: 10px;
  }

  .btn_white>img {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-top: -2px;
    margin-left: 4px;
    opacity: 0.8;
  }

  .imgBox img {
    width: 100%;
    height: auto;
  }

  body {
    margin: 0;
    padding: 0 8px;
    font-family: 'Noto Sans TC', sans-serif;
  }
  </style>
  <body>
    <div class="btn_white align_center js-tab">商品說明<img
      src="data:image/png;base64,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">
    </div>
      $description
    <br>
    <div class="btn_white align_center js-tab">尺寸說明<img
      src="data:image/png;base64,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">
    </div>
      $specification
    <br>
    <div class="btn_white align_center js-tab">試穿報告<img
      src="data:image/png;base64,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">
    </div>
      $tryReport
    </div>
  </body>
</html>
    ''';
  }

  String get descriptionImageHtml {
    var res = _getHtmlString(descriptionImage ?? '');
    // 在 youtube 的 iframe 中加入 autoplay 及 mute
    final regex = RegExp(r'https://www\.youtube\.com\/embed\/([^"]*)');
    final matches = regex.allMatches(res);
    for (final match in matches) {
      final url = match.group(0);
      if (url != null) {
        final uri0 = Uri.parse(url);
        final uri1 = uri0.replace(queryParameters: {
          ...uri0.queryParameters,
          'autoplay': '1',
          'mute': '1',
        });
        res = res.replaceAll(url, uri1.toString());
      }
    }
    return res;
  }

  String get imageHtmlString {
    return '''
<!DOCTYPE html>
<html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC&display=swap">
  </head>
  <style>
    img {
      max-width: 100%;
      height: auto;
    }
    body {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      margin: 0;
      padding: 0;
      font-size: 16px;
      line-height: 1.5;
      color: #333;
      overflow-x: hidden;
      overflow-y: auto;
      font-family: 'Noto Sans TC', sans-serif;
    }
  </style>
  <body>
    <p>$descriptionImage</p>
  </body>
</html>
    ''';
  }
}

// 定义 HTML 内容，包括手机版 Meta 标签
String _getHtmlString(String htmlString) {
  return '''
<!DOCTYPE html>
<html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC&display=swap">
  </head>
  <style>
    .btn_white {
        color: #565656;
        padding: 0;
        width: 100%;
        line-height: 32px;
        background: #f5f5f5;
        text-align: center;
        letter-spacing: 2px;
        margin-bottom: 10px;
    }

    .btn_white>img {
        width: 16px;
        height: 16px;
        vertical-align: middle;
        margin-top: -2px;
        margin-left: 4px;
        opacity: 0.8;
    }

    .imgBox {
        width: 100%;
        height: auto;
    }

    img {
        width: 100%;
        height: auto;
    }

    body {
        margin: 0;
        font-family: 'Noto Sans TC', sans-serif;
    }
  </style>
  <body>
    $htmlString
  </body>
</html>
''';
}

extension ColorAndIsStrongX on ColorAndIsStrong {
  Color? get colorObject {
    final colorCodeString = color ?? '';
    if (colorCodeString.isNotEmpty) {
      RegExp regExp = RegExp(r'^[0-9a-fA-F]{6}$');
      if (regExp.hasMatch(colorCodeString)) {
        final value = int.parse(colorCodeString, radix: 16); // 將色碼轉換為整數
        return Color(value + 0xFF000000); // 創建 Color 物件
      }
      // RegExp regExp = RegExp(r'#([a-fA-F0-9]{6})');
      // Match? match = regExp.firstMatch(colorCodeString);
      // if (match != null) {
      //   String colorCode = match.group(1)!; // 提取色碼
      //   final value = int.parse(colorCode, radix: 16); // 將色碼轉換為整數
      //   return Color(value + 0xFF000000); // 創建 Color 物件
      // }
    }
    return null;
  }
}

extension CategoryX on Category {
  // 如果包含 html tag，則回傳 html tag 的文字
  String get displayName {
    final regex = RegExp(r'<[^>]*>');
    final input = name ?? '';
    if (regex.hasMatch(input)) {
      return input.replaceAll(regex, '');
    }
    return input;
  }

  Color? _getTitleColor(String input) {
    if (colorAndIsStrong?.colorObject != null) {
      return colorAndIsStrong?.colorObject;
    }
    // fallback
    // final regex = RegExp(r'<font[^>]*color=#?([a-fA-F0-9]{6})[^>]*>');
    // final matches = regex.allMatches(input);
    // for (RegExpMatch match in matches) {
    //   if (match.groupCount > 0) {
    //     final matchString = match.group(1);
    //     if (matchString != null) { // ff6c00
    //       // Remove the # character and convert the color code to an integer.
    //       final colorCode = int.parse(matchString, radix: 16);
    //       // Create a Color object and return it.
    //       return Color(colorCode + 0xFF000000);
    //     }
    //   }
    // }
    return null;
  }

  FontWeight? _getStrong(String input) {
    if (colorAndIsStrong?.isStrong == true) {
      return FontWeight.bold;
    }
    // fallback
    // final regex = RegExp(r'<(strong|b)>(.*?)</\1>');
    // final matches = regex.allMatches(input);
    // for (RegExpMatch match in matches) {
    //   if (match.groupCount > 0) {
    //     final matchString = match.group(1);
    //     if (matchString != null) {
    //       return FontWeight.bold;
    //     }
    //   }
    // }
    return null;
  }

  TextStyle? get textStyle {
    // 可能包含 html 編碼: <font color=#ff6c00><strong>SALE<\/strong><\/font>
    final input = name ?? '';
    final style = TextStyle(
      color: _getTitleColor(input),
      fontWeight: _getStrong(input),
    );
    return style;
  }

  TextStyle? getTextStyle(GetStorage box) {
    if (url?.action == 'category') {
      try {
        final json = box.read(url?.id ?? '');
        final seo = CategoriesSeoRes.fromJson(json);
        return seo.adv?.textStyle;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  ProductDetail toProductDetail() => ProductDetail.fromJson(toJson());
  // 建立 tree
  void updateChildren() {
    for (final child in subCategories ?? <Category>[]) {
      child.parentId = id;
      child.updateChildren();
    }
  }

  void refactorName() {
    name = name?.replaceAll('‧', '\n') ?? '';
    for (var element in subCategories ?? <Category>[]) {
      element.refactorName();
    }
  }

  bool get hasParent => parentId != null && parentId!.isNotEmpty;
  // String get uniqueId => hasParent ? '$parentId.$id' : id ?? '';
  String get uniqueId => id ?? '';

  bool get isVisible => visible == '1';
}

extension ThumbnailX on Thumbnail {
  bool get containsImage {
    return src?.isNotEmpty == true;
  }

  bool get containsSize {
    // final sized = width != null && height != null;
    // if (!sized && isVideo) {
    //   RegExp regExp = RegExp(r'_(\d+)_(\d+)_');
    //   Match? match = regExp.firstMatch(src ?? '');
    //   if (match != null) {
    //     String width = match.group(1)!; // 提取寬度
    //     String height = match.group(2)!; // 提取高度
    //     this.width = num.tryParse(width);
    //     this.height = num.tryParse(height);
    //   }
    // }
    return width != null && height != null;
  }

  double get aspectRatio {
    final ret = (width ?? 0) / (height ?? 1);
    return ret.isNaN || ret == 0 || ret.isInfinite ? 1 : ret;
  }

  num get reverseAspectRatio {
    final ret = (height ?? 0) / (width ?? 1);
    return ret.isNaN || ret == 0 || ret.isInfinite ? 1 : ret;
  }

  bool get isVideo => src?.endsWith('.mp4') == true;
  Uri get uri => Uri.parse(src ?? '');
}

extension ProductSeriesResX on ProductSeriesRes {
  bool get isVideo => mainImage?.endsWith('.mp4') == true;
  bool get isAvailable => available == 1;
  bool get hsaQuantity => quantity is num && quantity! > 0;
  bool get isAvailableForSale => isAvailable && hsaQuantity;
  // 售完，可通知
  bool get isOutOfStock => quantity == 0;
  // 已下架
  bool get isSoldOut => !isAvailable;
  bool get hasPromotionPrice => promotionPrice != null && promotionPrice! >= 0;
  num get finalPrice {
    return hasPromotionPrice ? (promotionPrice ?? 0) : (price ?? 0);
  }

  String get displayText {
    if (isSoldOut) {
      return '售完已下架';
    }
    if (isOutOfStock) {
      return '售完。貨到通知我';
    }
    return '確認';
    // return '加到購物車';
  }

  Color get displayColor {
    if (!isAvailable) {
      return EfColors.grayD5;
    }
    if (quantity == 0) {
      return EfColors.gray77;
    }
    return EfColors.primary;
  }
}

extension RegisterReqX on RegisterReq {
  DateTime? get birthdayAsDateTime {
    return DateTime.tryParse(birthday ?? '');
  }

  String get birthdayAsString {
    final dt = birthdayAsDateTime;
    if (dt == null) {
      return '';
    }
    return DateFormat('yyyy / MM / dd').format(birthdayAsDateTime!);
  }

  bool validate() {
    // email check
    if (GetUtils.isEmail(email ?? '') == false) {
      throw '請輸入電子郵件';
    }
    // password check
    if (password == null || password?.isEmpty == true) {
      throw '請輸入密碼';
    }
    // fullname check
    if (fullname == null || fullname?.isEmpty == true) {
      throw '請輸入姓名';
    }
    return true;
  }

  bool validatePassword(String confirmPassword) {
    if (password != confirmPassword) {
      throw '密碼不一致';
    }
    return true;
  }
}

extension RegisterLineReqX on RegisterLineReq {
  bool validate() {
    // email check
    if (GetUtils.isEmail(email ?? '') == false) {
      throw '請輸入電子郵件';
    }
    // password check
    if (password == null || password?.isEmpty == true) {
      throw '請輸入密碼';
    }
    // fullname check
    if (fullname == null || fullname?.isEmpty == true) {
      throw '請輸入姓名';
    }
    return true;
  }

  bool validatePassword(String confirmPassword) {
    if (password != confirmPassword) {
      throw '密碼不一致';
    }
    return true;
  }
}

extension RegisterAppleReqX on RegisterAppleReq {
  bool validate() {
    // email check
    if (GetUtils.isEmail(email ?? '') == false) {
      throw '請輸入電子郵件';
    }
    // password check
    // if (password == null || password?.isEmpty == true) {
    //   throw '請輸入密碼';
    // }
    // fullname check
    if (fullname == null || fullname?.isEmpty == true) {
      throw '請輸入姓名';
    }
    return true;
  }

  bool validatePassword(String confirmPassword) {
    final pwd = password ?? '';
    if (pwd != confirmPassword) {
      throw '密碼不一致';
    }
    return true;
  }
}

extension MenuResX on MenuRes {
  Url get urlObject => Url.fromRawJson(url ?? '{}');
  bool get isBranch => branch == true;
  bool get hasChildren => children?.isNotEmpty == true;
  bool get hasParent => parentId != null && parentId! > 0;
  void toggleExpand() => expand = !(expand ?? false);

  void updateChildren() {
    for (final child in children ?? <MenuRes>[]) {
      child.parentId = id;
      child.updateChildren();
    }
  }

  Iterable<MenuRes> get menus sync* {
    if (app == true) {
      yield this;
      if (expand == true) {
        for (final child in children ?? <MenuRes>[]) {
          if (child.app == true) {
            yield child;
          }
        }
      }
    }
  }
}

String _getNotoString(String htmlString) {
  return '''
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC&display=swap">
</head>
<style>
  body {
      margin: 0;
      font-family: 'Noto Sans TC', sans-serif;
      white-space: nowrap;
  }
</style>
<body>
  $htmlString
</body>
''';
}

extension StringX on String {
  String get toHtml => _getHtmlString(this);
  String get toNotoHtml => _getNotoString(this);
  String get withoutTag => replaceAll(RegExp(r'<[^>]*>'), '');
}

extension MembersMessagesUnreadResX on MembersMessagesUnreadRes {
  num get allUnreadCount {
    return (num.tryParse(activities ?? '') ?? 0) +
        (num.tryParse(orders ?? '') ?? 0) +
        (num.tryParse(ships ?? '') ?? 0) +
        (num.tryParse(questions ?? '') ?? 0);
  }
}

extension GlobalKeyX on GlobalKey {
  Offset getOffset({Offset offset = Offset.zero}) {
    final renderBox = currentContext?.findRenderObject() as RenderBox?;
    // final renderBox = currentContext?.findAncestorRenderObjectOfType() as RenderBox?;
    return renderBox?.localToGlobal(offset) ?? Offset.zero;
  }
}

extension DateTimeX on DateTime {
  int get dateAsInt => int.parse(DateFormat('yyyyMMdd').format(this));
  String toDateString() => DateFormat('yyyy-MM-dd').format(this);
  String toDateTimeString() => DateFormat('yyyy-MM-dd HH:mm:ss').format(this);
  DateTime get midnight => DateTime(year, month, day);
}

extension CartItemsResX on CartItemsRes {
  // normal items
  Iterable<Item> get normalItems {
    return (items ?? []).where((element) {
      // return !Constants.discountProduct.contains(element.productId);
      return element.isNormal;
    });
  }

  // 購物車商品數量
  num get numberOfProducts {
    return (items ?? <Item>[]).fold<num>(
      0,
      (previousValue, element) => previousValue + (element.cartQuantity ?? 0),
    );
  }

  // 購物車總金額
  num get cartAmount {
    return (items ?? <Item>[]).fold<num>(
      0,
      (previousValue, element) => previousValue + (element.subtotal ?? 0),
    );
  }
}

extension MembersProfilePatchReqX on MembersProfilePatchReq {
  void validate() {
    // if (fullname == null || fullname!.isEmpty) {
    //   throw '姓名是必填欄位';
    // }
  }

  bool get isSubscribeEpaper => subscribeEpaper == Switcher.on.value;
  set isSubscribeEpaper(bool value) {
    subscribeEpaper = value ? Switcher.on.value : Switcher.off.value;
  }

  DateTime? get birthdayAsDateTime {
    return DateTime.tryParse(birthday ?? '');
  }

  String get birthdayAsString {
    final dt = birthdayAsDateTime;
    if (dt == null) {
      return '';
    }
    return DateFormat('yyyy / MM / dd').format(birthdayAsDateTime!);
  }
}

extension MembersProfileResX on MembersProfileRes {
  MembersProfilePatchReq toDraft() {
    return MembersProfilePatchReq(
      subscribeEpaper: '$subscribeEpaper',
      gender: gender,
      birthday: birthday,
    );
  }

  DateTime? get birthdayAsDateTime {
    return DateTime.tryParse(birthday ?? '');
  }
}

extension IndexModuleAppResX on IndexModuleAppRes {
  IndexModuleType get typeEnum {
    return IndexModuleType.values.firstWhere(
      (e) => e.value == type,
      orElse: () => IndexModuleType.unknown,
    );
  }

  bool get dataIsMap => data is Map;
  bool get dataIsList => data is List;

  Iterable<IndexModuleApp> get dataList {
    return Iterable.castFrom(data).map((e) => IndexModuleApp.fromJson(e));
  }

  Map<String, Iterable<IndexModuleApp>> get dataMap {
    final map = data as Map;
    return map.map((key, value) {
      return MapEntry(
          key, (value as List).map((e) => IndexModuleApp.fromJson(e)));
    });
  }
}

extension IndexModuleAppX on IndexModuleApp {
  bool get hasPromotionPrice => promotionPrice != null && promotionPrice! >= 0;
  num get finalPrice {
    return hasPromotionPrice ? (promotionPrice ?? 0) : (price ?? 0);
  }

  ProductDetail toProductDetail() => ProductDetail.fromJson(toJson());
}

extension VoucherResX on VoucherRes {
  String get subTitle {
    Iterable<String> children() sync* {
      var needSeparator = false;
      if (threshold != null && threshold!.isNotEmpty) {
        yield threshold!;
        needSeparator = true;
      }
      if (name != null && name!.isNotEmpty) {
        if (needSeparator) {
          yield '\n';
        }
        yield name!;
      }
    }

    return children().join();
  }

  DateTime get expireAt => DateTime.tryParse(endAt ?? '') ?? DateTime.now();
  String get displayExpireAt {
    final local = expireAt.isUtc ? expireAt.toLocal() : expireAt;
    return DateFormat('yyyy-MM-dd HH:mm').format(local);
  }

  bool get isExpired => DateTime.now().isAfter(expireAt);
  bool get isAvailable => isUsed == false && isValid == true && !isExpired;
  bool get canTap => isAvailable && url != null;

  String get displayStatus {
    if (isUsed == true) {
      return '已使用';
    }
    if (isExpired) {
      return '已過期';
    }
    return '';
  }

  Color get backgroundColor {
    if (isAvailable) {
      return EfColors.gray;
    }
    return EfColors.grayLight;
  }

  Color get textColor {
    if (isAvailable) {
      return EfColors.gray55;
    }
    return EfColors.gray93;
  }
}

extension ScrollMetricsX on ScrollMetrics {
  bool get isAtTop => pixels <= 0;
  bool get isAtBottom => pixels >= maxScrollExtent;
}

extension ScrollControllerX on ScrollController {
  bool get isAtTop => position.isAtTop;
  bool get isAtBottom => position.isAtBottom;
  // 把 addListener 轉成 Stream
  Stream<void> watch() async* {
    final streamController = StreamController<void>();
    void callback() {
      streamController.add(null);
    }

    addListener(callback);
    yield* streamController.stream;
    removeListener(callback);
    streamController.close();
  }
}

extension AdvX on Adv {
  // String get imageUrl => image ?? '';
  // String get shareUrl {
  //   final regExp = new RegExp(r'<a href="(https://[^"]*)">');
  //   final matches = regExp.allMatches(mobileHtml ?? '');
  //   for (RegExpMatch match in matches) {
  //     String matchString = match.group(1);
  //     print(matchString);
  //   }
  // }
  // TODO: use iterable map
  Iterable<ProductDetail> get products sync* {
    final regex = RegExp(r'<td[^>]*>(.*?)<\/td>', multiLine: true);
    final matches = regex.allMatches(mobileHtml ?? '');
    for (RegExpMatch match in matches) {
      if (match.groupCount > 0) {
        final matchString = match.group(1);
        if (matchString != null) {
          yield ProductDetail(
            // 取得 href
            shareUrl: _getHref(matchString),
            // 取得 img src
            thumbnail: Thumbnail(src: _getImgSrc(matchString)),
          );
        }
      }
    }
  }

  String _getHref(String input) {
    final regex = RegExp(r'<a href="([^"]*)">');
    final matches = regex.allMatches(input);
    for (RegExpMatch match in matches) {
      if (match.groupCount > 0) {
        final matchString = match.group(1);
        if (matchString != null) {
          return matchString;
        }
      }
    }
    return '';
  }

  String _getImgSrc(String input) {
    final regex = RegExp(r'<img[^>]*src="([^"]*)"[^>]*>');
    final matches = regex.allMatches(input);
    for (RegExpMatch match in matches) {
      if (match.groupCount > 0) {
        final matchString = match.group(1);
        if (matchString != null) {
          return matchString;
        }
      }
    }
    return '';
  }

  TextStyle? get textStyle {
    final style = TextStyle(
      color: _getTitleColor(name ?? ''),
      fontWeight: _getStrong(name ?? ''),
    );
    return style;
  }

  Color? _getTitleColor(String input) {
    final regex = RegExp(r'<font[^>]*color=#?([a-fA-F0-9]{6})[^>]*>');
    final matches = regex.allMatches(input);
    for (RegExpMatch match in matches) {
      if (match.groupCount > 0) {
        final matchString = match.group(1);
        if (matchString != null) {
          // Remove the # character and convert the color code to an integer.
          final colorCode = int.parse(matchString, radix: 16);
          // Create a Color object and return it.
          return Color(colorCode + 0xFF000000);
        }
      }
    }
    return null;
  }

  FontWeight? _getStrong(String input) {
    final regex = RegExp(r'<(strong|b)>(.*?)</\1>');
    final matches = regex.allMatches(input);
    for (RegExpMatch match in matches) {
      if (match.groupCount > 0) {
        final matchString = match.group(1);
        if (matchString != null) {
          return FontWeight.bold;
        }
      }
    }
    return null;
  }
}

extension ConfigsResX on ConfigsRes {
  String get appVersionRequires {
    if (GetPlatform.isAndroid) {
      return appVersionAndroidRequires ?? '1.0.0';
    }
    if (GetPlatform.isIOS) {
      return appVersionIosRequires ?? '1.0.0';
    }
    return '1.0.0';
  }

  // 傳入目前版本號，判斷是否有新版本
  bool hasNewerVersion(String currentVersion) {
    try {
      final current = currentVersion.split('.').map(int.parse).toList();
      final latest = appVersionRequires.split('.').map(int.parse).toList();
      for (var i = 0; i < current.length; i++) {
        if (latest[i] > current[i]) {
          return true;
        } else if (current[i] > latest[i]) {
          return false;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}

extension ShippingEventX on ShippingEvent {
  bool get isAppEnable => enable == true && isApp == true;
}

extension AppX on App {
  bool get isVideo => type == 'video';
  // 由 type 取得 thumbnail
  Thumbnail get thumbnailByType {
    if (isVideo) {
      return _video;
    }
    return thumbnail ?? Thumbnail();
  }

  Thumbnail get _video {
    // RegExp regExp = RegExp(r'_(\d+)_(\d+)_');
    // Match? match = regExp.firstMatch(videoUrl ?? '');
    // if (match != null) {
    //   String width = match.group(1)!; // 提取寬度
    //   String height = match.group(2)!; // 提取高度
    //   return Thumbnail(
    //     src: videoUrl,
    //     width: num.tryParse(width),
    //     height: num.tryParse(height),
    //   );
    // }
    return Thumbnail(src: videoUrl);
  }
}

extension ErrorResX on ErrorRes {
  String get displayError => toString();
}

extension MembersMessagesShipX on MembersMessagesShip {
  num get numOfQuantity {
    return (num.tryParse(quantity ?? '') ?? 0);
  }
}

extension MembersPopupResX on MembersPopupRes {
  String get htmlString {
    return '''
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC&display=swap">
  <head>
  <style>
  body {
    margin: 0;
    padding: 0 8px;
    font-family: 'Noto Sans TC', sans-serif;
    display: flex;
    justify-content: center;
  }
  </style>
  <body>
    $popupTitle
  </body>
</html>
    ''';
  }
}

extension AppierNotificationX on AppierNotification {
  MembersMessagesActivity toMembersMessagesActivity() {
    return MembersMessagesActivity(
      title: title,
      url: Url(url: deepLink),
      thumbnail: Thumbnail(src: imageUrl),
      createDatetime: qgDateTime.toDateTimeString(),
    );
  }

  DateTime get androidTimestamp {
    return DateTime.tryParse(qgTimestamp ?? '') ?? DateTime.now();
  }

  DateTime get iosTimestamp {
    final millisecondsSinceEpoch = int.tryParse(qgts ?? '') ?? 0;
    return DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch * 1000);
  }

  DateTime get qgDateTime {
    if (Platform.isAndroid) {
      return androidTimestamp;
    }
    if (Platform.isIOS) {
      return iosTimestamp;
    }
    return DateTime.now();
  }

  Iterable<String> get deepLinks sync* {
    if (deepLink != null && deepLink!.isNotEmpty) {
      yield deepLink!;
    }
    if (actions != null && actions!.isNotEmpty) {
      for (final action in actions!) {
        final deepLink = action.deepLink;
        if (deepLink != null && deepLink.isNotEmpty) {
          yield deepLink;
        }
      }
    }
    // slider 的 deepLink - Android Only
    if (slider != null && slider!.isNotEmpty) {
      for (final slide in slider!) {
        final deepLink = slide.deepLink;
        if (deepLink != null && deepLink.isNotEmpty) {
          yield deepLink;
        }
      }
    }
    // custom(slider or carousel) 的 deepLink - iOS Only
    if (qgPush?.custom != null) {
      final data = qgPush?.custom?.data ?? [];
      for (final push in data) {
        final deepLink = push.deepLink;
        if (deepLink != null && deepLink.isNotEmpty) {
          yield deepLink;
        }
      }
    }
    // carousel 的 deepLink - Android Only
    if (carousel != null && carousel!.isNotEmpty) {
      for (final slide in carousel!) {
        final deepLink = slide.deepLink;
        if (deepLink != null && deepLink.isNotEmpty) {
          yield deepLink;
        }
      }
    }
  }
}

extension LoginResX on LoginRes {
  // String? get email => payload?.email;
  // String? get fullname => payload?.fullname;
  // String? get provider => payload?.provider;
  // String? get session => payload?.session;
  // String? get userid => payload?.userid;

  JwtRes? get jwtRes {
    final decodedToken = JwtDecoder.tryDecode(token ?? '');
    return JwtRes.fromJson(decodedToken ?? {});
    // try {
    //   final decodedToken = JwtDecoder.decode(token!);
    //   return JwtRes.fromJson(decodedToken);
    // } catch (e) {
    //   return null;
    // }
  }

  bool get isAvailable => !isExpired;
  bool get isExpired => token == null || JwtDecoder.isExpired(token!);
  DateTime get expirationDate => JwtDecoder.getExpirationDate(token!);
  Duration get tokenTime => JwtDecoder.getTokenTime(token!);
  Duration get remainingTime => JwtDecoder.getRemainingTime(token!);
}

extension JwtResX on JwtRes {
  DateTime? get birthdayAsDateTime {
    return DateTime.tryParse(birthday ?? '');
  }
}

extension PayloadX on Payload {
  DateTime? get birthdayAsDateTime {
    return DateTime.tryParse(birthday ?? '');
  }
}

extension ItemX on Item {
  String get prefixNumber {
    final number = productNumber ?? '';
    final index = number.indexOf('-');
    if (index >= 0) {
      return number.substring(0, index);
    }
    return number;
  }

  String get postfixNumber {
    final number = productNumber ?? '';
    final index = number.indexOf('-');
    if (index >= 0) {
      return number.substring(index + 1);
    }
    return '';
  }

  AnalyticsEventItem toAnalyticsEventItem() {
    return AnalyticsEventItem(
      // affiliation: order.affiliateOrderType,
      currency: 'TWD',
      // coupon: order.couponCode,
      // creativeName: productDetail.topCategoryName,
      // creativeSlot: productDetail.categoryName,
      // discount: product.discount,
      // index: product.index,
      // itemBrand: productDetail.brandName,
      // itemCategory: topCategoryName,
      // itemCategory2: categoryName,
      // itemCategory3: subCategoryName,
      // itemCategory4: subSubCategoryName,
      // itemCategory5: subSubSubCategoryName,
      itemId: productNumber,
      // itemListId: productDetail.topCategoryName,
      // itemListName: productDetail.categoryName,
      itemName: name,
      itemVariant: specName,
      // locationId: product.locationId,
      price: price,
      promotionId: promotionId != null ? '$promotionId' : null,
      promotionName: promotionName,
      quantity: cartQuantity,
      // parameters:
    );
  }

  bool get isNormal => !isDiscount;
  bool get isDiscount {
    if (numOfProductId <= Constants.maxOfDiscountProductId) {
      return true;
    }
    // if (Constants.discountProduct.contains('$productId')) {
    //   return true;
    // }
    return false;
  }

  num get numOfProductId => productId ?? 0;
}

extension ProductsCommentsResX on ProductsCommentsRes {
  String get simpleOrderNumber {
    final input = orderNumber ?? '';
    return input.length > 4 ? input.substring(4) : input;
  }
}

extension ThresholdX on promotion.Threshold {
  num get numOfQuantity {
    return (num.tryParse(quantity ?? '') ?? 0);
  }
}

extension PromotionX on promotion.Promotion {
  Iterable<promotion.Threshold> get nnThresholds => thresholds ?? [];
  bool get hasThreshold => nnThresholds.isNotEmpty;
  Iterable<String> get displayThresholds {
    return nnThresholds.map((e) {
      return '買${e.quantity ?? ''}送${e.gift ?? ''}';
    });
  }

  String get displayThreshold => displayThresholds.join('・');
  bool get isHtml => smallTitle?.contains('<') == true;
}
