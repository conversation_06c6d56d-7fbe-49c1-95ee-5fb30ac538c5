import 'package:logger/logger.dart';
import 'package:talker_flutter/talker_flutter.dart';

class TalkerOutput extends LogOutput {
  final Talker talker;
  TalkerOutput(this.talker);

  @override
  void output(OutputEvent event) {
    final logEvent = event.origin;
    // final type = TalkerLogType.fromLogLevel(logEvent.level.logLevel);
    // final data = TalkerLog(
    //   '${logEvent.message}',
    //   key: type.key,
    // );
    talker.log(
      logEvent.message,
      logLevel: logEvent.level.logLevel,
    );
  }
}

extension LevelX on Level {
  LogLevel get logLevel {
    switch (this) {
      case Level.error:
        return LogLevel.error;
      // case Level.critical:
      //   return LogLevel.critical;
      case Level.info:
        return LogLevel.info;
      case Level.debug:
        return LogLevel.debug;
      case Level.verbose:
        return LogLevel.verbose;
      case Level.warning:
        return LogLevel.warning;
      default:
        return LogLevel.critical;
    }
  }
}
