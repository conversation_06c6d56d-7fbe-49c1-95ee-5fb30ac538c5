class Keys {
  static const status = 'status';
  static const userDefault = 'user_default';
  static const tableSettings = 'table_settings';
  static const loginRes = 'login_res';
  static const token = 'token';
  static const code = 'code';
  static const bbCode = 'bb_code';
  static const email = 'email';
  static const fullName = 'full_name';
  static const url = 'url';
  static const urlApp = 'url_app';
  static const title = 'title';
  static const accessToken = 'access_token';
  static const error = 'error';
  static const errorCode = 'error_code';
  static const errorDescription = 'error_description';
  static const errorReason = 'error_reason';
  static const boxSetting = 'box_setting';
  static const action = 'action';
  static const id = 'id';
  static const parentId = 'parent_id';
  static const type = 'type';
  static const data = 'data';
  static const profile = 'profile';
  static const headers = 'headers';
  static const avatar = 'avatar';
  static const fcmToken = 'fcm_token';
  static const apnsToken = 'apns_token';
  static const showEmptyCart = r'showEmptyCart';
  static const cartFinish = r'CartFinish';
  static const cartPayment = r'cart_payment';
  static const cartCheckout = r'cart_checkout';
  static const success = r'success';
  static const fail = r'fail';
  static const showProduct = r'showProduct';
  static const history = 'history';
  static const sale = 'SALE';
  static const home = '首頁';
  static const index = 'index';
  static const configs = 'configs';
  static const promotion = 'promotion';
  static const display = 'display';
  static const appierAuthCode = 'appier_auth_code';
  static const mid = 'mid';
  static const uid = 'uid';
  static const cookies = 'cookies';
  static const transactionReserveId = 'transactionReserveId';
  static const orderId = 'order_id';
  static const orderStatus = 'order_status';
  static const deepLink = 'deepLink';
  static const message = 'message';
  static const source = 'source';
  static const category = 'category';
  static const page = 'page';
  static const welcomeMessageLastTriggered = 'welcome_message_last_triggered';
}
