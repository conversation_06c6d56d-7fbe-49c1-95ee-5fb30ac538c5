// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions getCurrentPlatform(bool sandbox) {
    if (kIsWeb) {
      return web;
    }
    // ignore: missing_enum_constant_in_switch
    if (defaultTargetPlatform == TargetPlatform.android) {
      return android;
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      return ios;
    } else if (defaultTargetPlatform == TargetPlatform.macOS) {
      return macos;
    }
    throw UnsupportedError(
      'DefaultFirebaseOptions are not supported for this platform.',
    );
  }

  static const _apiKey = 'AIzaSyBBHxrPgMx7mYJe3uRnpA-3GXUmNtfxomw';
  static const _projectId = 'tw-efshop';
  static const _messagingSenderId = '498175649962';

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: _apiKey,
    appId: '',
    messagingSenderId: _messagingSenderId,
    projectId: _projectId,
    // authDomain: 'xxxxxxxxxxxxxxxxxxx',
    // databaseURL: 'xxxxxxxxxxxxxxxxxxx',
    // storageBucket: 'xxxxxxxxxxxxxxxxxxx',
    // measurementId: 'xxxxxxxxxxxxxxxxxxx',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: _apiKey,
    appId: '1:498175649962:android:a7cffea83a17fc297d88d3',
    messagingSenderId: _messagingSenderId,
    projectId: _projectId,
    // databaseURL: 'xxxxxxxxxxxxxxxxxxx',
    // storageBucket: 'xxxxxxxxxxxxxxxxxxx',
  );

  static const FirebaseOptions androidSandbox = FirebaseOptions(
    apiKey: _apiKey,
    appId: '1:498175649962:android:90908cf1d6b37491',
    messagingSenderId: _messagingSenderId,
    projectId: _projectId,
    // databaseURL: 'xxxxxxxxxxxxxxxxxxx',
    // storageBucket: 'xxxxxxxxxxxxxxxxxxx',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAsFqEGuSsdnMPzrLHzRC8Gw5n_an49OJ4',
    appId: '1:498175649962:ios:9bcefbb5aa0ac1577d88d3',
    messagingSenderId: _messagingSenderId,
    projectId: _projectId,
    databaseURL: 'https://tw-efshop.firebaseio.com',
    storageBucket: 'tw-efshop.appspot.com',
    androidClientId: '498175649962-u4g098hun1f0m7pkgl57cnpvu721bkpf.apps.googleusercontent.com',
    iosClientId: '498175649962-7van445ajpn5ru63upq55ppl720jb6sa.apps.googleusercontent.com',
    iosBundleId: 'com.shangching.com.shangching.efshop',
    appGroupId: 'group.com.shangching.com.shangching.efshop.notification',
  );

  static const FirebaseOptions iosSandbox = FirebaseOptions(
    apiKey: 'AIzaSyAsFqEGuSsdnMPzrLHzRC8Gw5n_an49OJ4',
    appId: '1:498175649962:ios:ebe164a8b10f5ae3',
    messagingSenderId: _messagingSenderId,
    projectId: _projectId,
    databaseURL: 'https://tw-efshop.firebaseio.com',
    storageBucket: 'tw-efshop.appspot.com',
    androidClientId: '498175649962-u4g098hun1f0m7pkgl57cnpvu721bkpf.apps.googleusercontent.com',
    iosClientId: '498175649962-qj0gag5qss60v9rghbi1jiupuptve1ot.apps.googleusercontent.com',
    iosBundleId: 'tw.com.efshop.efapptest',
    appGroupId: 'group.com.shangching.com.shangching.efshop.notification',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: _apiKey,
    appId: '',
    messagingSenderId: _messagingSenderId,
    projectId: _projectId,
    // databaseURL: 'xxxxxxxxxxxxxxxxxxx',
    // storageBucket: 'xxxxxxxxxxxxxxxxxxx',
    // androidClientId: 'xxxxxxxxxxxxxxxxxxx',
    // iosClientId: 'xxxxxxxxxxxxxxxxxxx',
    // iosBundleId: 'xxxxxxxxxxxxxxxxxxx',
  );
}
