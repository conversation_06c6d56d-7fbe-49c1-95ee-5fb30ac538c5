// 排序欄位
import 'app/routes/app_pages.dart';

enum SortField {
  soldTotal, // 銷售量
  createDate, // 建立日期
  price, // 價格
}

extension SortFieldX on SortField {
  String get name {
    switch (this) {
      case SortField.soldTotal:
        return '銷售量';
      case SortField.createDate:
        return '建立日期';
      case SortField.price:
        return '價格';
    }
  }

  String get value {
    switch (this) {
      case SortField.soldTotal:
        return 'sold_total';
      case SortField.createDate:
        return 'create_date';
      case SortField.price:
        return 'price';
    }
  }
}

// 排序方法
enum SortWay {
  asc, // 升冪
  desc, // 降冪
}

extension SortWayX on SortWay {
  String get name {
    switch (this) {
      case SortWay.asc:
        return '升冪';
      case SortWay.desc:
        return '降冪';
    }
  }

  String get value {
    switch (this) {
      case SortWay.asc:
        return 'asc';
      case SortWay.desc:
        return 'desc';
    }
  }
}

// 付款方式
enum PaymentId {
  eshop,
  fmeshop,
  cod,
  hitrust,
}

extension PaymentIdX on PaymentId {
  String get name {
    switch (this) {
      case PaymentId.eshop:
        return '信用卡';
      case PaymentId.fmeshop:
        return 'FamiPay';
      case PaymentId.cod:
        return '貨到付款';
      case PaymentId.hitrust:
        return '信用卡';
    }
  }

  String get value {
    switch (this) {
      case PaymentId.eshop:
        return '7eshop';
      case PaymentId.fmeshop:
        return 'fmeshop';
      case PaymentId.cod:
        return 'cod';
      case PaymentId.hitrust:
        return 'hitrust';
    }
  }
}

// 裝置類別
enum DeviceType {
  undefined,
  android,
  ios,
}

extension DeviceTypeX on DeviceType {
  String get display {
    switch (this) {
      case DeviceType.android:
        return 'Android';
      case DeviceType.ios:
        return 'iOS';
      case DeviceType.undefined:
        return '未定義';
    }
  }

  String get value {
    switch (this) {
      case DeviceType.android:
        return 'android';
      case DeviceType.ios:
        return 'ios';
      case DeviceType.undefined:
        return 'undefined';
    }
  }
}

// 詢問類別
// 1. 查詢出貨進度
// 2. 退貨與退款
// 3. 瑕疵/寄錯/短少
// 4. 換貨 (色/款/尺寸) (已不提供新增，但舊資料可能會有)
// 5. 其他
// 6. 修改收件資料

enum QuestionType {
  shipping, // 查詢出貨進度
  refund, // 退貨與退款
  defective, // 瑕疵/寄錯/短少
  exchange, // 換貨 (色/款/尺寸) (已不提供新增，但舊資料可能會有)
  other, // 其他
  modify, // 修改收件資料
}

extension QuestionTypeX on QuestionType {
  String get display {
    switch (this) {
      case QuestionType.shipping:
        return '查詢出貨進度';
      case QuestionType.refund:
        return '退貨與退款';
      case QuestionType.defective:
        return '瑕疵/寄錯/短少';
      case QuestionType.exchange:
        return '換貨';
      case QuestionType.other:
        return '其他';
      case QuestionType.modify:
        return '修改收件資料';
    }
  }

  int get value {
    switch (this) {
      case QuestionType.shipping:
        return 1;
      case QuestionType.refund:
        return 2;
      case QuestionType.defective:
        return 3;
      case QuestionType.exchange:
        return 4;
      case QuestionType.other:
        return 5;
      case QuestionType.modify:
        return 6;
    }
  }
}

// 退貨管道
// enum ShippingMethod {
//   sevenEleven, // 7-11
//   familyMart, // 全家
// }

// extension ShippingMethodX on ShippingMethod {
//   String get display {
//     switch (this) {
//       case ShippingMethod.sevenEleven:
//         return '7-11';
//       case ShippingMethod.familyMart:
//         return '全家';
//     }
//   }

//   String get value {
//     switch (this) {
//       case ShippingMethod.sevenEleven:
//         return '7return';
//       case ShippingMethod.familyMart:
//         return 'fmreturn';
//     }
//   }
// }

// 地址類別
enum AddressType {
  home, // 家
  sevenEleven, // 7-11
  familyMart, // 全家
}

const superMarketList = [
  AddressType.sevenEleven,
  AddressType.familyMart,
];

extension AddressTypeX on AddressType {
  String get display {
    switch (this) {
      case AddressType.home:
        return '宅配';
      case AddressType.sevenEleven:
        return '7-11';
      case AddressType.familyMart:
        return '全家';
    }
  }

  String get title {
    switch (this) {
      case AddressType.home:
        return '收貨地址';
      case AddressType.sevenEleven:
        return '7-11門市';
      case AddressType.familyMart:
        return '全家門市';
    }
  }

  String get value {
    switch (this) {
      case AddressType.home:
        return 'home';
      case AddressType.sevenEleven:
        return 'seven_eleven';
      case AddressType.familyMart:
        return 'family_mart';
    }
  }

  String get icon {
    switch (this) {
      case AddressType.home:
        return '';
      case AddressType.sevenEleven:
        return 'assets/images/seven_eleven.png';
      case AddressType.familyMart:
        return 'assets/images/family_mart.png';
    }
  }
}

// 開關
enum SwitchStatus {
  off,
  on,
}

extension SwitchStatusX on SwitchStatus {
  String get name {
    switch (this) {
      case SwitchStatus.on:
        return '開啟';
      case SwitchStatus.off:
        return '關閉';
    }
  }

  String get value {
    switch (this) {
      case SwitchStatus.on:
        return 'on';
      case SwitchStatus.off:
        return 'off';
    }
  }

  bool get isOn => this == SwitchStatus.on;
  bool get isOff => this == SwitchStatus.off;
}

// 訊息類型
enum MessageType {
  activities, // 活動
  orders, // 訂單
  ships, // 物流
  questions, // 客服
  coupon, // 優惠券
}

extension MessageTypeX on MessageType {
  String get display {
    switch (this) {
      case MessageType.activities:
        return '優惠活動';
      case MessageType.orders:
        return '通知消息';
      case MessageType.ships:
        return '物流通知';
      case MessageType.questions:
        return '客服紀錄';
      case MessageType.coupon:
        return '會員好禮';
    }
  }

  String get value {
    switch (this) {
      case MessageType.activities:
        return 'activities';
      case MessageType.orders:
        return 'orders';
      case MessageType.ships:
        return 'ships';
      case MessageType.questions:
        return 'questions';
      case MessageType.coupon:
        return 'coupon';
    }
  }

  String get iconPath {
    switch (this) {
      case MessageType.activities:
        return 'assets/images/msg_promotion.svg';
      case MessageType.orders:
        return 'assets/images/msg_notification.svg';
      case MessageType.ships:
        return 'assets/images/msg_logistic.svg';
      case MessageType.questions:
        return 'assets/images/msg_service.svg';
      case MessageType.coupon:
        return 'assets/images/msg_promotion_ex.svg';
    }
  }

  String get path {
    switch (this) {
      case MessageType.activities:
        return '/activity';
      case MessageType.orders:
        return '/notification';
      case MessageType.ships:
        return '/logistic';
      case MessageType.questions:
        return '/customer-service';
      case MessageType.coupon:
        return '/member_push_voucher';
    }
  }

  String get subtitle {
    switch (this) {
      case MessageType.activities:
        // return '【本週限定\$260】特級彈性窄管涼感褲';
        return '';
      case MessageType.orders:
        return '系統通知';
      case MessageType.ships:
        return '物流狀態及時通知';
      case MessageType.questions:
        return '查看與客服的溝通紀錄';
      case MessageType.coupon:
        return '優惠券';
    }
  }
}

enum Button {
  cancel,
  confirm,
}

extension ButtonX on Button {
  String get display {
    switch (this) {
      case Button.confirm:
        return '確認';
      case Button.cancel:
        return '取消';
    }
  }

  bool get value {
    switch (this) {
      case Button.confirm:
        return true;
      case Button.cancel:
        return false;
    }
  }
}

enum SubRouteType {
  signIn,
  product,
}

enum CrudType {
  create,
  read,
  update,
  delete,
}

extension CrudTypeX on CrudType {
  String get display {
    switch (this) {
      case CrudType.create:
        return '新增';
      case CrudType.read:
        return '查詢';
      case CrudType.update:
        return '編輯';
      case CrudType.delete:
        return '刪除';
    }
  }

  // String get value => name;

  // String get value {
  //   switch (this) {
  //     case CrudType.create:
  //       return 'create';
  //     case CrudType.read:
  //       return 'read';
  //     case CrudType.update:
  //       return 'update';
  //     case CrudType.delete:
  //       return 'delete';
  //   }
  // }
}

enum AddressTab {
  city,
  town,
  road,
  store,
}

extension AddressTabX on AddressTab {
  String get display {
    switch (this) {
      case AddressTab.city:
        return '縣市';
      case AddressTab.town:
        return '鄉鎮市區';
      case AddressTab.road:
        return '路段';
      case AddressTab.store:
        return '門市';
    }
  }

  // String get value {
  //   switch (this) {
  //     case AddressTab.city:
  //       return 'city';
  //     case AddressTab.town:
  //       return 'town';
  //     case AddressTab.road:
  //       return 'road';
  //     case AddressTab.store:
  //       return 'store';
  //   }
  // }
}

enum OrderProcess {
  created, // 訂單成立
  processing, // 處理中
  shipped, // 已出貨
  delivering, // 派送中
  arrived, // 送達門市
  success, // 派送成功
  cancel, // 已取消
}

extension OrderProcessX on OrderProcess {
  String get display {
    switch (this) {
      case OrderProcess.created:
        return '訂單成立';
      case OrderProcess.processing:
        return '處理中';
      case OrderProcess.shipped:
        return '已出貨';
      case OrderProcess.delivering:
        return '派送中';
      case OrderProcess.arrived:
        return '送達門市';
      case OrderProcess.success:
        return '派送成功';
      case OrderProcess.cancel:
        return '已取消';
    }
  }
}

enum OrderStatusRaw {
  creditCardFailed, // 信用卡未過
  orderEstablished, // 訂單成立
  processing, // 處理中
  pendingShipment, // 待出貨
  cancelled, // 取消
  shipped, // 已出貨
  awaitingSignature, // 待簽收
  arrivedAtStore, // 已到店
  pickedUp, // 已取貨
  awaitingRating, // 待評價
  returned, // 退貨
  closed, // 結案
  other, // 其他
}

extension OrderStatusRawX on OrderStatusRaw {
  String get value {
    switch (this) {
      case OrderStatusRaw.creditCardFailed:
        return '信用卡未過';
      case OrderStatusRaw.orderEstablished:
        return '訂單成立';
      case OrderStatusRaw.processing:
        return '處理中';
      case OrderStatusRaw.pendingShipment:
        return '待出貨';
      case OrderStatusRaw.cancelled:
        return '取消';
      case OrderStatusRaw.shipped:
        return '已出貨';
      case OrderStatusRaw.awaitingSignature:
        return '待簽收';
      case OrderStatusRaw.arrivedAtStore:
        return '已到店';
      case OrderStatusRaw.pickedUp:
        return '已取貨';
      case OrderStatusRaw.awaitingRating:
        return '待評價';
      case OrderStatusRaw.returned:
        return '退貨';
      case OrderStatusRaw.closed:
        return '結案';
      case OrderStatusRaw.other:
        return '其他';
      default:
        return '';
    }
  }
}

enum OrderStatus {
  padding, // 待出貨
  shipped, // 已出貨
  signed, // 待簽收
  refund, // 退貨退款
  rated, // 待評價
  all, // 全部
}

extension OrderStatusX on OrderStatus {
  String get display {
    switch (this) {
      case OrderStatus.padding:
        return '待出貨';
      case OrderStatus.shipped:
        return '已出貨';
      case OrderStatus.signed:
        return '待簽收';
      case OrderStatus.rated:
        return '待評價';
      case OrderStatus.refund:
        return '退貨退款';
      case OrderStatus.all:
        return '全部';
    }
  }

  ///
  /// 顯示訂單狀態列表
  ///
  Iterable<String> get displayList {
    switch (this) {
      case OrderStatus.padding:
        return [
          OrderStatusRaw.orderEstablished.value, // 訂單成立
          OrderStatusRaw.processing.value, // 處理中
          OrderStatusRaw.pendingShipment.value, // 待出貨
        ];
      case OrderStatus.shipped:
        return [
          OrderStatusRaw.shipped.value, // 已出貨
        ];
      case OrderStatus.signed:
        return [
          OrderStatusRaw.arrivedAtStore.value, // 已到店
          OrderStatusRaw.awaitingSignature.value, // 待簽收
        ];
      case OrderStatus.rated:
        return [
          OrderStatusRaw.pickedUp.value, // 已取貨
          OrderStatusRaw.awaitingRating.value, // 待評價
        ];
      case OrderStatus.refund:
        return [
          OrderStatusRaw.returned.value, // 退貨
        ];
      case OrderStatus.all:
        return [];
    }
  }
}

enum IndexTab {
  home,
  category,
  service,
  profile,
  // favorite,
  cart,
  empty,
  cartFinish,
  appCartFinish,
  showEmptyCart,
}

extension IndexTabX on IndexTab {
  String get value {
    switch (this) {
      case IndexTab.home:
        return Routes.HOME;
      case IndexTab.category:
        return Routes.CATEGORY_TOP;
      case IndexTab.service:
        return Routes.CUSTOMER_SERVICE;
      case IndexTab.profile:
        return Routes.PROFILE;
      // case IndexTab.favorite:
      //   return 'favorite';
      case IndexTab.cart:
        return Routes.CART;
      case IndexTab.empty:
        return Routes.EMPTY;
      case IndexTab.cartFinish:
        return Routes.CART_FINISH;
      case IndexTab.appCartFinish:
        return Routes.APP_CART_FINISH;
      case IndexTab.showEmptyCart:
        return Routes.SHOW_EMPTY_CART;
    }
  }
}

enum Boxes {
  address,
  city,
  town,
  road,
  favorite,
  setting,
  preorder,
  faq,
  order,
  activity,
  bank,
  notification,
  category, // 分類
  banner, // 首頁廣告
  productDetail, // 商品詳情
  productSeries, // 商品系列
  productComment, // 商品評論
  productCollocation, // 商品搭配
  categorySeo, // Category SEO
  advsCategories, // 分類廣告
  indexModule, // 首頁模組
  cart, // 購物車
  log, // 日誌
}

enum BadgeVisibility {
  hide,
  show,
  auto,
}

// 發票載具：0 無載具, 1 手機條碼, 2 自然人, 3 愛心碼, 4 悠遊卡, 5 會員
enum CarrierType {
  none, // 0: 無載具
  mobile, // 1: 手機條碼
  natural, // 2: 自然人
  love, // 3: 愛心碼
  easycard, // 4: 悠遊卡
  member, // 5: 會員
}

extension CarrierTypeX on CarrierType {
  String get display {
    switch (this) {
      case CarrierType.none:
        return '無載具';
      case CarrierType.mobile:
        return '手機條碼';
      case CarrierType.natural:
        return '自然人';
      case CarrierType.love:
        return '愛心碼';
      case CarrierType.easycard:
        return '悠遊卡';
      case CarrierType.member:
        return '會員';
    }
  }
}

enum PaymentType {
  hitrust,
  eshop,
  fmeshop,
  cod,
  linepay, // line-pay
}

extension PaymentTypeX on PaymentType {
  String get display {
    switch (this) {
      case PaymentType.hitrust:
        return '信用卡付款';
      case PaymentType.eshop:
        return '7-11取貨付款';
      case PaymentType.fmeshop:
        return '全家取貨付款';
      case PaymentType.cod:
        return '貨到付款';
      case PaymentType.linepay:
        return 'LINE Pay';
    }
  }
}

// 付款方式: 信用卡付款, 7-11取貨付款, 全家取貨付款, 貨到付款
enum PaymentName {
  creditCard,
  sevenEleven,
  familyMart,
  cod,
  linePay,
}

extension PaymentNameX on PaymentName {
  String get value {
    switch (this) {
      case PaymentName.creditCard:
        return '信用卡付款';
      case PaymentName.sevenEleven:
        return '7-11取貨付款';
      case PaymentName.familyMart:
        return '全家取貨付款';
      case PaymentName.cod:
        return '貨到付款';
      case PaymentName.linePay:
        return 'LINE Pay';
    }
  }
}

// 付款狀態：待付款、已付款、付款失敗 (僅針對刷卡)
enum PaymentStatus {
  pending,
  payup,
  fail,
}

extension PaymentStatusX on PaymentStatus {
  String get display {
    switch (this) {
      case PaymentStatus.pending:
        return '待付款';
      case PaymentStatus.payup:
        return '已付款';
      case PaymentStatus.fail:
        return '付款失敗';
    }
  }
}

// 退貨原因
enum RefundReason {
  none,
  notExpect, // 6: 商品不符合期待
  wrong, // 5: 買錯、重複下單
  sizeBig, // 1: 尺寸太大
  sizeSmall, // 2: 尺寸太小
  color, // 3: 顏色不喜歡
  defective, // 4: 收到瑕疵商品/短缺
  price, // 7: 商品價格問題
}

extension RefundReasonX on RefundReason {
  String get display {
    switch (this) {
      case RefundReason.none:
        return '請選擇';
      case RefundReason.sizeBig:
        return '尺寸太大';
      case RefundReason.sizeSmall:
        return '尺寸太小';
      case RefundReason.color:
        return '顏色不喜歡';
      case RefundReason.defective:
        return '收到瑕疵商品/短缺';
      case RefundReason.wrong:
        return '買錯、重複下單';
      case RefundReason.notExpect:
        return '商品不符合期待';
      case RefundReason.price:
        return '商品價格問題';
    }
  }

  num get value {
    switch (this) {
      case RefundReason.none:
        return 0;
      case RefundReason.sizeBig:
        return 1;
      case RefundReason.sizeSmall:
        return 2;
      case RefundReason.color:
        return 3;
      case RefundReason.defective:
        return 4;
      case RefundReason.wrong:
        return 5;
      case RefundReason.notExpect:
        return 6;
      case RefundReason.price:
        return 7;
    }
  }
}

enum InvoiceType {
  none, // 不開立
  personal, // 個人
  company, // 公司
  carrier, // 載具
  loveCode, // 捐贈
}

extension InvoiceTypeX on InvoiceType {
  String get display {
    switch (this) {
      case InvoiceType.none:
        return '不開立';
      case InvoiceType.personal:
        return '個人-電子發票';
      case InvoiceType.company:
        return '公司-統一編號';
      case InvoiceType.carrier:
        return '個人-手機條碼';
      case InvoiceType.loveCode:
        return '捐贈發票';
    }
  }
}

enum DiscountProduct {
  point, // 點數
  promotion, // 活動
  coupon, // 優惠券
}

extension DiscountProductX on DiscountProduct {
  String get display {
    switch (this) {
      case DiscountProduct.point:
        return '點數';
      case DiscountProduct.promotion:
        return '活動';
      case DiscountProduct.coupon:
        return '優惠券';
    }
  }

  String get value {
    switch (this) {
      case DiscountProduct.point:
        return '4002';
      case DiscountProduct.promotion:
        return '4000';
      case DiscountProduct.coupon:
        return ''; // TODO: 優惠券代碼
    }
  }
}

enum CartTab {
  webView, // 網頁購物車
  empty, // 空購物車
  orderSuccess, // 訂單成功
  orderFailed, // 訂單失敗
  category, // 分類
}

enum Switcher {
  off,
  on,
}

extension SwitcherX on Switcher {
  String get display {
    switch (this) {
      case Switcher.off:
        return '關閉';
      case Switcher.on:
        return '開啟';
    }
  }

  String get value {
    switch (this) {
      case Switcher.off:
        return '0';
      case Switcher.on:
        return '1';
    }
  }

  bool get isOn => this == Switcher.on;
  bool get isOff => this == Switcher.off;
}

enum Gender {
  female, // 0
  male, // 1
  none, // 2
}

extension GenderX on Gender {
  String get display {
    switch (this) {
      case Gender.none:
        return '請選擇';
      case Gender.male:
        return '先生';
      case Gender.female:
        return '小姐';
    }
  }

  bool get isFemale => this == Gender.female;
  bool get isMale => this == Gender.male;
  String get value => index.toString();
}

enum IndexModuleType {
  textUrl,
  slideGrids,
  slideGridsProducts,
  dynamicGridOne,
  dynamicGridTwo,
  dynamicGridThree,
  dynamicGridFour,
  banners,
  twoDimensions,
  html,
  unknown,
}

extension IndexModuleTypeX on IndexModuleType {
  String get value {
    switch (this) {
      case IndexModuleType.textUrl:
        return 'text_url';
      case IndexModuleType.slideGrids:
        return 'slide_grids';
      case IndexModuleType.slideGridsProducts:
        return 'slide_grids_products';
      case IndexModuleType.dynamicGridOne:
        return 'dynamic_grid_one';
      case IndexModuleType.dynamicGridTwo:
        return 'dynamic_grid_two';
      case IndexModuleType.dynamicGridThree:
        return 'dynamic_grid_three';
      case IndexModuleType.dynamicGridFour:
        return 'dynamic_grid_four';
      case IndexModuleType.banners:
        return 'banners';
      case IndexModuleType.twoDimensions:
        return 'two_dimensions';
      case IndexModuleType.html:
        return 'html';
      case IndexModuleType.unknown:
        return 'unknown';
    }
  }
}

enum VoucherStage {
  app,
  mobile,
}

// --- 訂單成立 ---
// 7-11現貨待出貨
// 全家現貨待出貨
// 信用卡待出貨
// 一般 待出貨
// --- 處理中 ---
// 7-11打單完畢
// 全家打單完畢
// 宅配打單完畢
// --- 已出貨 ---
// 7-11已上傳
// 全家已上傳
// 宅配已出貨
// --- 派送中 ---
// 7-11 已到大智通
// 全家已到DC
// 宅配已出貨(隔日)
// --- 送達門市 ---
// 7-11商品送達門市
// 全家商品送達門市
// 宅配已送達
enum OrderTagName {
  sevenElevenPending, // 7-11現貨待出貨
  familyMartPending, // 全家現貨待出貨
  creditCardPending, // 信用卡待出貨
  pending, // 一般 待出貨
  sevenElevenProcessing, // 7-11打單完畢
  familyMartProcessing, // 全家打單完畢
  homeProcessing, // 宅配打單完畢
  sevenElevenShipped, // 7-11已上傳
  familyMartShipped, // 全家已上傳
  homeShipped, // 宅配已出貨
  sevenElevenDelivering, // 7-11 已到大智通
  familyMartDelivering, // 全家已到DC
  homeDelivering, // 宅配已出貨
  sevenElevenArrived, // 7-11商品送達門市
  familyMartArrived, // 全家商品送達門市
  homeArrived, // 宅配已送達
}

extension OrderTagNameX on OrderTagName {
  String get display {
    switch (this) {
      case OrderTagName.sevenElevenPending:
        return '7-11現貨待出貨';
      case OrderTagName.familyMartPending:
        return '全家現貨待出貨';
      case OrderTagName.creditCardPending:
        return '信用卡待出貨';
      case OrderTagName.pending:
        return '一般 待出貨';
      case OrderTagName.sevenElevenProcessing:
        return '7-11打單完畢';
      case OrderTagName.familyMartProcessing:
        return '全家打單完畢';
      case OrderTagName.homeProcessing:
        return '宅配打單完畢';
      case OrderTagName.sevenElevenShipped:
        return '7-11已上傳';
      case OrderTagName.familyMartShipped:
        return '全家已上傳';
      case OrderTagName.homeShipped:
        return '宅配已出貨';
      case OrderTagName.sevenElevenDelivering:
        return '7-11 已到大智通';
      case OrderTagName.familyMartDelivering:
        return '全家已到DC';
      case OrderTagName.homeDelivering:
        return '宅配已出貨';
      case OrderTagName.sevenElevenArrived:
        return '7-11商品送達門市';
      case OrderTagName.familyMartArrived:
        return '全家商品送達門市';
      case OrderTagName.homeArrived:
        return '宅配已送達';
    }
  }
  String get value => display;
}
