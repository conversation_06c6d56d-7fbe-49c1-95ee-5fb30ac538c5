import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:talker_flutter/talker_flutter.dart';

class CrashlyticsTalkerObserver extends TalkerObserver {
  @override
  void onLog(TalkerData log) {
    if (Firebase.apps.isEmpty == false) {
      FirebaseCrashlytics.instance.log(log.generateTextMessage());
    }
  }

  @override
  void onError(err) {
    FirebaseCrashlytics.instance.recordError(
      err.error,
      err.stackTrace,
      reason: err.message,
    );
    // FirebaseCrashlytics.instance.recordFlutterError(FlutterErrorDetails(
    //   exception: err.error ?? Exception(err.message),
    //   stack: err.stackTrace,
    //   library: 'talker_flutter',
    //   context: DiagnosticsNode.message(err.message ?? 'No message'),
    // ));
  }

  @override
  void onException(err) {
    FirebaseCrashlytics.instance.recordError(
      err.exception,
      err.stackTrace,
      reason: err.message,
      fatal: true,
    );
    // FirebaseCrashlytics.instance.recordFlutterFatalError(FlutterErrorDetails(
    //   exception: err.exception ?? Exception(err.message),
    //   stack: err.stackTrace,
    //   library: 'talker_flutter',
    //   context: DiagnosticsNode.message(err.message ?? 'No message'),
    // ));
  }
}
