import 'dart:io';

import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'ef_colors.dart';
import 'enums.dart';

class Constants {
  static const schemeHttps = 'https';
  static const efDomain = 'efshop.com.tw';
  static const officialWebsite = 'www.$efDomain';
  static const efAuthority = 'm.$efDomain';
  static const linePayAuthority = 'web-pay.line.me';
  static const lineMeAuthority = 'line.me';
  static const designWidth = 375.0;
  static const designHeight = 812.0;
  static const designWidthRatio = 100.0 / Constants.designWidth;
  static const designHeightRatio = 100.0 / Constants.designHeight;
  static const gridSpacing = 8.0;
  static const borderRadius = BorderRadius.all(Radius.circular(8.0));
  static const tabHeight = 46.0;
  // static final borderRadius = BorderRadius.circular(8.0);
  static final chipPadding = EdgeInsets.symmetric(
    vertical: 2.0.dh,
    horizontal: 3.0.dw,
  );
  static const sizer = SwitchStatus.off;
  static const widgetHeight = 36.0;
  static const facebookIdSandbox = '522314041213398'; // sandbox
  static const facebookIdProduction = '2243741228996320'; // production
  static const facebookId = facebookIdProduction;
  static const facebookScheme = 'fb$facebookId';
  // static const facebookRedirectUri = '$facebookScheme://authorize';
  static final facebookRedirectUri =
      Uri(scheme: facebookScheme, host: 'authorize');
  static const lineClientIdSandbox = '2000103481';
  static const lineClientIdProduction = '1493494037';
  static const lineClientId = lineClientIdProduction;
  // static const lineRedirectUri = 'https://www.efshop.com.tw/';
  // static const lineClientSecret = '';
  static const fcmSenderId = '498175649962';
  static const appGroup =
      'group.com.shangching.com.shangching.efshop.notification';
  static const appierSandbox = 'd021cfac392ead2d2f1c';
  static const appierProduction = 'd7c31d6283d3ccc28bc8';
  static const appierWebChatBotPageId = 'page-3aa88108fbb24ad2a0b259b4';
  static const appierApiToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzY29wZSI6ImFsbCIsImJvdElkIjoiYm90LW0xSVpGSGoxdCIsImlhdCI6MTY1Nzc4NjA1MiwiaXNzIjoiYm90Ym9ubmllX2NvbnNvbGUifQ.qHGXzebARcBCxDcQdlrwXUKPfgpaVZQkQUHQlpHsKC0';
  static const welcomeModuleId = 'module-s904ELONn';
  // https://m.efshop.com.tw/app_access?redirect=content/6/display
  // https://m.efshop.com.tw/mobile-app/transformation?redirect=/content/6/display
  // https://m.efshop.com.tw/mobile-app/transformation?redirect=/content/15/display
  // 會員條款
  static final uriPrivacy =
      Uri.https(efAuthority, '/mobile-app/transformation', {
    'redirect': '/content/6/display',
  });
  static final uriReward = Uri.https(efAuthority, '/app_access', {
    'redirect': 'my_bonus',
  });
  static final uriRevoke = Uri.https('docs.google.com',
      '/forms/d/e/1FAIpQLSeq5UDl2tl67k4Y2sEXvagxqU9ed7araKmQgZ0Nb8xP7RvCTg/viewform');
  static final uriCart = Uri.https(efAuthority, '/app_access', {
    'redirect': 'cart',
    'client_agent_source': Platform.operatingSystem,
    'client_agent_source_os_version': Platform.operatingSystemVersion,
    'client_agent_source_app_version': Get.find<PackageInfo>().version,
  });
  // https://m.efshop.com.tw/content/1116/display
  static final uriRefund = Uri.https(efAuthority, '/content/1116/display');
  // https://m.efshop.com.tw/cart
  static final uriCartRoot = Uri.https(efAuthority, 'cart');
  static final uriWebChat = Uri.https('chat.botbonnie.com', '', {
    'standalone': 'true',
    'appId': appierWebChatBotPageId,
  });
  // https://play.google.com/store/apps/details?id=com.efshop
  static final _uriPlayStore =
      Uri.https('play.google.com', '/store/apps/details', {
    // 'id': Get.find<PackageInfo>().packageName,
    'id': 'com.efshop',
  });
  // https://itunes.apple.com/tw/app/id1156791229
  static final _uriAppStore =
      Uri.https('itunes.apple.com', '/tw/app/id1156791229');
  static Uri get uriStore {
    if (Platform.isAndroid) {
      return _uriPlayStore;
    } else {
      return _uriAppStore;
    }
  }

  // https://efshop.tw/app
  static final shareApp = Uri.https('efshop.tw', '/app');
  // https://bb-webchat-api.appier.com/api/v2/server-api/auth-code
  static final uriBotBonnieApi = Uri.https(
      'bb-webchat-api.appier.com', '/api/v2/server-api/auth-code');
  static const countPerPage = 20;
  // static const authorizeIndexTab = [
  //   IndexTab.profile,
  //   IndexTab.cart,
  // ];
  static const discountProduct = [
    '4000', // 優惠折扣
    '4002', // 紅利點數
    '4003', // 紅利點數
    '6000', // 紅利點數
    '26174', // 迎新好禮✿滿2688✿送手機掛繩組
    // '26174', // 仲夏好禮✿滿1200送隱形襪
  ];
  static const maxOfDiscountProductId = 10000;
  static const carriers = [
    CarrierType.mobile,
    CarrierType.natural,
    CarrierType.easycard,
    CarrierType.member,
  ];
  static final orderStatusFailed = [
    OrderStatusRaw.cancelled.value, // 取消
    '付款失敗',
    OrderStatusRaw.creditCardFailed.value, // 信用卡未過
    // OrderStatusRaw.returned.value, // 退貨
  ];
  static final orderStatusCancelled = [
    OrderStatusRaw.cancelled.value, // 取消
  ];
  static const availablePath = [
    Routes.PRODUCT,
    Routes.SHOW_PRODUCT,
    Routes.FAVORITE,
    Routes.COUPON,
    Routes.MEMBER_VOUCHER_LIST,
    Routes.MY_BONUS,
    Routes.PROMOTION,
    Routes.CATEGORY,
    Routes.CONTENT,
    Routes.SEARCH,
    Routes.HOME,
    Routes.CATEGORY_TOP,
    Routes.PROFILE,
    Routes.CART,
    Routes.EF_WEB,
  ];
  static const buttonHeight = 44.0;
  static const buttonFontSize = 18.0;
  // static final buttonBorderRadius = BorderRadius.circular(4.0);
  static const buttonBorderRadius = BorderRadius.all(Radius.circular(4.0));
  // 需要登入的才能看到的訊息類別
  static final authenticatedPath = [
    MessageType.orders.path,
    MessageType.ships.path,
    MessageType.questions.path,
    MessageType.coupon.path,
  ];

  // 顯示物流詳情按鈕的訂單狀態
  static final shippingButton = [
    OrderStatusRaw.pickedUp.value, // 已取貨
    OrderStatusRaw.processing.value, // 處理中
    OrderStatusRaw.pendingShipment.value, // 待出貨
    OrderStatusRaw.shipped.value, // 已出貨
    OrderStatusRaw.closed.value, // 結案
    OrderStatusRaw.awaitingRating.value, // 待評價
    OrderStatusRaw.returned.value, // 退貨
    OrderStatusRaw.awaitingSignature.value, // 待簽收
    OrderStatusRaw.arrivedAtStore.value, // 已到店
  ];

  // 顯示退貨按鈕的訂單狀態
  // static final refundButton = [
  //   OrderStatusRaw.returned.value, // 退貨
  // ];

  // 顯示發票按鈕的訂單狀態
  static final invoiceButton = [
    OrderStatusRaw.pickedUp.value, // 已取貨
    OrderStatusRaw.closed.value, // 結案
    OrderStatusRaw.awaitingRating.value, // 待評價
  ];

  // 顯示評價商品按鈕的訂單狀態
  static final ratingButton = [
    OrderStatusRaw.pickedUp.value, // 已取貨
    // OrderStatusRaw.shipped.value, // 已出貨
    OrderStatusRaw.closed.value, // 結案
    OrderStatusRaw.awaitingRating.value, // 待評價
    OrderStatusRaw.returned.value, // 退貨
    OrderStatusRaw.awaitingSignature.value // 待簽收
  ];

  // Android production bundle id
  static const androidBundleId = 'com.efshop';
  // iOS production bundle id
  static const iosBundleId = 'com.shangching.com.shangching.efshop';
  // Android sandbox bundle id
  static const androidBundleIdSandbox = 'com.efshop.app.efshopandroid';
  // iOS sandbox bundle id
  static const iosBundleIdSandbox = 'tw.com.efshop.efapptest';
  static DeviceType get deviceType {
    if (GetPlatform.isAndroid) {
      return DeviceType.android;
    }
    if (GetPlatform.isIOS) {
      return DeviceType.ios;
    }
    return DeviceType.undefined;
  }

  static const sizeOrder = {
    "F": 0,
    "XS": 1,
    "S": 2,
    "M": 3,
    "L": 4,
    "XL": 5,
    "XXL": 6,
    "3XL": 7,
    "4XL": 8,
    "5XL": 9,
    "加大": 10
  };

  static const domains = [
    'www.$efDomain',
    'm.$efDomain',
  ];
  // 客服專線
  static const servicePhone = '客服專線：(02)2602-0707';
  // 客服時段
  static const serviceTime = '客服時段：09:00~17:30(週六日及國定假日除外)';
  // 可以顯示 bottom bar 的 tab
  static const visibleIndexTab = [
    IndexTab.home,
    IndexTab.category,
    IndexTab.service,
    IndexTab.profile,
  ];
  static const testUrl = 'https://m.efshop.com.tw/showEmptyCart';
  // static const testUrl = 'https://m.efshop.com.tw/app/CartFinish/fail/7422211?openExternalBrowser=1';
  // static const testUrl = 'https://m.efshop.com.tw/app/CartFinish/success/7422211?openExternalBrowser=1';
  static const notificationData = <String, dynamic>{
    "sound": "1",
    "icon": "myicon",
    "body": "抗UV吸排抗菌運動衣褲！120元起",
    "title": "抗UV吸排抗菌運動衣褲！120元起",
    "parameters":
        "{\"partner_id\":\"home\",\"action\":\"category\",\"id\":\"457\",\"type\":\"activities\"}"
  };
  static final themeData = ThemeData(
    colorScheme: const ColorScheme.light(
      surfaceTint: Colors.transparent,
      primary: EfColors.primary,
    ),
    actionIconTheme: ActionIconThemeData(
      backButtonIconBuilder: (context) => const Icon(
        Icons.arrow_back_ios,
        color: EfColors.gray,
      ),
    ),
    listTileTheme: const ListTileThemeData(
      titleAlignment: ListTileTitleAlignment.center,
      iconColor: EfColors.grayText,
      textColor: EfColors.grayText,
      tileColor: Colors.white,
      leadingAndTrailingTextStyle: TextStyle(
        fontSize: 15,
      ),
      titleTextStyle: TextStyle(
        fontSize: 15,
      ),
      contentPadding: EdgeInsets.symmetric(
        horizontal: 16,
        // vertical: 8,
      ),
    ),
    // textTheme: const TextTheme(
    //   displayLarge: TextStyle(
    //     color: EfColors.primary,
    //   ),
    //   displayMedium: TextStyle(
    //     color: EfColors.primary,
    //   ),
    //   displaySmall: TextStyle(
    //     color: EfColors.primary,
    //   ),
    //   headlineLarge: TextStyle(
    //     color: EfColors.primary,
    //   ),
    //   headlineMedium: TextStyle(
    //     color: EfColors.primary,
    //   ),
    //   headlineSmall: TextStyle(
    //     color: EfColors.primary,
    //   ),
    //   titleLarge: TextStyle(
    //     color: EfColors.primary,
    //     // fontSize: 20.0,
    //   ),
    //   titleMedium: TextStyle(
    //     color: EfColors.primary,
    //     // fontSize: 20.0,
    //   ),
    //   titleSmall: TextStyle(
    //     color: EfColors.primary,
    //     // fontSize: 20.0,
    //   ),
    //   bodyLarge: TextStyle(
    //     color: EfColors.primary,
    //   ),
    //   bodyMedium: TextStyle(
    //     color: EfColors.primary,
    //   ),
    //   bodySmall: TextStyle(
    //     color: EfColors.primary,
    //   ),
    //   labelLarge: TextStyle(
    //     color: EfColors.primary,
    //   ),
    //   labelMedium: TextStyle(
    //     color: EfColors.primary,
    //   ),
    //   labelSmall: TextStyle(
    //     color: EfColors.primary,
    //   ),
    // ),
    // buttonTheme: const ButtonThemeData(
    //   buttonColor: EfColors.primary,
    //   textTheme: ButtonTextTheme.primary,
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.all(
    //       Radius.circular(4),
    //     ),
    //   ),
    // ),
    badgeTheme: const BadgeThemeData(
        backgroundColor: EfColors.primary, offset: Offset(4, 4)),
    checkboxTheme: const CheckboxThemeData(
      shape: CircleBorder(),
      side: BorderSide(
        color: EfColors.grayLight,
        width: 1.0,
      ),
      // fillColor: MaterialStateProperty.all(EfColors.primary),
      // checkColor: MaterialStateProperty.all(Colors.white),
      // shape: RoundedRectangleBorder(
      //   borderRadius: BorderRadius.circular(4.dsp),
      // ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        elevation: 0,
        padding: EdgeInsets.symmetric(
          vertical: 2.dh,
          horizontal: 8.dw,
        ),
        disabledBackgroundColor: EfColors.disabled,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(2),
          ),
        ),
        side: const BorderSide(
          color: EfColors.grayE0,
          width: 1,
        ),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: EfColors.primary,
        elevation: 0,
        padding: EdgeInsets.zero,
        disabledBackgroundColor: EfColors.disabled,
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        padding: EdgeInsets.zero,
        disabledBackgroundColor: EfColors.disabled,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
      ),
    ),
    dividerTheme: const DividerThemeData(
      color: EfColors.divider,
      thickness: 1,
    ),
    dividerColor: EfColors.divider,
    indicatorColor: EfColors.primary,
    primarySwatch: Colors.red,
    progressIndicatorTheme: const ProgressIndicatorThemeData(
      color: EfColors.primary,
    ),
    tabBarTheme: const TabBarTheme(
      indicatorSize: TabBarIndicatorSize.tab,
      // 隱藏指示器
      // indicator: BoxDecoration(),
      dividerHeight: 0,
      tabAlignment: TabAlignment.start,
      labelColor: EfColors.primary,
      unselectedLabelColor: EfColors.gray,
      labelStyle: TextStyle(
        fontSize: 16,
        // color: EfColors.primary,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: 16,
      ),
      indicatorColor: EfColors.primary,
    ),
    scaffoldBackgroundColor: EfColors.grayF6,
    appBarTheme: AppBarTheme(
      color: Colors.white,
      elevation: 1,
      shadowColor: EfColors.grayF6,
      iconTheme: const IconThemeData(
        color: EfColors.gray,
      ),
      titleTextStyle: TextStyle(
        color: EfColors.gray,
        fontSize: 18.dsp,
        fontWeight: FontWeight.w500,
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      contentPadding: EdgeInsets.symmetric(
        // vertical: 12.dh,
        horizontal: 16.dw,
      ),
      border: const OutlineInputBorder(
        borderRadius: Constants.buttonBorderRadius,
        borderSide: BorderSide(
          color: EfColors.border,
          // width: 1,
        ),
      ),
      enabledBorder: const OutlineInputBorder(
        borderRadius: Constants.buttonBorderRadius,
        borderSide: BorderSide(
          color: EfColors.border,
          width: 1,
        ),
      ),
      focusedBorder: const OutlineInputBorder(
        borderRadius: Constants.buttonBorderRadius,
        borderSide: BorderSide(
          color: EfColors.primary,
          // width: 1,
        ),
      ),
      labelStyle: TextStyle(
        color: EfColors.black,
        fontSize: 14.dsp,
      ),
      hintStyle: TextStyle(
        color: EfColors.grayLight,
        fontSize: 14.dsp,
      ),
    ),
  );
}
