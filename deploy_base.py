import subprocess
import time


def execute(command):
    print("======= start build =======")
    start = time.time()
    command_run = subprocess.Popen(command, shell=True)
    command_run.wait()
    end = time.time()
    result_code = command_run.returncode
    if result_code != 0:
        print("======= build failed, take: %.2f sec =======" % (end - start))
    else:
        print("======= build success, take: %.2f sec =======" % (end - start))
    return result_code


def flutter_clean():
    execute("flutter clean")
